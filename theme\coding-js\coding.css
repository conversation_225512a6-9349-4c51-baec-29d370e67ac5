.output_displayed_text {
  color: blue;
}

.output_error {
  color: red;
}

.p-link {
  text-decoration: underline;
  text-align: right;
}

.p-link:hover {
  cursor:pointer;
}

.output {
  font-family: monospace;
  font-style: italic;
  padding: 0pt 4pt;
  color: #444;
}

/*autoscroll*/
.CodeMirror-scroll {
  height: auto;
  width: auto;
  overflow-y: hidden;
  overflow-x: auto;
}

/*identifying editable portions*/

.CodeMirror-scroll:hover:not(.static):not(.CodeMirror-focused) {
  background: rgba(0,0,0,0.05);
}

.CodeMirror-scroll:hover.static:not(.CodeMirror-focused) {
  background-color: rgba(0,0,0,0.01);
}

.CodeMirror-focused:not(:hover) {
  background: rgba(0,0,0,0.07);
}

.CodeMirror-focused:hover {
  background: rgba(0,0,0,0.075);
}

/**/

.CodeMirror-matchingbracket {
  background: rgba(0,0,0,0.1);
}

/**/

div.CodeMirror span.CodeMirror-matchingbracket {
    color: #5f5f5f; /*#869CEB; /*why can't we use .CodeMirror-matchingbracket? */
}

.CodeMirror {
  line-height: 1.3em;
}
