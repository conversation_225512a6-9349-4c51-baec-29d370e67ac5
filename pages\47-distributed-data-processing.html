<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from www.composingprograms.com/pages/47-distributed-data-processing.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:45:18 GMT -->
<head>
  <title>4.7 Distributed Data Processing</title>
  <meta charset="utf-8" />

  <link rel="stylesheet" type="text/css" href="../theme/css/cp.css" />

  <!-- Stylesheets -->
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/pytutor.css"/>
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/ui-lightness/jquery-ui-1.8.21.custom.css" />
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/codemirror.css"  />
  <link rel="stylesheet" type="text/css" href="../theme/coding-js/coding.css"  />

  <!-- jQuery -->
  <script type="text/javascript" src="../theme/tutor/js/jquery-1.8.2.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery-ui-1.8.24.custom.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery.ba-bbq.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery.jsPlumb-1.3.10-all-min.js"></script>

  <!-- codemirror.net online code editor -->
  <script type="text/javascript" src="../theme/tutor/js/codemirror/codemirror.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/codemirror/python.js"></script>

  <!-- d3 -->
  <script type="text/javascript" src="../theme/tutor/js/d3.v2.min.js"></script>

  <!-- Online Python Tutor -->
  <script type="text/javascript" src="../theme/tutor/js/pytutor.js"></script>

  <!-- Coding.js -->
  <script type="text/javascript" src="../theme/coding-js/coding.js"></script>
  <script> var $c = new CodingJS("../theme/coding-js/index.html"); </script>

  <!-- Composing Programs -->
  <script type="text/javascript" src="../theme/js/cp.js"></script>

  
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.1/MathJax.js?config=TeX-AMS-MML_HTMLorMML" type= "text/javascript">
       MathJax.Hub.Config({
           config: ["MMLorHTML.js"],
           jax: ["input/TeX","input/MathML","output/HTML-CSS","output/NativeMML"],
           TeX: { extensions: ["AMSmath.js","AMSsymbols.html","noErrors.html","noUndefined.js"], equationNumbers: { autoNumber: "AMS" } },
           extensions: ["tex2jax.js","mml2jax.html","MathMenu.html","MathZoom.html","jsMath2jax.js"],
           tex2jax: {
               inlineMath: [ ['$','$'] ],
               displayMath: [ ['$$','$$'] ],
               processEscapes: true },
           "HTML-CSS": {
               styles: { ".MathJax .mo, .MathJax .mi": {color: "black ! important"}}
           }
       });
    </script>

</head>

<body id="index" class="home">
  <div class="container">

    <div class="nav-main">
      <div class="wrap">
        <a class="nav-home" href="../index.html">
          <span class="nav-logo">c<span class="nav-logo-compose">⚬</span>mp<span class="nav-logo-compose">⚬</span>sing pr<span class="nav-logo-compose">⚬</span>grams</span>
        </a>
        <ul class="nav-site">
          <li><a href="../index.html">Text</a></li>
          <li><a href="../projects.html">Projects</a></li>
          <li><a href="../tutor.html">Tutor</a></li>
          <li><a href="../about.html">About</a></li>
        </ul>
      </div>
    </div>

    <section class="content wrap documentationContent">
      <div class="nav-docs">
	<h3>Chapter 4<a id="hide_contents">Hide contents</a> </h3>
		<div class="nav-docs-section">
			<h3><a href="41-introduction.html">4.1 Introduction</a></h3>
		</div>
		<div class="nav-docs-section">
			<h3><a href="42-implicit-sequences.html">4.2 Implicit Sequences</a></h3>
				<li><a href="42-implicit-sequences.html#iterators">4.2.1 Iterators</a>
				<li><a href="42-implicit-sequences.html#iterables">4.2.2 Iterables</a>
				<li><a href="42-implicit-sequences.html#built-in-iterators">4.2.3 Built-in Iterators</a>
				<li><a href="42-implicit-sequences.html#for-statements">4.2.4 For Statements</a>
				<li><a href="42-implicit-sequences.html#generators-and-yield-statements">4.2.5 Generators and Yield Statements</a>
				<li><a href="42-implicit-sequences.html#iterable-interface">4.2.6 Iterable Interface</a>
				<li><a href="42-implicit-sequences.html#creating-iterables-with-yield">4.2.7 Creating Iterables with Yield</a>
				<li><a href="42-implicit-sequences.html#iterator-interface">4.2.8 Iterator Interface</a>
				<li><a href="42-implicit-sequences.html#streams">4.2.9 Streams</a>
				<li><a href="42-implicit-sequences.html#python-streams">4.2.10 Python Streams</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="43-declarative-programming.html">4.3 Declarative Programming</a></h3>
				<li><a href="43-declarative-programming.html#tables">4.3.1 Tables</a>
				<li><a href="43-declarative-programming.html#select-statements">4.3.2 Select Statements</a>
				<li><a href="43-declarative-programming.html#joins">4.3.3 Joins</a>
				<li><a href="43-declarative-programming.html#interpreting-sql">4.3.4 Interpreting SQL</a>
				<li><a href="43-declarative-programming.html#recursive-select-statements">4.3.5 Recursive Select Statements</a>
				<li><a href="43-declarative-programming.html#aggregation-and-grouping">4.3.6 Aggregation and Grouping</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="44-logic-programming.html">4.4 Logic Programming</a></h3>
				<li><a href="44-logic-programming.html#facts-and-queries">4.4.1 Facts and Queries</a>
				<li><a href="44-logic-programming.html#recursive-facts">4.4.2 Recursive Facts</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="45-unification.html">4.5 Unification</a></h3>
				<li><a href="45-unification.html#pattern-matching">4.5.1 Pattern Matching</a>
				<li><a href="45-unification.html#representing-facts-and-queries">4.5.2 Representing Facts and Queries</a>
				<li><a href="45-unification.html#the-unification-algorithm">4.5.3 The Unification Algorithm</a>
				<li><a href="45-unification.html#proofs">4.5.4 Proofs</a>
				<li><a href="45-unification.html#search">4.5.5 Search</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="46-distributed-computing.html">4.6 Distributed Computing</a></h3>
				<li><a href="46-distributed-computing.html#messages">4.6.1 Messages</a>
				<li><a href="46-distributed-computing.html#client-server-architecture">4.6.2 Client/Server Architecture</a>
				<li><a href="46-distributed-computing.html#peer-to-peer-systems">4.6.3 Peer-to-Peer Systems</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="47-distributed-data-processing.html">4.7 Distributed Data Processing</a></h3>
				<li><a href="47-distributed-data-processing.html#id1">4.7.1 MapReduce</a>
				<li><a href="47-distributed-data-processing.html#local-implementation">4.7.2 Local Implementation</a>
				<li><a href="47-distributed-data-processing.html#distributed-implementation">4.7.3 Distributed Implementation</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="48-parallel-computing.html">4.8 Parallel Computing</a></h3>
				<li><a href="48-parallel-computing.html#parallelism-in-python">4.8.1 Parallelism in Python</a>
				<li><a href="48-parallel-computing.html#the-problem-with-shared-state">4.8.2 The Problem with Shared State</a>
				<li><a href="48-parallel-computing.html#when-no-synchronization-is-necessary">4.8.3 When No Synchronization is Necessary</a>
				<li><a href="48-parallel-computing.html#synchronized-data-structures">4.8.4 Synchronized Data Structures</a>
				<li><a href="48-parallel-computing.html#locks">4.8.5 Locks</a>
				<li><a href="48-parallel-computing.html#barriers">4.8.6 Barriers</a>
				<li><a href="48-parallel-computing.html#message-passing">4.8.7 Message Passing</a>
				<li><a href="48-parallel-computing.html#synchronization-pitfalls">4.8.8 Synchronization Pitfalls</a>
				<li><a href="48-parallel-computing.html#conclusion">4.8.9 Conclusion</a>
		</div>
      </div>

      <div class="inner-content">
  <div class="section" id="distributed-data-processing">
<h2>4.7   Distributed Data Processing</h2>
<p>Distributed systems are often used to collect, access, and manipulate large data
sets. For example, the database systems described earlier in the chapter can
operate over datasets that are stored across multiple machines. No single
machine may contain the data necessary to respond to a query, and so
communication is required to service requests.</p>
<p>This section investigates a typical big data processing scenario in which a
data set too large to be processed by a single machine is instead distributed
among many machines, each of which process a portion of the dataset. The result
of processing must often be aggregated across machines, so that results from
one machine's computation can be combined with others. To coordinate this
distributed data processing, we will discuss a programming framework called
<a class="reference external" href="http://en.wikipedia.org/wiki/MapReduce">MapReduce</a>.</p>
<p>Creating a distributed data processing application with MapReduce combines
many of the ideas presented throughout this text. An application is expressed
in terms of pure functions that are used to <em>map</em> over a large dataset and then
to <em>reduce</em> the mapped sequences of values into a final result.</p>
<p>Familiar concepts from functional programming are used to maximal advantage in
a MapReduce program.  MapReduce requires that the functions used to map and
reduce the data be pure functions.  In general, a program expressed only in
terms of pure functions has considerable flexibility in how it is executed.
Sub-expressions can be computed in arbitrary order and in parallel without
affecting the final result.  A MapReduce application evaluates many pure
functions in parallel, reordering computations to be executed efficiently in a
distributed system.</p>
<p>The principal advantage of MapReduce is that it enforces a separation of
concerns between two parts of a distributed data processing application:</p>
<ol class="arabic simple">
<li>The map and reduce functions that process data and combine results.</li>
<li>The communication and coordination between machines.</li>
</ol>
<p>The coordination mechanism handles many issues that arise in distributed
computing, such as machine failures, network failures, and progress monitoring.
While managing these issues introduces some complexity in a MapReduce
application, none of that complexity is exposed to the application developer.
Instead, building a MapReduce application only requires specifying the
map and reduce functions in (1) above; the challenges of distributed
computation are hidden via abstraction.</p>
<div class="section" id="id1">
<h3>4.7.1   MapReduce</h3>
<p>The MapReduce framework assumes as input a large, unordered stream of
input values of an arbitrary type. For instance, each input may be a line of
text in some vast corpus. Computation proceeds in three steps.</p>
<ol class="arabic simple">
<li>A map function is applied to each input, which outputs zero or more
intermediate key-value pairs of an arbitrary type.</li>
<li>All intermediate key-value pairs are grouped by key, so that pairs with the
same key can be reduced together.</li>
<li>A reduce function combines the values for a given key <tt class="docutils literal">k</tt>; it outputs zero
or more values, which are each associated with <tt class="docutils literal">k</tt> in the final output.</li>
</ol>
<p>To perform this computation, the MapReduce framework creates tasks (perhaps on
different machines) that perform various roles in the computation.  A <em>map
task</em> applies the map function to some subset of the input data and outputs
intermediate key-value pairs.  A <em>reduce</em> task sorts and groups key-value pairs
by key, then applies the reduce function to the values for each key.
All communication between map and reduce tasks is handled by the framework, as
is the task of grouping intermediate key-value pairs by key.</p>
<p>In order to utilize multiple machines in a MapReduce application, multiple
mappers run in parallel in a <em>map phase</em>, and multiple reducers run in parallel
in a <em>reduce phase</em>. In between these phases, the <em>sort phase</em> groups together
key-value pairs by sorting them, so that all key-value pairs with the same key
are adjacent.</p>
<p>Consider the problem of counting the vowels in a corpus of text. We can solve
this problem using the MapReduce framework with an appropriate choice of map
and reduce functions.  The map function takes as input a line of text and
outputs key-value pairs in which the key is a vowel and the value is a count.
Zero counts are omitted from the output:</p>
<pre class="literal-block">
def count_vowels(line):
    """A map function that counts the vowels in a line."""
    for vowel in 'aeiou':
        count = line.count(vowel)
        if count &gt; 0:
            emit(vowel, count)
</pre>
<p>The reduce function is the built-in sum functions in Python, which takes as
input an iterator over values (all values for a given key) and returns their
sum.</p>
</div>
<div class="section" id="local-implementation">
<h3>4.7.2   Local Implementation</h3>
<p>To specify a MapReduce application, we require an implementation of the
MapReduce framework into which we can insert map and reduce functions. In the
following section, we will use the open-source <a class="reference external" href="http://en.wikipedia.org/wiki/Hadoop">Hadoop</a> implementation. In this
section, we develop a minimal implementation using built-in tools of the Unix
operating system.</p>
<p>The Unix operating system creates an abstraction barrier between user programs
and the underlying hardware of a computer.  It provides a mechanism for
programs to communicate with each other, in particular by allowing one program
to consume the output of another. In their seminal text on Unix programming,
Kernigham and Pike assert that, ""The power of a system comes more from the
relationships among programs than from the programs themselves."</p>
<p>A Python source file can be converted into a Unix program by adding a comment
to the first line indicating that the program should be executed using the
Python 3 interpreter. The input to a Unix program is an iterable object called
<em>standard input</em> and accessed as <tt class="docutils literal">sys.stdin</tt>. Iterating over this object
yields string-valued lines of text. The output of a Unix program is called
<em>standard output</em> and accessed as <tt class="docutils literal">sys.stdout</tt>. The built-in <tt class="docutils literal">print</tt>
function writes a line of text to standard output.  The following Unix program
writes each line of its input to its output, in reverse:</p>
<pre class="literal-block">
#!/usr/bin/env python3

import sys

for line in sys.stdin:
    print(line.strip('\n')[::-1])
</pre>
<p>If we save this program to a file called <tt class="docutils literal">rev.py</tt>, we can execute it as a
Unix program. First, we need to tell the operating system that we have created
an executable program:</p>
<pre class="literal-block">
$ chmod u+x rev.py
</pre>
<p>Next, we can pass input into this program.  Input to a program can come from
another program.  This effect is achieved using the <tt class="docutils literal">|</tt> symbol (called
"pipe") which channels the output of the program before the pipe into the
program after the pipe.  The program <tt class="docutils literal">nslookup</tt> outputs the host name of an
IP address (in this case for the New York Times):</p>
<pre class="literal-block">
$ nslookup *************** | ./rev.py
moc.semityn.www
</pre>
<p>The <tt class="docutils literal">cat</tt> program outputs the contents of files.  Thus, the <tt class="docutils literal">rev.py</tt>
program can be used to reverse the contents of the <tt class="docutils literal">rev.py</tt> file:</p>
<pre class="literal-block">
$ cat rev.py | ./rev.py
3nohtyp vne/nib/rsu/!#

sys tropmi

:nidts.sys ni enil rof
)]1-::[)'n\'(pirts.enil(tnirp
</pre>
<p>These tools are enough for us to implement a basic MapReduce framework.  This
version has only a single map task and single reduce task, which are both Unix
programs implemented in Python. We run an entire MapReduce application using
the following command:</p>
<pre class="literal-block">
$ cat input | ./mapper.py | sort | ./reducer.py
</pre>
<p>The <tt class="docutils literal">mapper.py</tt> and <tt class="docutils literal">reducer.py</tt> programs must implement the map function
and reduce function, along with some simple input and output behavior. For
instance, in order to implement the vowel counting application described above,
we would write the following <tt class="docutils literal">count_vowels_mapper.py</tt> program:</p>
<pre class="literal-block">
#!/usr/bin/env python3

import sys
from mr import emit

def count_vowels(line):
    """A map function that counts the vowels in a line."""
    for vowel in 'aeiou':
        count = line.count(vowel)
        if count &gt; 0:
            emit(vowel, count)

for line in sys.stdin:
    count_vowels(line)
</pre>
<p>In addition, we would write the following <tt class="docutils literal">sum_reducer.py</tt> program:</p>
<pre class="literal-block">
#!/usr/bin/env python3

import sys
from mr import values_by_key, emit

for key, value_iterator in values_by_key(sys.stdin):
    emit(key, sum(value_iterator))
</pre>
<p>The <a class="reference external" href="../examples/mapreduce/mr.py">mr module</a> is a companion
module to this text that provides the functions <tt class="docutils literal">emit</tt> to emit a key-value
pair and <tt class="docutils literal">group_values_by_key</tt> to group together values that have the same
key. This module also includes an interface to the Hadoop distributed
implementation of MapReduce.</p>
<p>Finally, assume that we have the following input file called <tt class="docutils literal">haiku.txt</tt>:</p>
<pre class="literal-block">
Google MapReduce
Is a Big Data framework
For batch processing
</pre>
<p>Local execution using Unix pipes gives us the count of each vowel in the
haiku:</p>
<pre class="literal-block">
$ cat haiku.txt | ./count_vowels_mapper.py | sort | ./sum_reducer.py
'a'   6
'e'   5
'i'   2
'o'   5
'u'   1
</pre>
</div>
<div class="section" id="distributed-implementation">
<h3>4.7.3   Distributed Implementation</h3>
<p><a class="reference external" href="http://en.wikipedia.org/wiki/Hadoop">Hadoop</a> is the name of an open-source implementation of the MapReduce framework
that executes MapReduce applications on a cluster of machines, distributing
input data and computation for efficient parallel processing. Its streaming
interface allows arbitrary Unix programs to define the map and reduce
functions. In fact, our <tt class="docutils literal">count_vowels_mapper.py</tt> and <tt class="docutils literal">sum_reducer.py</tt> can
be used directly with a Hadoop installation to compute vowel counts on large
text corpora.</p>
<p>Hadoop offers several advantages over our simplistic local MapReduce
implementation. The first is speed: map and reduce functions are applied in
parallel using different tasks on different machines running simultaneously.
The second is fault tolerance: when a task fails for any reason, its result can
be recomputed by another task in order to complete the overall computation.
The third is monitoring: the framework provides a user interface for tracking
the progress of a MapReduce application.</p>
<p>In order to run the vowel counting application using the provided
<tt class="docutils literal">mapreduce.py</tt> module, install Hadoop, change the assignment statement of
<tt class="docutils literal">HADOOP</tt> to the root of your local installation, copy a collection of text
files into the Hadoop distributed file system, and then run:</p>
<pre class="literal-block">
$ python3 mr.py run count_vowels_mapper.py sum_reducer.py [input] [output]
</pre>
<p>where <tt class="docutils literal">[input]</tt> and <tt class="docutils literal">[output]</tt> are directories in the Hadoop file system.</p>
<p>For more information on the Hadoop streaming interface and use of the system,
consult the <a class="reference external" href="http://hadoop.apache.org/docs/stable/streaming.html">Hadoop Streaming Documentation</a>.</p>
</div>
</div>
  <p><i>Continue</i>:
  	<a href="48-parallel-computing.html">
  		4.8 Parallel Computing
  	</a>
      </div>
    </section>

    <div class="wrap">
      <footer id="contentinfo" class="body">
          Composing Programs by <a href="http://www.denero.org/">John
          DeNero</a>, based on the textbook <a
          href="http://mitpress.mit.edu/sicp/">Structure and
          Interpretation of Computer Programs</a> by Harold Abelson and
          Gerald Jay Sussman, is licensed under a <a rel="license"
          href="http://creativecommons.org/licenses/by-sa/3.0/">Creative
          Commons Attribution-ShareAlike 3.0 Unported License</a>.
      </footer><!-- /#contentinfo -->
    </div>
  </div>
</body>

<!-- Mirrored from www.composingprograms.com/pages/47-distributed-data-processing.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:45:19 GMT -->
</html>