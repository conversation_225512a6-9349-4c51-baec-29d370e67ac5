<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from www.composingprograms.com/versions/v1/pages/25-object-oriented-programming.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:46:03 GMT -->
<head>
  <title>2.5 Object-Oriented Programming</title>
  <meta charset="utf-8" />

  <link rel="stylesheet" type="text/css" href="../theme/css/cp.css" />

  <!-- Stylesheets -->
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/pytutor.css"/>
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/ui-lightness/jquery-ui-1.8.21.custom.css" />
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/codemirror.css"  />

  <!-- jQuery -->
  <script type="text/javascript" src="../theme/tutor/js/jquery-1.8.2.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery-ui-1.8.24.custom.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery.ba-bbq.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery.jsPlumb-1.3.10-all-min.js"></script>

  <!-- codemirror.net online code editor -->
  <script type="text/javascript" src="../theme/tutor/js/codemirror/codemirror.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/codemirror/python.js"></script>

  <!-- d3 -->
  <script type="text/javascript" src="../theme/tutor/js/d3.v2.min.js"></script>

  <!-- Online Python Tutor -->
  <script type="text/javascript" src="../theme/tutor/js/pytutor.js"></script>
  <script type="text/javascript" src="../theme/js/tutorize.js"></script>


    <script src="http://cdn.mathjax.org/mathjax/latest/MathJax.js?config=TeX-AMS-MML_HTMLorMML" type= "text/javascript">
       MathJax.Hub.Config({
           config: ["MMLorHTML.js"],
           jax: ["input/TeX","input/MathML","output/HTML-CSS","output/NativeMML"],
           TeX: { extensions: ["AMSmath.js","AMSsymbols.html","noErrors.html","noUndefined.js"], equationNumbers: { autoNumber: "AMS" } },
           extensions: ["tex2jax.js","mml2jax.html","MathMenu.html","MathZoom.html","jsMath2jax.js"],
           tex2jax: {
               inlineMath: [ ['$','$'] ],
               displayMath: [ ['$$','$$'] ],
               processEscapes: true },
           "HTML-CSS": {
               styles: { ".MathJax .mo, .MathJax .mi": {color: "black ! important"}}
           }
       });
    </script>

</head>

<body id="index" class="home">
  <div class="container">

    <div class="nav-main">
      <div class="wrap">
        <a class="nav-home" href="../index.html">
          <span class="nav-logo">c<span class="nav-logo-compose">⚬</span>mp<span class="nav-logo-compose">⚬</span>sing pr<span class="nav-logo-compose">⚬</span>grams</span>
        </a>
VERSION 1, Replaced July 2014
      </div>
    </div>

    <section class="content wrap documentationContent">
      <div class="nav-docs">
	<h3>Chapter 2<a id="hide_contents">Hide contents</a> </h3>
		<div class="nav-docs-section">
			<h3><a href="21-introduction.html">2.1 Introduction</a></h3>
				<li><a href="21-introduction.html#the-object-metaphor">2.1.1 The Object Metaphor</a>
				<li><a href="21-introduction.html#native-data-types">2.1.2 Native Data Types</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="22-data-abstraction.html">2.2 Data Abstraction</a></h3>
				<li><a href="22-data-abstraction.html#example-arithmetic-on-rational-numbers">2.2.1 Example: Arithmetic on Rational Numbers</a>
				<li><a href="22-data-abstraction.html#pairs">2.2.2 Pairs</a>
				<li><a href="22-data-abstraction.html#abstraction-barriers">2.2.3 Abstraction Barriers</a>
				<li><a href="22-data-abstraction.html#the-properties-of-data">2.2.4 The Properties of Data</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="23-sequences.html">2.3 Sequences</a></h3>
				<li><a href="23-sequences.html#tuples">2.3.1 Tuples</a>
				<li><a href="23-sequences.html#sequence-iteration">2.3.2 Sequence Iteration</a>
				<li><a href="23-sequences.html#sequence-abstraction">2.3.3 Sequence Abstraction</a>
				<li><a href="23-sequences.html#nested-pairs">2.3.4 Nested Pairs</a>
				<li><a href="23-sequences.html#recursive-lists">2.3.5 Recursive Lists</a>
				<li><a href="23-sequences.html#strings">2.3.6 Strings</a>
				<li><a href="23-sequences.html#sequence-processing">2.3.7 Sequence Processing</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="24-mutable-data.html">2.4 Mutable Data</a></h3>
				<li><a href="24-mutable-data.html#lists">2.4.1 Lists</a>
				<li><a href="24-mutable-data.html#dictionaries">2.4.2 Dictionaries</a>
				<li><a href="24-mutable-data.html#local-state">2.4.3 Local State</a>
				<li><a href="24-mutable-data.html#the-benefits-of-non-local-assignment">2.4.4 The Benefits of Non-Local Assignment</a>
				<li><a href="24-mutable-data.html#the-cost-of-non-local-assignment">2.4.5 The Cost of Non-Local Assignment</a>
				<li><a href="24-mutable-data.html#implementing-lists-and-dictionaries">2.4.6 Implementing Lists and Dictionaries</a>
				<li><a href="24-mutable-data.html#dispatch-dictionaries">2.4.7 Dispatch Dictionaries</a>
				<li><a href="24-mutable-data.html#propagating-constraints">2.4.8 Propagating Constraints</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="25-object-oriented-programming.html">2.5 Object-Oriented Programming</a></h3>
				<li><a href="25-object-oriented-programming.html#objects-and-classes">2.5.1 Objects and Classes</a>
				<li><a href="25-object-oriented-programming.html#defining-classes">2.5.2 Defining Classes</a>
				<li><a href="25-object-oriented-programming.html#message-passing-and-dot-expressions">2.5.3 Message Passing and Dot Expressions</a>
				<li><a href="25-object-oriented-programming.html#class-attributes">2.5.4 Class Attributes</a>
				<li><a href="25-object-oriented-programming.html#inheritance">2.5.5 Inheritance</a>
				<li><a href="25-object-oriented-programming.html#using-inheritance">2.5.6 Using Inheritance</a>
				<li><a href="25-object-oriented-programming.html#multiple-inheritance">2.5.7 Multiple Inheritance</a>
				<li><a href="25-object-oriented-programming.html#functions-as-objects">2.5.8 Functions as Objects</a>
				<li><a href="25-object-oriented-programming.html#the-role-of-objects">2.5.9 The Role of Objects</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="26-implementing-classes-and-objects.html">2.6 Implementing Classes and Objects</a></h3>
				<li><a href="26-implementing-classes-and-objects.html#instances">2.6.1 Instances</a>
				<li><a href="26-implementing-classes-and-objects.html#classes">2.6.2 Classes</a>
				<li><a href="26-implementing-classes-and-objects.html#using-implemented-objects">2.6.3 Using Implemented Objects</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="27-recursive-data-structures.html">2.7 Recursive Data Structures</a></h3>
				<li><a href="27-recursive-data-structures.html#a-recursive-list-class">2.7.1 A Recursive List Class</a>
				<li><a href="27-recursive-data-structures.html#hierarchical-structures">2.7.2 Hierarchical Structures</a>
				<li><a href="27-recursive-data-structures.html#memoization">2.7.3 Memoization</a>
				<li><a href="27-recursive-data-structures.html#orders-of-growth">2.7.4 Orders of Growth</a>
				<li><a href="27-recursive-data-structures.html#example-exponentiation">2.7.5 Example: Exponentiation</a>
				<li><a href="27-recursive-data-structures.html#sets">2.7.6 Sets</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="28-generic-operations.html">2.8 Generic Operations</a></h3>
				<li><a href="28-generic-operations.html#string-conversion">2.8.1 String Conversion</a>
				<li><a href="28-generic-operations.html#multiple-representations">2.8.2 Multiple Representations</a>
				<li><a href="28-generic-operations.html#special-methods">2.8.3 Special Methods</a>
				<li><a href="28-generic-operations.html#generic-functions">2.8.4 Generic Functions</a>
		</div>
      </div>

      <div class="inner-content">
  <div class="section" id="object-oriented-programming">
<h2>2.5   Object-Oriented Programming</h2>
<p>Object-oriented programming (OOP) is a method for organizing programs that
brings together many of the ideas introduced in this chapter.  Like abstract
data types, objects create an abstraction barrier between the use and
implementation of data.  Like dispatch dictionaries in message passing, objects
respond to behavioral requests.  Like mutable data structures, objects have
local state that is not directly accessible from the global environment.
The Python object system provides new syntax to promote the use of these
techniques for organizing programs.</p>
<p>But the object system offers more than just convenience; it enables a new
metaphor for designing programs in which several independent agents interact
within the computer. Each object bundles together local state and behavior in a
way that hides the complexity of both behind a data abstraction. Objects
communicate with each other, and useful results are computed as a consequence
of their interaction.  Not only do objects pass messages, they also share
behavior among other objects of the same type and inherit characteristics from
related types.</p>
<p>The paradigm of object-oriented programming has its own vocabulary that
reinforces the object metaphor.  We have seen that an object is a data value
that has methods and attributes, accessible via dot notation. Every object also
has a type, called its <em>class</em>.  To implement new types of data, we create new
classes.</p>
<div class="section" id="objects-and-classes">
<h3>2.5.1   Objects and Classes</h3>
<p>A class serves as a template for all objects whose type is that class. Every
object is an instance of some particular class.  The objects we have used so far
all have built-in classes, but new classes can be defined similarly to how new
functions can be defined.  A class definition specifies the attributes and
methods shared among objects of that class.  We will introduce the class
statement by revisiting the example of a bank account.</p>
<p>When introducing local state, we saw that bank accounts are naturally modeled as
mutable values that have a <tt class="docutils literal">balance</tt>.  A bank account object should have a
<tt class="docutils literal">withdraw</tt> method that updates the account balance and returns the requested
amount, if it is available.  We would like additional behavior to complete the
account abstraction: a bank account should be able to return its current
balance, return the name of the account holder, and accept deposits.</p>
<p>An <tt class="docutils literal">Account</tt> class allows us to create multiple instances of bank accounts.
The act of creating a new object instance is known as <em>instantiating</em> the class.
The syntax in Python for instantiating a class is identical to the syntax of
calling a function.  In this case, we call <tt class="docutils literal">Account</tt> with the argument
<tt class="docutils literal">'Jim'</tt>, the account holder's name.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="n">Account</span><span class="p">(</span><span class="s">'Jim'</span><span class="p">)</span>
</pre></div>

<p>An <em>attribute</em> of an object is a name-value pair associated with the object,
which is accessible via dot notation.  The attributes specific to a particular
object, as opposed to all objects of a class, are called <em>instance attributes</em>.
Each <tt class="docutils literal">Account</tt> has its own balance and account holder name, which are examples
of instance attributes. In the broader programming community, instance
attributes may also be called <em>fields</em>, <em>properties</em>, or <em>instance variables</em>.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="o">.</span><span class="n">holder</span>
<span class="go">'Jim'</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="o">.</span><span class="n">balance</span>
<span class="go">0</span>
</pre></div>

<p>Functions that operate on the object or perform object-specific computations are
called methods. The side effects and return value of a method can depend upon,
and change, other attributes of the object.  For example, <tt class="docutils literal">deposit</tt> is a
method of our <tt class="docutils literal">Account</tt> object <tt class="docutils literal">a</tt>. It takes one argument, the amount to
deposit, changes the <tt class="docutils literal">balance</tt> attribute of the object, and returns the
resulting balance.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="o">.</span><span class="n">deposit</span><span class="p">(</span><span class="mi">15</span><span class="p">)</span>
<span class="go">15</span>
</pre></div>

<p>In OOP, we say that methods are <em>invoked</em> on a particular object.  As a result
of invoking the <tt class="docutils literal">withdraw</tt> method, either the withdrawal is approved and the
amount is deducted and returned, or the request is declined and the account
prints an error message.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="o">.</span><span class="n">withdraw</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span>  <span class="c"># The withdraw method returns the balance after withdrawal</span>
<span class="go">5</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="o">.</span><span class="n">balance</span>       <span class="c"># The balance attribute has changed</span>
<span class="go">5</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="o">.</span><span class="n">withdraw</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span>
<span class="go">'Insufficient funds'</span>
</pre></div>

<p>As illustrated above, the behavior of a method can depend upon the changing
attributes of the object.  Two calls to <tt class="docutils literal">withdraw</tt> with the same argument
return different results.</p>
</div>
<div class="section" id="defining-classes">
<h3>2.5.2   Defining Classes</h3>
<p>User-defined classes are created by <tt class="docutils literal">class</tt> statements, which consist of a
single clause.   A class statement defines the class name, then includes a suite
of statements to define the attributes of the class:</p>
<pre class="literal-block">
class &lt;name&gt;:
    &lt;suite&gt;
</pre>
<p>When a class statement is executed, a new class is created and bound to
<tt class="docutils literal">&lt;name&gt;</tt> in the first frame of the current environment.  The suite is then
executed.  Any names bound within the <tt class="docutils literal">&lt;suite&gt;</tt>  of a <tt class="docutils literal">class</tt> statement,
through <tt class="docutils literal">def</tt> or assignment statements, create or modify attributes of the
class.</p>
<p>Classes are typically organized around manipulating instance attributes, which
are the name-value pairs associated not with the class itself, but with each
object of that class.  The class specifies the instance attributes of its
objects by defining a method for initializing new objects.  For example, part
of initializing an object of the <tt class="docutils literal">Account</tt> class is to assign it a starting
balance of 0.</p>
<p>The <tt class="docutils literal">&lt;suite&gt;</tt> of a <tt class="docutils literal">class</tt> statement contains <tt class="docutils literal">def</tt> statements that define
new methods for objects of that class.  The method that initializes objects has
a special name in Python, <tt class="docutils literal">__init__</tt> (two underscores on each side of
"init"), and is called the <em>constructor</em> for the class.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Account</span><span class="p">:</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">account_holder</span><span class="p">):</span>
<span class="gp">    </span>        <span class="bp">self</span><span class="o">.</span><span class="n">balance</span> <span class="o">=</span> <span class="mi">0</span>
<span class="gp">    </span>        <span class="bp">self</span><span class="o">.</span><span class="n">holder</span> <span class="o">=</span> <span class="n">account_holder</span>
</pre></div>

<p>The <tt class="docutils literal">__init__</tt> method for <tt class="docutils literal">Account</tt> has two formal parameters.  The first
one, <tt class="docutils literal">self</tt>, is bound to the newly created <tt class="docutils literal">Account</tt> object. The second
parameter, <tt class="docutils literal">account_holder</tt>, is bound to the argument passed to the class when
it is called to be instantiated.</p>
<p>The constructor binds the instance attribute name <tt class="docutils literal">balance</tt> to 0.  It also
binds the attribute name <tt class="docutils literal">holder</tt> to the value of the name <tt class="docutils literal">account_holder</tt>.
The formal parameter <tt class="docutils literal">account_holder</tt> is a local name to the <tt class="docutils literal">__init__</tt>
method.  On the other hand, the name <tt class="docutils literal">holder</tt> that is bound via the final
assignment statement persists, because it is stored as an attribute of <tt class="docutils literal">self</tt>
using dot notation.</p>
<p>Having defined the <tt class="docutils literal">Account</tt> class, we can instantiate it.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="n">Account</span><span class="p">(</span><span class="s">'Jim'</span><span class="p">)</span>
</pre></div>

<p>This "call" to the <tt class="docutils literal">Account</tt> class creates a new object that is an instance of
<tt class="docutils literal">Account</tt>, then calls the constructor function <tt class="docutils literal">__init__</tt> with two
arguments: the newly created object and the string <tt class="docutils literal">'Jim'</tt>.  By convention, we
use the parameter name <tt class="docutils literal">self</tt> for the first argument of a constructor, because
it is bound to the object being instantiated.  This convention is adopted in
virtually all Python code.</p>
<p>Now, we can access the object's <tt class="docutils literal">balance</tt> and <tt class="docutils literal">holder</tt> using dot notation.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="o">.</span><span class="n">balance</span>
<span class="go">0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span><span class="o">.</span><span class="n">holder</span>
<span class="go">'Jim'</span>
</pre></div>

<p><strong>Identity.</strong> Each new account instance has its own balance attribute, the value
of which is independent of other objects of the same class.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">b</span> <span class="o">=</span> <span class="n">Account</span><span class="p">(</span><span class="s">'Jack'</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">b</span><span class="o">.</span><span class="n">balance</span> <span class="o">=</span> <span class="mi">200</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">acc</span><span class="o">.</span><span class="n">balance</span> <span class="k">for</span> <span class="n">acc</span> <span class="ow">in</span> <span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">)]</span>
<span class="go">[0, 200]</span>
</pre></div>

<p>To enforce this separation, every object that is an instance of a user-defined
class has a unique identity.  Object identity is compared using the <tt class="docutils literal">is</tt> and
<tt class="docutils literal">is not</tt> operators.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="ow">is</span> <span class="n">a</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="ow">is</span> <span class="ow">not</span> <span class="n">b</span>
<span class="go">True</span>
</pre></div>

<p>Despite being constructed from identical calls, the objects bound to <tt class="docutils literal">a</tt> and
<tt class="docutils literal">b</tt> are not the same.  As usual, binding an object to a new name using
assignment does not create a new object.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">c</span> <span class="o">=</span> <span class="n">a</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">c</span> <span class="ow">is</span> <span class="n">a</span>
<span class="go">True</span>
</pre></div>

<p>New objects that have user-defined classes are only created when a class (such
as <tt class="docutils literal">Account</tt>) is instantiated with call expression syntax.</p>
<p><strong>Methods.</strong> Object methods are also defined by a <tt class="docutils literal">def</tt> statement in the suite
of a <tt class="docutils literal">class</tt> statement.  Below, <tt class="docutils literal">deposit</tt> and <tt class="docutils literal">withdraw</tt> are both defined
as methods on objects of the <tt class="docutils literal">Account</tt> class.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Account</span><span class="p">:</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">account_holder</span><span class="p">):</span>
<span class="gp">    </span>        <span class="bp">self</span><span class="o">.</span><span class="n">balance</span> <span class="o">=</span> <span class="mi">0</span>
<span class="gp">    </span>        <span class="bp">self</span><span class="o">.</span><span class="n">holder</span> <span class="o">=</span> <span class="n">account_holder</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">deposit</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">amount</span><span class="p">):</span>
<span class="gp">    </span>        <span class="bp">self</span><span class="o">.</span><span class="n">balance</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">balance</span> <span class="o">+</span> <span class="n">amount</span>
<span class="gp">    </span>        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">balance</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">withdraw</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">amount</span><span class="p">):</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="n">amount</span> <span class="o">&gt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">balance</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="s">'Insufficient funds'</span>
<span class="gp">    </span>        <span class="bp">self</span><span class="o">.</span><span class="n">balance</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">balance</span> <span class="o">-</span> <span class="n">amount</span>
<span class="gp">    </span>        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">balance</span>
</pre></div>

<p>While method definitions do not differ from function definitions in how they are
declared, method definitions do have a different effect.  The function value
that is created by a <tt class="docutils literal">def</tt> statement within a <tt class="docutils literal">class</tt> statement is bound to
the declared name, but bound locally within the class as an attribute.  That
value is invoked as a method using dot notation from an instance of the class.</p>
<p>Each method definition again includes a special first parameter <tt class="docutils literal">self</tt>,
which is bound to the object on which the method is invoked.  For example,
let us say that <tt class="docutils literal">deposit</tt> is invoked on a particular <tt class="docutils literal">Account</tt> object and
passed a single argument value: the amount deposited.  The object itself is
bound to <tt class="docutils literal">self</tt>, while the argument is bound to <tt class="docutils literal">amount</tt>.  All invoked
methods have access to the object via the <tt class="docutils literal">self</tt> parameter, and so they can
all access and manipulate the object's state.</p>
<p>To invoke these methods, we again use dot notation, as illustrated below.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">tom_account</span> <span class="o">=</span> <span class="n">Account</span><span class="p">(</span><span class="s">'Tom'</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tom_account</span><span class="o">.</span><span class="n">deposit</span><span class="p">(</span><span class="mi">100</span><span class="p">)</span>
<span class="go">100</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tom_account</span><span class="o">.</span><span class="n">withdraw</span><span class="p">(</span><span class="mi">90</span><span class="p">)</span>
<span class="go">10</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tom_account</span><span class="o">.</span><span class="n">withdraw</span><span class="p">(</span><span class="mi">90</span><span class="p">)</span>
<span class="go">'Insufficient funds'</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tom_account</span><span class="o">.</span><span class="n">holder</span>
<span class="go">'Tom'</span>
</pre></div>

<p>When a method is invoked via dot notation, the object itself (bound to
<tt class="docutils literal">tom_account</tt>, in this case) plays a dual role.  First, it determines what the
name <tt class="docutils literal">withdraw</tt> means; <tt class="docutils literal">withdraw</tt> is not a name in the environment, but
instead a name that is local to the <tt class="docutils literal">Account</tt> class.  Second, it is bound to
the first parameter <tt class="docutils literal">self</tt> when the <tt class="docutils literal">withdraw</tt> method is invoked.  The
details of the procedure for evaluating dot notation follow in the next
section.</p>
</div>
<div class="section" id="message-passing-and-dot-expressions">
<h3>2.5.3   Message Passing and Dot Expressions</h3>
<p>Methods, which are defined in classes, and instance attributes, which are
typically assigned in constructors, are the fundamental elements of
object-oriented programming.  These two concepts replicate much of the
behavior of a dispatch dictionary in a message passing implementation of a data
value.  Objects take messages using dot notation, but instead of those messages
being arbitrary string-valued keys, they are names local to a class.  Objects
also have named local state values (the instance attributes), but that state can
be accessed and manipulated using dot notation, without having to employ
<tt class="docutils literal">nonlocal</tt> statements in the implementation.</p>
<p>The central idea in message passing was that data values should have behavior by
responding to messages that are relevant to the abstract type they represent.
Dot notation is a syntactic feature of Python that formalizes the message
passing metaphor. The advantage of using a language with a built-in object
system is that message passing can interact seamlessly with other language
features, such as assignment statements.  We do not require different messages
to "get" or "set" the value associated with a local attribute name; the language
syntax allows us to use the message name directly.</p>
<p><strong>Dot expressions.</strong> The code fragment <tt class="docutils literal">tom_account.deposit</tt> is called a <em>dot
expression</em>. A dot expression consists of an expression, a dot, and a name:</p>
<pre class="literal-block">
&lt;expression&gt; . &lt;name&gt;
</pre>
<p>The <tt class="docutils literal">&lt;expression&gt;</tt> can be any valid Python expression, but the <tt class="docutils literal">&lt;name&gt;</tt> must
be a simple name (not an expression that evaluates to a name).  A dot expression
evaluates to the value of the attribute with the given <tt class="docutils literal">&lt;name&gt;</tt>, for the
object that is the value of the <tt class="docutils literal">&lt;expression&gt;</tt>.</p>
<p>The built-in function <tt class="docutils literal">getattr</tt> also returns an attribute for an object by
name.  It is the function equivalent of dot notation.  Using <tt class="docutils literal">getattr</tt>, we can
look up an attribute using a string, just as we did with a dispatch dictionary.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="nb">getattr</span><span class="p">(</span><span class="n">tom_account</span><span class="p">,</span> <span class="s">'balance'</span><span class="p">)</span>
<span class="go">10</span>
</pre></div>

<p>We can also test whether an object has a named attribute with <tt class="docutils literal">hasattr</tt>.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="nb">hasattr</span><span class="p">(</span><span class="n">tom_account</span><span class="p">,</span> <span class="s">'deposit'</span><span class="p">)</span>
<span class="go">True</span>
</pre></div>

<p>The attributes of an object include all of its instance attributes, along with
all of the attributes (including methods) defined in its class.  Methods are
attributes of the class that require special handling.</p>
<p><strong>Methods and functions.</strong> When a method is invoked on an object, that object is
implicitly passed as the first argument to the method. That is, the object that
is the value of the <tt class="docutils literal">&lt;expression&gt;</tt> to the left of the dot is passed
automatically as the first argument to the method named on the right side of the
dot expression.  As a result, the object is bound to the parameter <tt class="docutils literal">self</tt>.</p>
<p>To achieve automatic <tt class="docutils literal">self</tt> binding, Python distinguishes between
<em>functions</em>, which we have been creating since the beginning of the text, and
<em>bound methods</em>, which couple together a function and the object on which
that method will be invoked. A bound method value is already associated with its
first argument, the instance on which it was invoked, which will be named
<tt class="docutils literal">self</tt> when the method is called.</p>
<p>We can see the difference in the interactive interpreter by calling <tt class="docutils literal">type</tt> on
the returned values of dot expressions.  As an attribute of a class, a method
is just a function, but as an attribute of an instance, it is a bound method:</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="n">Account</span><span class="o">.</span><span class="n">deposit</span><span class="p">)</span>
<span class="go">&lt;class 'function'&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="n">tom_account</span><span class="o">.</span><span class="n">deposit</span><span class="p">)</span>
<span class="go">&lt;class 'method'&gt;</span>
</pre></div>

<p>These two results differ only in the fact that the first is a standard
two-argument function with parameters <tt class="docutils literal">self</tt> and <tt class="docutils literal">amount</tt>. The second
is a one-argument method, where the name <tt class="docutils literal">self</tt> will be bound to the object
named <tt class="docutils literal">tom_account</tt> automatically when the method is called, while the
parameter <tt class="docutils literal">amount</tt> will be bound to the argument passed to the method. Both of
these values, whether function values or bound method values, are associated
with the same <tt class="docutils literal">deposit</tt> function body.</p>
<p>We can call <tt class="docutils literal">deposit</tt> in two ways: as a function and as a bound method.  In
the former case, we must supply an argument for the <tt class="docutils literal">self</tt> parameter
explicitly.  In the latter case, the <tt class="docutils literal">self</tt> parameter is bound automatically.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">Account</span><span class="o">.</span><span class="n">deposit</span><span class="p">(</span><span class="n">tom_account</span><span class="p">,</span> <span class="mi">1001</span><span class="p">)</span>  <span class="c"># The deposit function takes 2 arguments</span>
<span class="go">1011</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tom_account</span><span class="o">.</span><span class="n">deposit</span><span class="p">(</span><span class="mi">1000</span><span class="p">)</span>           <span class="c"># The deposit method takes 1 argument</span>
<span class="go">2011</span>
</pre></div>

<p>The function <tt class="docutils literal">getattr</tt> behaves exactly like dot notation: if its first
argument is an object but the name is a method defined in the class, then
<tt class="docutils literal">getattr</tt> returns a bound method value.  On the other hand, if the first
argument is a class, then <tt class="docutils literal">getattr</tt> returns the attribute value directly,
which is a plain function.</p>
<p><strong>Practical guidance: naming conventions.</strong> Class names are conventionally
written using the CapWords convention (also called CamelCase because the capital
letters in the middle of a name look like humps).  Method names follow the
standard convention of naming functions using lowercased words separated by
underscores.</p>
<p>In some cases, there are instance variables and methods that are related to the
maintenance and consistency of an object that we don't want users of the object
to see or use.  They are not part of the abstraction defined by a class, but
instead part of the implementation.  Python's convention dictates that if an
attribute name starts with an underscore, it should only be accessed within
methods of the class itself, rather than by users of the class.</p>
</div>
<div class="section" id="class-attributes">
<h3>2.5.4   Class Attributes</h3>
<p>Some attribute values are shared across all objects of a given class. Such
attributes are associated with the class itself, rather than any individual
instance of the class. For instance, let us say that a bank pays interest on the
balance of accounts at a fixed interest rate. That interest rate may change, but
it is a single value shared across all accounts.</p>
<p>Class attributes are created by assignment statements in the suite of a
<tt class="docutils literal">class</tt> statement, outside of any method definition. In the broader developer
community, class attributes may also be called class variables or static
variables. The following class statement creates a class attribute for
<tt class="docutils literal">Account</tt> with the name <tt class="docutils literal">interest</tt>.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Account</span><span class="p">:</span>
<span class="gp">    </span>    <span class="n">interest</span> <span class="o">=</span> <span class="mf">0.02</span>            <span class="c"># A class attribute</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">account_holder</span><span class="p">):</span>
<span class="gp">    </span>        <span class="bp">self</span><span class="o">.</span><span class="n">balance</span> <span class="o">=</span> <span class="mi">0</span>
<span class="gp">    </span>        <span class="bp">self</span><span class="o">.</span><span class="n">holder</span> <span class="o">=</span> <span class="n">account_holder</span>
<span class="gp">    </span>    <span class="c"># Additional methods would be defined here</span>
</pre></div>

<p>This attribute can still be accessed from any instance of the class.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">tom_account</span> <span class="o">=</span> <span class="n">Account</span><span class="p">(</span><span class="s">'Tom'</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">jim_account</span> <span class="o">=</span> <span class="n">Account</span><span class="p">(</span><span class="s">'Jim'</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tom_account</span><span class="o">.</span><span class="n">interest</span>
<span class="go">0.02</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">jim_account</span><span class="o">.</span><span class="n">interest</span>
<span class="go">0.02</span>
</pre></div>

<p>However, a single assignment statement to a class attribute changes the value of
the attribute for all instances of the class.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">Account</span><span class="o">.</span><span class="n">interest</span> <span class="o">=</span> <span class="mf">0.04</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tom_account</span><span class="o">.</span><span class="n">interest</span>
<span class="go">0.04</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">jim_account</span><span class="o">.</span><span class="n">interest</span>
<span class="go">0.04</span>
</pre></div>

<p><strong>Attribute names.</strong> We have introduced enough complexity into our object system
that we have to specify how names are resolved to particular attributes.  After
all, we could easily have a class attribute and an instance attribute with the
same name.</p>
<p>As we have seen, a dot expressions consist of an expression, a dot, and a name:</p>
<pre class="literal-block">
&lt;expression&gt; . &lt;name&gt;
</pre>
<p>To evaluate a dot expression:</p>
<ol class="arabic simple">
<li>Evaluate the <tt class="docutils literal">&lt;expression&gt;</tt> to the left of the dot, which yields the
<em>object</em> of the dot expression.</li>
<li><tt class="docutils literal">&lt;name&gt;</tt> is matched against the instance attributes of that object; if an
attribute with that name exists, its value is returned.</li>
<li>If <tt class="docutils literal">&lt;name&gt;</tt> does not appear among instance attributes, then <tt class="docutils literal">&lt;name&gt;</tt> is
looked up in the class, which yields a class attribute value.</li>
<li>That value is returned unless it is a function, in which case a bound method
is returned instead.</li>
</ol>
<p>In this evaluation procedure, instance attributes are found before class
attributes, just as local names have priority over global in an environment.
Methods defined within the class are bound to the object of the dot expression
during the third step of this evaluation procedure.  The procedure for looking
up a name in a class has additional nuances that will arise shortly, once we
introduce class inheritance.</p>
<p><strong>Attribute assignment.</strong> All assignment statements that contain a dot
expression on their left-hand side affect attributes for the object of that dot
expression.  If the object is an instance, then assignment sets an instance
attribute.  If the object is a class, then assignment sets a class attribute.
As a consequence of this rule, assignment to an attribute of an object cannot
affect the attributes of its class.  The examples below illustrate this
distinction.</p>
<p>If we assign to the named attribute <tt class="docutils literal">interest</tt> of an account instance, we
create a new instance attribute that has the same name as the existing class
attribute.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">jim_account</span><span class="o">.</span><span class="n">interest</span> <span class="o">=</span> <span class="mf">0.08</span>
</pre></div>

<p>and that attribute value will be returned from a dot expression.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">jim_account</span><span class="o">.</span><span class="n">interest</span>
<span class="go">0.08</span>
</pre></div>

<p>However, the class attribute <tt class="docutils literal">interest</tt> still retains its original value,
which is returned for all other accounts.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">tom_account</span><span class="o">.</span><span class="n">interest</span>
<span class="go">0.04</span>
</pre></div>

<p>Changes to the class attribute <tt class="docutils literal">interest</tt> will affect <tt class="docutils literal">tom_account</tt>, but the
instance attribute for <tt class="docutils literal">jim_account</tt> will be unaffected.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">Account</span><span class="o">.</span><span class="n">interest</span> <span class="o">=</span> <span class="mf">0.05</span>  <span class="c"># changing the class attribute</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">tom_account</span><span class="o">.</span><span class="n">interest</span>     <span class="c"># changes instances without like-named instance attributes</span>
<span class="go">0.05</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">jim_account</span><span class="o">.</span><span class="n">interest</span>     <span class="c"># but the existing instance attribute is unaffected</span>
<span class="go">0.08</span>
</pre></div>

</div>
<div class="section" id="inheritance">
<h3>2.5.5   Inheritance</h3>
<p>When working in the object-oriented programming paradigm, we often find that
different types are related.  In particular, we find that similar classes differ
in their amount of specialization. Two classes may have similar attributes, but
one represents a special case of the other.</p>
<p>For example, we may want to implement a checking account, which is different
from a standard account. A checking account charges an extra $1 for each
withdrawal and has a lower interest rate.  Here, we demonstrate the desired
behavior.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">ch</span> <span class="o">=</span> <span class="n">CheckingAccount</span><span class="p">(</span><span class="s">'Tom'</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ch</span><span class="o">.</span><span class="n">interest</span>     <span class="c"># Lower interest rate for checking accounts</span>
<span class="go">0.01</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ch</span><span class="o">.</span><span class="n">deposit</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span>  <span class="c"># Deposits are the same</span>
<span class="go">20</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">ch</span><span class="o">.</span><span class="n">withdraw</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span>  <span class="c"># withdrawals decrease balance by an extra charge</span>
<span class="go">14</span>
</pre></div>

<p>A <tt class="docutils literal">CheckingAccount</tt> is a specialization of an <tt class="docutils literal">Account</tt>.  In OOP
terminology, the generic account will serve as the base class of
<tt class="docutils literal">CheckingAccount</tt>, while <tt class="docutils literal">CheckingAccount</tt> will be a subclass of
<tt class="docutils literal">Account</tt>.  (The terms <em>parent class</em> and <em>superclass</em> are also used for the
base class, while <em>child class</em> is also used for the subclass.)</p>
<p>A subclass <em>inherits</em> the attributes of its base class, but may <em>override</em>
certain attributes, including certain methods. With inheritance, we only specify
what is different between the subclass and the base class. Anything that we
leave unspecified in the subclass is automatically assumed to behave just as it
would for the base class.</p>
<p>Inheritance also has a role in our object metaphor, in addition to being a
useful organizational feature.  Inheritance is meant to represent <em>is-a</em>
relationships between classes, which contrast with <em>has-a</em> relationships.  A
checking account <em>is-a</em> specific type of account, so having a
<tt class="docutils literal">CheckingAccount</tt> inherit from <tt class="docutils literal">Account</tt> is an appropriate use of
inheritance. On the other hand, a bank <em>has-a</em> list of bank accounts that it
manages, so neither should inherit from the other.  Instead, a list of account
objects would be naturally expressed as an instance attribute of a bank object.</p>
</div>
<div class="section" id="using-inheritance">
<h3>2.5.6   Using Inheritance</h3>
<p>First, we give a full implementation of the <tt class="docutils literal">Account</tt> class, which
includes docstrings for the class and its methods.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">Account</span><span class="p">:</span>
<span class="gp">    </span>    <span class="sd">"""A bank account that has a non-negative balance."""</span>
<span class="gp">    </span>    <span class="n">interest</span> <span class="o">=</span> <span class="mf">0.02</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">account_holder</span><span class="p">):</span>
<span class="gp">    </span>        <span class="bp">self</span><span class="o">.</span><span class="n">balance</span> <span class="o">=</span> <span class="mi">0</span>
<span class="gp">    </span>        <span class="bp">self</span><span class="o">.</span><span class="n">holder</span> <span class="o">=</span> <span class="n">account_holder</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">deposit</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">amount</span><span class="p">):</span>
<span class="gp">    </span>        <span class="sd">"""Increase the account balance by amount and return the new balance."""</span>
<span class="gp">    </span>        <span class="bp">self</span><span class="o">.</span><span class="n">balance</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">balance</span> <span class="o">+</span> <span class="n">amount</span>
<span class="gp">    </span>        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">balance</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">withdraw</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">amount</span><span class="p">):</span>
<span class="gp">    </span>        <span class="sd">"""Decrease the account balance by amount and return the new balance."""</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="n">amount</span> <span class="o">&gt;</span> <span class="bp">self</span><span class="o">.</span><span class="n">balance</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="s">'Insufficient funds'</span>
<span class="gp">    </span>        <span class="bp">self</span><span class="o">.</span><span class="n">balance</span> <span class="o">=</span> <span class="bp">self</span><span class="o">.</span><span class="n">balance</span> <span class="o">-</span> <span class="n">amount</span>
<span class="gp">    </span>        <span class="k">return</span> <span class="bp">self</span><span class="o">.</span><span class="n">balance</span>
</pre></div>

<p>A full implementation of <tt class="docutils literal">CheckingAccount</tt> appears below. We specify
inheritance by placing an expression that evaluates to the base class in
parentheses after the class name.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">CheckingAccount</span><span class="p">(</span><span class="n">Account</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""A bank account that charges for withdrawals."""</span>
<span class="gp">    </span>    <span class="n">withdraw_charge</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">    </span>    <span class="n">interest</span> <span class="o">=</span> <span class="mf">0.01</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">withdraw</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">amount</span><span class="p">):</span>
<span class="gp">    </span>        <span class="k">return</span> <span class="n">Account</span><span class="o">.</span><span class="n">withdraw</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">amount</span> <span class="o">+</span> <span class="bp">self</span><span class="o">.</span><span class="n">withdraw_charge</span><span class="p">)</span>
</pre></div>

<p>Here, we introduce a class attribute <tt class="docutils literal">withdraw_charge</tt> that is specific to the
<tt class="docutils literal">CheckingAccount</tt> class. We assign a lower value to the <tt class="docutils literal">interest</tt>
attribute.  We also define a new <tt class="docutils literal">withdraw</tt> method to override the behavior
defined in the <tt class="docutils literal">Account</tt> class. With no further statements in the class suite,
all other behavior is inherited from the base class <tt class="docutils literal">Account</tt>.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">checking</span> <span class="o">=</span> <span class="n">CheckingAccount</span><span class="p">(</span><span class="s">'Sam'</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">checking</span><span class="o">.</span><span class="n">deposit</span><span class="p">(</span><span class="mi">10</span><span class="p">)</span>
<span class="go">10</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">checking</span><span class="o">.</span><span class="n">withdraw</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span>
<span class="go">4</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">checking</span><span class="o">.</span><span class="n">interest</span>
<span class="go">0.01</span>
</pre></div>

<p>The expression <tt class="docutils literal">checking.deposit</tt> evaluates to a bound method for making
deposits, which was defined in the <tt class="docutils literal">Account</tt> class. When Python
resolves a name in a dot expression that is not an attribute of the instance, it
looks up the name in the class.  In fact, the act of "looking up" a name in a
class tries to find that name in every base class in the inheritance chain for
the original object's class.  We can define this procedure recursively.  To look
up a name in a class.</p>
<ol class="arabic simple">
<li>If it names an attribute in the class, return the attribute value.</li>
<li>Otherwise, look up the name in the base class, if there is one.</li>
</ol>
<p>In the case of <tt class="docutils literal">deposit</tt>, Python would have looked for the name first on the
instance, and then in the <tt class="docutils literal">CheckingAccount</tt> class.  Finally, it would look in
the <tt class="docutils literal">Account</tt> class, where <tt class="docutils literal">deposit</tt> is defined. According to our evaluation
rule for dot expressions, since <tt class="docutils literal">deposit</tt> is a function looked up in the class
for the <tt class="docutils literal">checking</tt> instance, the dot expression evaluates to a bound method
value.  That method is invoked with the argument 10, which calls the deposit
method with <tt class="docutils literal">self</tt> bound to the <tt class="docutils literal">checking</tt> object and <tt class="docutils literal">amount</tt> bound to
10.</p>
<p>The class of an object stays constant throughout.  Even though the <tt class="docutils literal">deposit</tt>
method was found in the <tt class="docutils literal">Account</tt> class, <tt class="docutils literal">deposit</tt> is called with <tt class="docutils literal">self</tt>
bound to an instance of <tt class="docutils literal">CheckingAccount</tt>, not of <tt class="docutils literal">Account</tt>.</p>
<p><strong>Calling ancestors.</strong> Attributes that have been overridden are still
accessible via class objects.  For instance, we implemented
the <tt class="docutils literal">withdraw</tt> method of <tt class="docutils literal">CheckingAccount</tt> by calling the <tt class="docutils literal">withdraw</tt>
method of <tt class="docutils literal">Account</tt> with an argument that included the <tt class="docutils literal">withdraw_charge</tt>.</p>
<p>Notice that we called <tt class="docutils literal">self.withdraw_charge</tt> rather than the equivalent
<tt class="docutils literal">CheckingAccount.withdraw_charge</tt>.  The benefit of the former over the latter
is that a class that inherits from <tt class="docutils literal">CheckingAccount</tt> might override the
withdrawal charge.  If that is the case, we would like our implementation of
<tt class="docutils literal">withdraw</tt> to find that new value instead of the old one.</p>
<p><strong>Interfaces.</strong> It is extremely common in object-oriented programs that
different types of objects will share the same attribute names. An <em>object
interface</em> is a collection of attributes and conditions on those attributes.
For example, all accounts must have <tt class="docutils literal">deposit</tt> and <tt class="docutils literal">withdraw</tt> methods that
take numerical arguments, as well as a <tt class="docutils literal">balance</tt> attribute. The classes
<tt class="docutils literal">Account</tt> and <tt class="docutils literal">CheckingAccount</tt> both implement this interface. Inheritance
specifically promotes name sharing in this way. In some programming languages
such as Java, interface implementations must be explicitly declared.  In others
such as Python, Ruby, and Go, any object with the appropriate names implements
an interface.</p>
<p>The parts of your program that use objects (rather than implementing them) are
most robust to future changes if they do not make assumptions about object
types, but instead only about their attribute names. That is, they use the
object abstraction, rather than assuming anything about its implementation.</p>
<p>For example, let us say that we run a lottery, and we wish to deposit $5 into
each of a list of accounts.  The following implementation does not assume
anything about the types of those accounts, and therefore works equally well
with any type of object that has a <tt class="docutils literal">deposit</tt> method:</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">deposit_all</span><span class="p">(</span><span class="n">winners</span><span class="p">,</span> <span class="n">amount</span><span class="o">=</span><span class="mi">5</span><span class="p">):</span>
<span class="gp">    </span>    <span class="k">for</span> <span class="n">account</span> <span class="ow">in</span> <span class="n">winners</span><span class="p">:</span>
<span class="gp">    </span>        <span class="n">account</span><span class="o">.</span><span class="n">deposit</span><span class="p">(</span><span class="n">amount</span><span class="p">)</span>
</pre></div>

<p>The function <tt class="docutils literal">deposit_all</tt> above assumes only that each <tt class="docutils literal">account</tt> satisfies
the account object abstraction, and so it will work with any other account
classes that also implement this interface.  Assuming a particular class of
account would violate the abstraction barrier of the account object
abstraction. For example, the following implementation will not necessarily
work with new kinds of accounts:</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">deposit_all</span><span class="p">(</span><span class="n">winners</span><span class="p">,</span> <span class="n">amount</span><span class="o">=</span><span class="mi">5</span><span class="p">):</span>
<span class="gp">    </span>    <span class="k">for</span> <span class="n">account</span> <span class="ow">in</span> <span class="n">winners</span><span class="p">:</span>
<span class="gp">    </span>        <span class="n">Account</span><span class="o">.</span><span class="n">deposit</span><span class="p">(</span><span class="n">account</span><span class="p">,</span> <span class="n">amount</span><span class="p">)</span>
</pre></div>

<p>We will address this topic in more detail later in the chapter.</p>
</div>
<div class="section" id="multiple-inheritance">
<h3>2.5.7   Multiple Inheritance</h3>
<p>Python supports the concept of a subclass inheriting attributes from multiple
base classes, a language feature called <em>multiple inheritance</em>.</p>
<p>Suppose that we have a <tt class="docutils literal">SavingsAccount</tt> that inherits from <tt class="docutils literal">Account</tt>, but
charges customers a small fee every time they make a deposit.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">SavingsAccount</span><span class="p">(</span><span class="n">Account</span><span class="p">):</span>
<span class="gp">    </span>    <span class="n">deposit_charge</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">deposit</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">amount</span><span class="p">):</span>
<span class="gp">    </span>        <span class="k">return</span> <span class="n">Account</span><span class="o">.</span><span class="n">deposit</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">amount</span> <span class="o">-</span> <span class="bp">self</span><span class="o">.</span><span class="n">deposit_charge</span><span class="p">)</span>
</pre></div>

<p>Then, a clever executive conceives of an <tt class="docutils literal">AsSeenOnTVAccount</tt> account with the
best features of both <tt class="docutils literal">CheckingAccount</tt> and <tt class="docutils literal">SavingsAccount</tt>: withdrawal
fees, deposit fees, and a low interest rate. It's both a checking and a savings
account in one! "If we build it," the executive reasons, "someone will sign up
and pay all those fees.  We'll even give them a dollar."</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">class</span> <span class="nc">AsSeenOnTVAccount</span><span class="p">(</span><span class="n">CheckingAccount</span><span class="p">,</span> <span class="n">SavingsAccount</span><span class="p">):</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">__init__</span><span class="p">(</span><span class="bp">self</span><span class="p">,</span> <span class="n">account_holder</span><span class="p">):</span>
<span class="gp">    </span>        <span class="bp">self</span><span class="o">.</span><span class="n">holder</span> <span class="o">=</span> <span class="n">account_holder</span>
<span class="gp">    </span>        <span class="bp">self</span><span class="o">.</span><span class="n">balance</span> <span class="o">=</span> <span class="mi">1</span>           <span class="c"># A free dollar!</span>
</pre></div>

<p>In fact, this implementation is complete.  Both withdrawal and deposits will
generate fees, using the function definitions in <tt class="docutils literal">CheckingAccount</tt> and
<tt class="docutils literal">SavingsAccount</tt> respectively.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">such_a_deal</span> <span class="o">=</span> <span class="n">AsSeenOnTVAccount</span><span class="p">(</span><span class="s">"John"</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">such_a_deal</span><span class="o">.</span><span class="n">balance</span>
<span class="go">1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">such_a_deal</span><span class="o">.</span><span class="n">deposit</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span>            <span class="c"># $2 fee from SavingsAccount.deposit</span>
<span class="go">19</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">such_a_deal</span><span class="o">.</span><span class="n">withdraw</span><span class="p">(</span><span class="mi">5</span><span class="p">)</span>            <span class="c"># $1 fee from CheckingAccount.withdraw</span>
<span class="go">13</span>
</pre></div>

<p>Non-ambiguous references are resolved correctly as expected:</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">such_a_deal</span><span class="o">.</span><span class="n">deposit_charge</span>
<span class="go">2</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">such_a_deal</span><span class="o">.</span><span class="n">withdraw_charge</span>
<span class="go">1</span>
</pre></div>

<p>But what about when the reference is ambiguous, such as the reference to the
<tt class="docutils literal">withdraw</tt> method that is defined in both <tt class="docutils literal">Account</tt> and <tt class="docutils literal">CheckingAccount</tt>?
The figure below depicts an <em>inheritance graph</em> for the <tt class="docutils literal">AsSeenOnTVAccount</tt>
class.  Each arrow points from a subclass to a base class.</p>
<div class="figure">
<img alt="" src="../img/multiple_inheritance.png"/>
</div>
<p>For a simple "diamond" shape like this, Python resolves names from left to
right, then upwards.  In this example, Python checks for an attribute name in
the following classes, in order, until an attribute with that name is found:</p>
<pre class="literal-block">
AsSeenOnTVAccount, CheckingAccount, SavingsAccount, Account, object
</pre>
<p>There is no correct solution to the inheritance ordering problem, as there are
cases in which we might prefer to give precedence to certain inherited classes
over others.  However, any programming language that supports multiple
inheritance must select some ordering in a consistent way, so that users of the
language can predict the behavior of their programs.</p>
<p><strong>Further reading.</strong> Python resolves this name using a recursive algorithm
called the C3 Method Resolution Ordering.  The method resolution order of any
class can be queried using the <tt class="docutils literal">mro</tt> method on all classes.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">c</span><span class="o">.</span><span class="n">__name__</span> <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="n">AsSeenOnTVAccount</span><span class="o">.</span><span class="n">mro</span><span class="p">()]</span>
<span class="go">['AsSeenOnTVAccount', 'CheckingAccount', 'SavingsAccount', 'Account', 'object']</span>
</pre></div>

<p>The precise algorithm for finding method resolution orderings is not a topic for
this text, but is <a class="reference external" href="http://python-history.blogspot.com/2010/06/method-resolution-order.html">described by Python's primary author</a> with
a reference to the original paper.</p>
</div>
<div class="section" id="functions-as-objects">
<h3>2.5.8   Functions as Objects</h3>
<p>In Python and many other programming languages, functions themselves are objects
that are instances of special classes. For example, functions built into the
interpreter are instances of the <tt class="docutils literal">builtin_function_or_method</tt> class:</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="nb">pow</span><span class="p">)</span>
<span class="go">&lt;class 'builtin_function_or_method'&gt;</span>
</pre></div>

<p>Similarly, as demonstrated above for the <tt class="docutils literal">deposit</tt> function and method,
user-defined functions are instances of <tt class="docutils literal">function</tt>, and user-defined methods
are instances of <tt class="docutils literal">method</tt>:</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="n">Account</span><span class="o">.</span><span class="n">deposit</span><span class="p">)</span>
<span class="go">&lt;class 'function'&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="n">tom_account</span><span class="o">.</span><span class="n">deposit</span><span class="p">)</span>
<span class="go">&lt;class 'method'&gt;</span>
</pre></div>

<p>By calling <tt class="docutils literal">mro</tt> on the resulting types, we can see that they all inherit from
<tt class="docutils literal">object</tt>, just like the classes we defined above. (The names
<tt class="docutils literal">builtin_function_or_method</tt>, <tt class="docutils literal">function</tt>, and <tt class="docutils literal">method</tt> are not bound in
the global namespace, so we cannot directly call <tt class="docutils literal">mro</tt> on them.)</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">c</span><span class="o">.</span><span class="n">__name__</span> <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="nb">type</span><span class="p">(</span><span class="nb">pow</span><span class="p">)</span><span class="o">.</span><span class="n">mro</span><span class="p">()]</span>
<span class="go">['builtin_function_or_method', 'object']</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">c</span><span class="o">.</span><span class="n">__name__</span> <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="nb">type</span><span class="p">(</span><span class="n">Account</span><span class="o">.</span><span class="n">deposit</span><span class="p">)</span><span class="o">.</span><span class="n">mro</span><span class="p">()]</span>
<span class="go">['function', 'object']</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">c</span><span class="o">.</span><span class="n">__name__</span> <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="nb">type</span><span class="p">(</span><span class="n">tom_account</span><span class="o">.</span><span class="n">deposit</span><span class="p">)</span><span class="o">.</span><span class="n">mro</span><span class="p">()]</span>
<span class="go">['method', 'object']</span>
</pre></div>

<p>Since functions are objects, they have attributes just like any other object.
For example, all functions have a <tt class="docutils literal">__name__</tt> attribute:</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="nb">pow</span><span class="o">.</span><span class="n">__name__</span>
<span class="go">'pow'</span>
</pre></div>

<p>Attributes can also be added to user-defined functions, and most existing
attributes can be modified. Methods and built-in functions, however, don't allow
adding or changing attributes.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">my_pow</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">):</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="nb">pow</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="n">y</span><span class="p">)</span>
</pre></div>

<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">my_pow</span>
<span class="go">&lt;function my_pow at 0x7f77b2558270&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">my_pow</span><span class="o">.</span><span class="n">__name__</span> <span class="o">=</span> <span class="s">"power"</span>    <span class="c"># change attribute</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">my_pow</span>
<span class="go">&lt;function power at 0x7f77b2558270&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">my_pow</span><span class="o">.</span><span class="n">value</span> <span class="o">=</span> <span class="mi">42</span>   <span class="c"># new attribute</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">my_pow</span><span class="o">.</span><span class="n">value</span>
<span class="go">42</span>
</pre></div>

<p>(Note: As of Python 3.3, functions have a <tt class="docutils literal">__qualname__</tt> attribute that is
used in many places instead of <tt class="docutils literal">__name__</tt>. In the example above, it is the
<tt class="docutils literal">__qualname__</tt> attribute that must be changed instead of <tt class="docutils literal">__name__</tt> to
achieve the same behavior in Python 3.3.)</p>
<p>Function attributes allow us to store function-specific data with the function
itself, without polluting the global namespace.</p>
</div>
<div class="section" id="the-role-of-objects">
<h3>2.5.9   The Role of Objects</h3>
<p>The Python object system is designed to make data abstraction and message
passing both convenient and flexible.  The specialized syntax of classes,
methods, inheritance, and dot expressions all enable us to formalize the object
metaphor in our programs, which improves our ability to organize large programs.</p>
<p>In particular, we would like our object system to promote a <em>separation of
concerns</em> among the different aspects of the program.  Each object in a program
encapsulates and manages some part of the program's state, and each class
statement defines the functions that implement some part of the program's
overall logic.  Abstraction barriers enforce the boundaries between different
aspects of a large program.</p>
<p>Object-oriented programming is particularly well-suited to programs that model
systems that have separate but interacting parts.  For instance, different users
interact in a social network, different characters interact in a game, and
different shapes interact in a physical simulation. When representing such
systems, the objects in a program often map naturally onto objects in the system
being modeled, and classes represent their types and relationships.</p>
<p>On the other hand, classes may not provide the best mechanism for implementing
certain abstractions.  Functional abstractions provide a more natural metaphor
for representing relationships between inputs and outputs. One should not feel
compelled to fit every bit of logic in a program within a class, especially when
defining independent functions for manipulating data is more natural.  Functions
can also enforce a separation of concerns.</p>
<p>Multi-paradigm languages such as Python allow programmers to match
organizational paradigms to appropriate problems. Learning to identify when to
introduce a new class, as opposed to a new function, in order to simplify or
modularize a program, is an important design skill in software engineering that
deserves careful attention.</p>
</div>
</div>
  <p><i>Continue</i>:
  	<a href="26-implementing-classes-and-objects.html">
  		2.6 Implementing Classes and Objects
  	</a>
      </div>
    </section>

    <div class="wrap">
      <footer id="contentinfo" class="body">
          Composing Programs by <a href="http://www.denero.org/">John
          DeNero</a>, based on the textbook <a
          href="http://mitpress.mit.edu/sicp/">Structure and
          Interpretation of Computer Programs</a> by Harold Abelson and
          Gerald Jay Sussman, is licensed under a <a rel="license"
          href="http://creativecommons.org/licenses/by-sa/3.0/">Creative
          Commons Attribution-ShareAlike 3.0 Unported License</a>.
      </footer><!-- /#contentinfo -->
    </div>
  </div>
</body>

<!-- Mirrored from www.composingprograms.com/versions/v1/pages/25-object-oriented-programming.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:46:04 GMT -->
</html>
