<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from www.composingprograms.com/projects.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:43:50 GMT -->
<head>
  <title>Programming Projects</title>
  <meta charset="utf-8" />

  <link rel="stylesheet" type="text/css" href="theme/css/cp.css" />


</head>

<body id="index" class="home">
  <div class="container">

    <div class="nav-main">
      <div class="wrap">
        <a class="nav-home" href="index.html">
          <span class="nav-logo">c<span class="nav-logo-compose">⚬</span>mp<span class="nav-logo-compose">⚬</span>sing pr<span class="nav-logo-compose">⚬</span>grams</span>
        </a>
        <ul class="nav-site">
          <li><a href="index.html">Text</a></li>
          <li><a href="projects.html">Projects</a></li>
          <li><a href="tutor.html">Tutor</a></li>
          <li><a href="about.html">About</a></li>
        </ul>
      </div>
    </div>

    <section class="content wrap documentationContent">
      <div class="nav-docs">
	<div class="nav-docs-section">
		<h3>Main</h3>
		<ul>
      <li><a href="index.html">Text</a></li>
      <li><a href="projects.html">Projects</a></li>
      <li><a href="tutor.html">Tutor</a></li>
      <li><a href="about.html">About</a></li>
    </ul>
	</div>
	<div class="nav-docs-section">
		<h3>Related Sites</h3>
		<ul>
      <li><a href="http://inst.eecs.berkeley.edu/~cs61a">CS 61A Course</a></li>
      <li><a href="versions/v1/index.html">Version 1</a></li>
    </ul>
	</div>
      </div>

      <div class="inner-content">
	<p>These programming projects are designed to complement the Composing Programs text, but they are freely available for any educational use.  Each project comes with a comprehensive set of test cases so that students can check their work.

	<ul>

		<li> The <a
		href="http://inst.eecs.berkeley.edu/~cs61a/fa13/proj/hog/hog.html">Hog</a>
		project highlights the use of iteration and higher-order functions from
		Sections 1.1 - 1.6 of the text. Students build a simulator for a dice game
		called Hog.  This project is based on a <a
		href="http://nifty.stanford.edu/2010/neller-pig/">2010 SIGCSE Nifty
		Assignment</a> by Todd Neller.

		<li> The <a href="http://inst.eecs.berkeley.edu/~cs61a/fa13/proj/trends/trends.html">Twitter Trends</a> project
		highlights the use of data abstraction, tuples, lists, and maps from
		Sections 2.1-2.3 of the text. Students display U.S. maps colored by the
		positive or negative sentiment of tweets that contain a search term.  This
		project was featured as a <a
		href="http://nifty.stanford.edu/2013/denero-muralidharan-trends/">2013 SIGCSE
		Nifty Assignment</a>.

		<li> The <a href="http://inst.eecs.berkeley.edu/~cs61a/fa13/proj/ants/ants.html">Ants Vs SomeBees</a> project
		highlights the object-oriented programming paradigm from Section 2.4 of the
		text.  Students build a clone of the popular tower defense game, Plants Vs
		Zombies. This project was featured as a <a
		href="http://nifty.stanford.edu/2014/denero-ants-vs-somebees/">2014 SIGCSE
		Nifty Assignment</a>.

		<li> The <a href="http://inst.eecs.berkeley.edu/~cs61a/fa13/proj/scheme/scheme.html">Scheme Interpreter</a>
		project asks students to implement an interpreter for a functional subset of
		the Scheme programming language from Chapter 3. The project includes a
		<a href="http://inst.eecs.berkeley.edu/~cs61a/fa12/projects/scheme_contest_gallery/scheme_contest.html#submission-
		1-cs-est-61a">recursive art contest</a>.

	</ul>

	<p>All projects are available for use under a Creative Commons Attribution-ShareAlike 3.0 Unported License.

      </div>
    </section>

    <div class="wrap">
      <footer id="contentinfo" class="body">
          Composing Programs by <a href="http://www.denero.org/">John
          DeNero</a>, based on the textbook <a
          href="http://mitpress.mit.edu/sicp/">Structure and
          Interpretation of Computer Programs</a> by Harold Abelson and
          Gerald Jay Sussman, is licensed under a <a rel="license"
          href="http://creativecommons.org/licenses/by-sa/3.0/">Creative
          Commons Attribution-ShareAlike 3.0 Unported License</a>.
      </footer><!-- /#contentinfo -->
    </div>
  </div>
</body>

<!-- Mirrored from www.composingprograms.com/projects.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:43:50 GMT -->
</html>