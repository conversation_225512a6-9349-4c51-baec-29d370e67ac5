<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from www.composingprograms.com/pages/24-mutable-data.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:45:00 GMT -->
<head>
  <title>2.4 Mutable Data</title>
  <meta charset="utf-8" />

  <link rel="stylesheet" type="text/css" href="../theme/css/cp.css" />

  <!-- Stylesheets -->
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/pytutor.css"/>
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/ui-lightness/jquery-ui-1.8.21.custom.css" />
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/codemirror.css"  />
  <link rel="stylesheet" type="text/css" href="../theme/coding-js/coding.css"  />

  <!-- jQuery -->
  <script type="text/javascript" src="../theme/tutor/js/jquery-1.8.2.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery-ui-1.8.24.custom.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery.ba-bbq.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery.jsPlumb-1.3.10-all-min.js"></script>

  <!-- codemirror.net online code editor -->
  <script type="text/javascript" src="../theme/tutor/js/codemirror/codemirror.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/codemirror/python.js"></script>

  <!-- d3 -->
  <script type="text/javascript" src="../theme/tutor/js/d3.v2.min.js"></script>

  <!-- Online Python Tutor -->
  <script type="text/javascript" src="../theme/tutor/js/pytutor.js"></script>

  <!-- Coding.js -->
  <script type="text/javascript" src="../theme/coding-js/coding.js"></script>
  <script> var $c = new CodingJS("../theme/coding-js/index.html"); </script>

  <!-- Composing Programs -->
  <script type="text/javascript" src="../theme/js/cp.js"></script>

  
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.1/MathJax.js?config=TeX-AMS-MML_HTMLorMML" type= "text/javascript">
       MathJax.Hub.Config({
           config: ["MMLorHTML.js"],
           jax: ["input/TeX","input/MathML","output/HTML-CSS","output/NativeMML"],
           TeX: { extensions: ["AMSmath.js","AMSsymbols.html","noErrors.html","noUndefined.js"], equationNumbers: { autoNumber: "AMS" } },
           extensions: ["tex2jax.js","mml2jax.html","MathMenu.html","MathZoom.html","jsMath2jax.js"],
           tex2jax: {
               inlineMath: [ ['$','$'] ],
               displayMath: [ ['$$','$$'] ],
               processEscapes: true },
           "HTML-CSS": {
               styles: { ".MathJax .mo, .MathJax .mi": {color: "black ! important"}}
           }
       });
    </script>

</head>

<body id="index" class="home">
  <div class="container">

    <div class="nav-main">
      <div class="wrap">
        <a class="nav-home" href="../index.html">
          <span class="nav-logo">c<span class="nav-logo-compose">⚬</span>mp<span class="nav-logo-compose">⚬</span>sing pr<span class="nav-logo-compose">⚬</span>grams</span>
        </a>
        <ul class="nav-site">
          <li><a href="../index.html">Text</a></li>
          <li><a href="../projects.html">Projects</a></li>
          <li><a href="../tutor.html">Tutor</a></li>
          <li><a href="../about.html">About</a></li>
        </ul>
      </div>
    </div>

    <section class="content wrap documentationContent">
      <div class="nav-docs">
	<h3>Chapter 2<a id="hide_contents">Hide contents</a> </h3>
		<div class="nav-docs-section">
			<h3><a href="21-introduction.html">2.1 Introduction</a></h3>
				<li><a href="21-introduction.html#native-data-types">2.1.1 Native Data Types</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="22-data-abstraction.html">2.2 Data Abstraction</a></h3>
				<li><a href="22-data-abstraction.html#example-rational-numbers">2.2.1 Example: Rational Numbers</a>
				<li><a href="22-data-abstraction.html#pairs">2.2.2 Pairs</a>
				<li><a href="22-data-abstraction.html#abstraction-barriers">2.2.3 Abstraction Barriers</a>
				<li><a href="22-data-abstraction.html#the-properties-of-data">2.2.4 The Properties of Data</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="23-sequences.html">2.3 Sequences</a></h3>
				<li><a href="23-sequences.html#lists">2.3.1 Lists</a>
				<li><a href="23-sequences.html#sequence-iteration">2.3.2 Sequence Iteration</a>
				<li><a href="23-sequences.html#sequence-processing">2.3.3 Sequence Processing</a>
				<li><a href="23-sequences.html#sequence-abstraction">2.3.4 Sequence Abstraction</a>
				<li><a href="23-sequences.html#strings">2.3.5 Strings</a>
				<li><a href="23-sequences.html#trees">2.3.6 Trees</a>
				<li><a href="23-sequences.html#linked-lists">2.3.7 Linked Lists</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="24-mutable-data.html">2.4 Mutable Data</a></h3>
				<li><a href="24-mutable-data.html#the-object-metaphor">2.4.1 The Object Metaphor</a>
				<li><a href="24-mutable-data.html#sequence-objects">2.4.2 Sequence Objects</a>
				<li><a href="24-mutable-data.html#dictionaries">2.4.3 Dictionaries</a>
				<li><a href="24-mutable-data.html#local-state">2.4.4 Local State</a>
				<li><a href="24-mutable-data.html#the-benefits-of-non-local-assignment">2.4.5 The Benefits of Non-Local Assignment</a>
				<li><a href="24-mutable-data.html#the-cost-of-non-local-assignment">2.4.6 The Cost of Non-Local Assignment</a>
				<li><a href="24-mutable-data.html#implementing-lists-and-dictionaries">2.4.7 Implementing Lists and Dictionaries</a>
				<li><a href="24-mutable-data.html#dispatch-dictionaries">2.4.8 Dispatch Dictionaries</a>
				<li><a href="24-mutable-data.html#propagating-constraints">2.4.9 Propagating Constraints</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="25-object-oriented-programming.html">2.5 Object-Oriented Programming</a></h3>
				<li><a href="25-object-oriented-programming.html#objects-and-classes">2.5.1 Objects and Classes</a>
				<li><a href="25-object-oriented-programming.html#defining-classes">2.5.2 Defining Classes</a>
				<li><a href="25-object-oriented-programming.html#message-passing-and-dot-expressions">2.5.3 Message Passing and Dot Expressions</a>
				<li><a href="25-object-oriented-programming.html#class-attributes">2.5.4 Class Attributes</a>
				<li><a href="25-object-oriented-programming.html#inheritance">2.5.5 Inheritance</a>
				<li><a href="25-object-oriented-programming.html#using-inheritance">2.5.6 Using Inheritance</a>
				<li><a href="25-object-oriented-programming.html#multiple-inheritance">2.5.7 Multiple Inheritance</a>
				<li><a href="25-object-oriented-programming.html#the-role-of-objects">2.5.8 The Role of Objects</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="26-implementing-classes-and-objects.html">2.6 Implementing Classes and Objects</a></h3>
				<li><a href="26-implementing-classes-and-objects.html#instances">2.6.1 Instances</a>
				<li><a href="26-implementing-classes-and-objects.html#classes">2.6.2 Classes</a>
				<li><a href="26-implementing-classes-and-objects.html#using-implemented-objects">2.6.3 Using Implemented Objects</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="27-object-abstraction.html">2.7 Object Abstraction</a></h3>
				<li><a href="27-object-abstraction.html#string-conversion">2.7.1 String Conversion</a>
				<li><a href="27-object-abstraction.html#special-methods">2.7.2 Special Methods</a>
				<li><a href="27-object-abstraction.html#multiple-representations">2.7.3 Multiple Representations</a>
				<li><a href="27-object-abstraction.html#generic-functions">2.7.4 Generic Functions</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="28-efficiency.html">2.8 Efficiency</a></h3>
				<li><a href="28-efficiency.html#measuring-efficiency">2.8.1 Measuring Efficiency</a>
				<li><a href="28-efficiency.html#memoization">2.8.2 Memoization</a>
				<li><a href="28-efficiency.html#orders-of-growth">2.8.3 Orders of Growth</a>
				<li><a href="28-efficiency.html#example-exponentiation">2.8.4 Example: Exponentiation</a>
				<li><a href="28-efficiency.html#growth-categories">2.8.5 Growth Categories</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="29-recursive-objects.html">2.9 Recursive Objects</a></h3>
				<li><a href="29-recursive-objects.html#linked-list-class">2.9.1 Linked List Class</a>
				<li><a href="29-recursive-objects.html#tree-class">2.9.2 Tree Class</a>
				<li><a href="29-recursive-objects.html#sets">2.9.3 Sets</a>
		</div>
      </div>

      <div class="inner-content">
  <div class="section" id="mutable-data">
<h2>2.4   Mutable Data</h2>
<p>We have seen how abstraction is vital in helping us to cope with the complexity
of large systems.  Effective programming also requires organizational
principles that can guide us in formulating the overall design of a program. In
particular, we need strategies to help us structure large systems to be
modular, meaning that they divide naturally into coherent parts that can be
separately developed and maintained.</p>
<p>One powerful technique for creating modular programs is to incorporate data
that may change state over time.  In this way, a single data object can
represent something that evolves independently of the rest of the program. The
behavior of a changing object may be influenced by its history, just like an
entity in the world. Adding state to data is a central ingredient of a paradigm
called object-oriented programming.</p>
<div class="section" id="the-object-metaphor">
<h3>2.4.1   The Object Metaphor</h3>
<p>In the beginning of this text, we distinguished between functions and data:
functions performed operations and data were operated upon.  When we included
function values among our data, we acknowledged that data too can have
behavior. Functions could be manipulated as data, but could also be called to
perform computation.</p>
<p><em>Objects</em> combine data values with behavior.  Objects represent information,
but also <em>behave</em> like the things that they represent.  The logic of how an
object interacts with other objects is bundled along with the
information that encodes the object's value.  When an object is printed, it
knows how to spell itself out in text.  If an object is composed of parts, it
knows how to reveal those parts on demand.  Objects are both information and
processes, bundled together to represent the properties, interactions, and
behaviors of complex things.</p>
<p>Object behavior is implemented in Python through specialized object syntax
and associated terminology, which we can introduce by example.  A date is a
kind of object.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">datetime</span> <span class="kn">import</span> <span class="n">date</span>
</pre></div>

<p>The name <tt class="docutils literal">date</tt> is bound to a <em>class</em>. As we have seen, a class represents a
kind of value. Individual dates are called <em>instances</em> of that class.
Instances can be <em>constructed</em> by calling the class on arguments that
characterize the instance.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">tues</span> <span class="o">=</span> <span class="n">date</span><span class="p">(</span><span class="mi">2014</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">13</span><span class="p">)</span>
</pre></div>

<p>While <tt class="docutils literal">tues</tt> was constructed from primitive numbers, it behaves like a date.
For instance, subtracting it from another date will give a time difference,
which we can print.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">date</span><span class="p">(</span><span class="mi">2014</span><span class="p">,</span> <span class="mi">5</span><span class="p">,</span> <span class="mi">19</span><span class="p">)</span> <span class="o">-</span> <span class="n">tues</span><span class="p">)</span>
<span class="go">6 days, 0:00:00</span>
</pre></div>

<p>Objects have <em>attributes</em>, which are named values that are part of the object.
In Python, like many other programming languages, we use dot notation to
designated an attribute of an object.</p>
<blockquote>
&lt;expression&gt; . &lt;name&gt;</blockquote>
<p>Above, the <tt class="docutils literal">&lt;expression&gt;</tt> evaluates to an object, and <tt class="docutils literal">&lt;name&gt;</tt> is the name
of an attribute for that object.</p>
<p>Unlike the names that we have considered so far, these attribute names are not
available in the general environment.  Instead, attribute names are particular
to the object instance preceding the dot.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">tues</span><span class="o">.</span><span class="n">year</span>
<span class="go">2014</span>
</pre></div>

<p>Objects also have <em>methods</em>, which are function-valued attributes.
Metaphorically, we say that the object "knows" how to carry out those methods.
By implementation, methods are functions that compute their results from both
their arguments and their object.  For example, The <tt class="docutils literal">strftime</tt> method
(a classic function name meant to evoke "string format of time") of <tt class="docutils literal">tues</tt>
takes a single argument that specifies how to display a date (e.g., <tt class="docutils literal">%A</tt>
means that the day of the week should be spelled out in full).</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">tues</span><span class="o">.</span><span class="n">strftime</span><span class="p">(</span><span class="s1">'%A, %B </span><span class="si">%d</span><span class="s1">'</span><span class="p">)</span>
<span class="go">'Tuesday, May 13'</span>
</pre></div>

<p>Computing the return value  of <tt class="docutils literal">strftime</tt> requires two inputs: the string
that describes the format of the output and the date information bundled into
<tt class="docutils literal">tues</tt>.  Date-specific logic is applied within this method to yield this
result.  We never stated that the 13th of May, 2014, was a Tuesday, but
knowing the corresponding weekday is part of what it means to be a date.  By
bundling behavior and information together, this Python object offers us a
convincing, self-contained abstraction of a date.</p>
<p>Dates are objects, but numbers, strings, lists, and ranges are all objects as
well. They represent values, but also behave in a manner that befits the values
they represent. They also have attributes and methods. For instance, strings
have an array of methods that facilitate text processing.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="s1">'1234'</span><span class="o">.</span><span class="n">isnumeric</span><span class="p">()</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">'rOBERT dE nIRO'</span><span class="o">.</span><span class="n">swapcase</span><span class="p">()</span>
<span class="go">'Robert De Niro'</span>
<span class="gp">&gt;&gt;&gt; </span><span class="s1">'eyes'</span><span class="o">.</span><span class="n">upper</span><span class="p">()</span><span class="o">.</span><span class="n">endswith</span><span class="p">(</span><span class="s1">'YES'</span><span class="p">)</span>
<span class="go">True</span>
</pre></div>

<p>In fact, all values in Python are objects. That is, all values have behavior
and attributes. They act like the values they represent.</p>
</div>
<div class="section" id="sequence-objects">
<h3>2.4.2   Sequence Objects</h3>
<p>Instances of primitive built-in values such as numbers are <em>immutable</em>. The
values themselves cannot change over the course of program execution. Lists on
the other hand are <em>mutable</em>.</p>
<p>Mutable objects are used to represent values that change over time. A person is
the same person from one day to the next, despite having aged, received a
haircut, or otherwise changed in some way. Similarly, an object may have
changing properties due to <em>mutating</em> operations. For example,  it is possible
to change the contents of a list. Most changes are performed by invoking
methods on list objects.</p>
<p>We can introduce many list modification operations through an example that
illustrates the history of playing cards (drastically simplified). Comments in
the examples describe the effect of each method invocation.</p>
<p>Playing cards were invented in China, perhaps around the 9th century. An early
deck had three suits, which corresponded to denominations of money.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">chinese</span> <span class="o">=</span> <span class="p">[</span><span class="s1">'coin'</span><span class="p">,</span> <span class="s1">'string'</span><span class="p">,</span> <span class="s1">'myriad'</span><span class="p">]</span>  <span class="c1"># A list literal</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span> <span class="o">=</span> <span class="n">chinese</span>                         <span class="c1"># Two names refer to the same list</span>
</pre></div>

<p>As cards migrated to Europe (perhaps through Egypt), only the suit of coins
remained in Spanish decks (<em>oro</em>).</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span><span class="o">.</span><span class="n">pop</span><span class="p">()</span>             <span class="c1"># Remove and return the final element</span>
<span class="go">'myriad'</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span><span class="o">.</span><span class="n">remove</span><span class="p">(</span><span class="s1">'string'</span><span class="p">)</span>  <span class="c1"># Remove the first element that equals the argument</span>
</pre></div>

<p>Three more suits were added (they evolved in name and design over time),</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s1">'cup'</span><span class="p">)</span>              <span class="c1"># Add an element to the end</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span><span class="o">.</span><span class="n">extend</span><span class="p">([</span><span class="s1">'sword'</span><span class="p">,</span> <span class="s1">'club'</span><span class="p">])</span>  <span class="c1"># Add all elements of a sequence to the end</span>
</pre></div>

<p>and Italians called swords <em>spades</em>.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span> <span class="o">=</span> <span class="s1">'spade'</span>  <span class="c1"># Replace an element</span>
</pre></div>

<p>giving the suits of a traditional Italian deck of cards.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span>
<span class="go">['coin', 'cup', 'spade', 'club']</span>
</pre></div>

<p>The French variant used today in the U.S. changes the first two suits:</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span><span class="p">[</span><span class="mi">0</span><span class="p">:</span><span class="mi">2</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="s1">'heart'</span><span class="p">,</span> <span class="s1">'diamond'</span><span class="p">]</span>  <span class="c1"># Replace a slice</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span>
<span class="go">['heart', 'diamond', 'spade', 'club']</span>
</pre></div>

<p>Methods also exist for inserting, sorting, and reversing lists.  All of these
mutation operations change the value of the list; they do not create new list
objects.</p>
<p><strong>Sharing and Identity.</strong> Because we have been changing a single list rather
than creating new lists, the object bound to the name <tt class="docutils literal">chinese</tt> has also
changed, because it is the same list object that was bound to <tt class="docutils literal">suits</tt>!</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">chinese</span>  <span class="c1"># This name co-refers with "suits" to the same changing list</span>
<span class="go">['heart', 'diamond', 'spade', 'club']</span>
</pre></div>

<p>This behavior is new.  Previously, if a name did not appear in a statement,
then its value would not be affected by that statement.  With mutable data,
methods called on one name can affect another name at the same time.</p>
<p>The environment diagram for this example shows how the value bound to
<tt class="docutils literal">chinese</tt> is changed by statements involving only <tt class="docutils literal">suits</tt>. Step through
each line of the following example to observe these changes.</p>
<div class="example" data-output="False" data-showallframelabels="True" data-step="3" id="example_6" style="">
chinese = ['coin', 'string', 'myriad']
suits = chinese
suits.pop()
suits.remove('string')
suits.append('cup')
suits.extend(['sword', 'club'])
suits[2] = 'spade'
suits[0:2] = ['heart', 'diamond']
</div>
<script type="text/javascript">
var example_6_trace = {"code": "chinese = ['coin', 'string', 'myriad']\nsuits = chinese\nsuits.pop()\nsuits.remove('string')\nsuits.append('cup')\nsuits.extend(['sword', 'club'])\nsuits[2] = 'spade'\nsuits[0:2] = ['heart', 'diamond']", "trace": [{"event": "step_line", "func_name": "<module>", "globals": {}, "heap": {}, "line": 1, "ordered_globals": [], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"chinese": ["REF", 1]}, "heap": {"1": ["LIST", "coin", "string", "myriad"]}, "line": 2, "ordered_globals": ["chinese"], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"chinese": ["REF", 1], "suits": ["REF", 1]}, "heap": {"1": ["LIST", "coin", "string", "myriad"]}, "line": 3, "ordered_globals": ["chinese", "suits"], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"chinese": ["REF", 1], "suits": ["REF", 1]}, "heap": {"1": ["LIST", "coin", "string"]}, "line": 4, "ordered_globals": ["chinese", "suits"], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"chinese": ["REF", 1], "suits": ["REF", 1]}, "heap": {"1": ["LIST", "coin"]}, "line": 5, "ordered_globals": ["chinese", "suits"], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"chinese": ["REF", 1], "suits": ["REF", 1]}, "heap": {"1": ["LIST", "coin", "cup"]}, "line": 6, "ordered_globals": ["chinese", "suits"], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"chinese": ["REF", 1], "suits": ["REF", 1]}, "heap": {"1": ["LIST", "coin", "cup", "sword", "club"]}, "line": 7, "ordered_globals": ["chinese", "suits"], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"chinese": ["REF", 1], "suits": ["REF", 1]}, "heap": {"1": ["LIST", "coin", "cup", "spade", "club"]}, "line": 8, "ordered_globals": ["chinese", "suits"], "stack_to_render": [], "stdout": ""}, {"event": "return", "func_name": "<module>", "globals": {"chinese": ["REF", 1], "suits": ["REF", 1]}, "heap": {"1": ["LIST", "heart", "diamond", "spade", "club"]}, "line": 8, "ordered_globals": ["chinese", "suits"], "stack_to_render": [], "stdout": ""}]}</script><p>Lists can be copied using the <tt class="docutils literal">list</tt> constructor function.  Changes to one
list do not affect another, unless they share structure.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">nest</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">suits</span><span class="p">)</span>  <span class="c1"># Bind "nest" to a second list with the same elements</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">nest</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="n">suits</span>     <span class="c1"># Create a nested list</span>
</pre></div>

<p>According to this environment, changing the list referenced by <tt class="docutils literal">suits</tt> will
affect the nested list that is the first element of <tt class="docutils literal">nest</tt>, but not the other
elements.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span><span class="o">.</span><span class="n">insert</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="s1">'Joker'</span><span class="p">)</span>  <span class="c1"># Insert an element at index 2, shifting the rest</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">nest</span>
<span class="go">[['heart', 'diamond', 'Joker', 'spade', 'club'], 'diamond', 'spade', 'club']</span>
</pre></div>

<p>And likewise, undoing this change in the first element of <tt class="docutils literal">nest</tt> will change
<tt class="docutils literal">suit</tt> as well.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">nest</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">pop</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
<span class="go">'Joker'</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span>
<span class="go">['heart', 'diamond', 'spade', 'club']</span>
</pre></div>

<p>Stepping through this example line by line will show the representation of a
nested list.</p>
<div class="example" data-output="False" data-showallframelabels="True" data-step="0" id="example_7" style="">
suits = ['heart', 'diamond', 'spade', 'club']
nest = list(suits)
nest[0] = suits
suits.insert(2, 'Joker')
joke = nest[0].pop(2)
</div>
<script type="text/javascript">
var example_7_trace = {"code": "suits = ['heart', 'diamond', 'spade', 'club']\nnest = list(suits)\nnest[0] = suits\nsuits.insert(2, 'Joker')\njoke = nest[0].pop(2)", "trace": [{"event": "step_line", "func_name": "<module>", "globals": {}, "heap": {}, "line": 1, "ordered_globals": [], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"suits": ["REF", 1]}, "heap": {"1": ["LIST", "heart", "diamond", "spade", "club"]}, "line": 2, "ordered_globals": ["suits"], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"nest": ["REF", 2], "suits": ["REF", 1]}, "heap": {"1": ["LIST", "heart", "diamond", "spade", "club"], "2": ["LIST", "heart", "diamond", "spade", "club"]}, "line": 3, "ordered_globals": ["suits", "nest"], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"nest": ["REF", 2], "suits": ["REF", 1]}, "heap": {"1": ["LIST", "heart", "diamond", "spade", "club"], "2": ["LIST", ["REF", 1], "diamond", "spade", "club"]}, "line": 4, "ordered_globals": ["suits", "nest"], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"nest": ["REF", 2], "suits": ["REF", 1]}, "heap": {"1": ["LIST", "heart", "diamond", "Joker", "spade", "club"], "2": ["LIST", ["REF", 1], "diamond", "spade", "club"]}, "line": 5, "ordered_globals": ["suits", "nest"], "stack_to_render": [], "stdout": ""}, {"event": "return", "func_name": "<module>", "globals": {"joke": "Joker", "nest": ["REF", 2], "suits": ["REF", 1]}, "heap": {"1": ["LIST", "heart", "diamond", "spade", "club"], "2": ["LIST", ["REF", 1], "diamond", "spade", "club"]}, "line": 5, "ordered_globals": ["suits", "nest", "joke"], "stack_to_render": [], "stdout": ""}]}</script><p>Because two lists may have the same contents but in fact be different lists, we
require a means to test whether two objects are the same.  Python includes two
comparison operators, called <tt class="docutils literal">is</tt> and <tt class="docutils literal">is not</tt>, that test whether two
expressions in fact evaluate to the identical object.  Two objects are
identical if they are equal in their current value, and any change to one will
always be reflected in the other. Identity is a stronger condition than
equality.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span> <span class="ow">is</span> <span class="n">nest</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span> <span class="ow">is</span> <span class="p">[</span><span class="s1">'heart'</span><span class="p">,</span> <span class="s1">'diamond'</span><span class="p">,</span> <span class="s1">'spade'</span><span class="p">,</span> <span class="s1">'club'</span><span class="p">]</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span> <span class="o">==</span> <span class="p">[</span><span class="s1">'heart'</span><span class="p">,</span> <span class="s1">'diamond'</span><span class="p">,</span> <span class="s1">'spade'</span><span class="p">,</span> <span class="s1">'club'</span><span class="p">]</span>
<span class="go">True</span>
</pre></div>

<p>The final two comparisons illustrate the difference between <tt class="docutils literal">is</tt> and <tt class="docutils literal">==</tt>.
The former checks for identity, while the latter checks for the equality of
contents.</p>
<p><strong>List comprehensions.</strong> A list comprehension always creates a new list.
For example, the <tt class="docutils literal">unicodedata</tt> module tracks the official names of every
character in the Unicode alphabet.  We can look up the characters corresponding
to names, including those for card suits.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">unicodedata</span> <span class="kn">import</span> <span class="n">lookup</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">lookup</span><span class="p">(</span><span class="s1">'WHITE '</span> <span class="o">+</span> <span class="n">s</span><span class="o">.</span><span class="n">upper</span><span class="p">()</span> <span class="o">+</span> <span class="s1">' SUIT'</span><span class="p">)</span> <span class="k">for</span> <span class="n">s</span> <span class="ow">in</span> <span class="n">suits</span><span class="p">]</span>
<span class="go">['♡', '♢', '♤', '♧']</span>
</pre></div>

<p>This resulting list does not share any of its contents with <tt class="docutils literal">suits</tt>, and
evaluating the list comprehension does not modify the <tt class="docutils literal">suits</tt> list.</p>
<p>You can read more about the Unicode standard for representing text in the
<a class="reference external" href="http://getpython3.com/diveintopython3/strings.html#one-ring-to-rule-them-all">Unicode section</a>
of Dive into Python 3.</p>
<p><strong>Tuples.</strong> A tuple, an instance of the built-in <tt class="docutils literal">tuple</tt> type, is an
immutable sequence. Tuples are created using a tuple literal that separates
element expressions by commas. Parentheses are optional but used commonly in
practice. Any objects can be placed within tuples.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="mi">1</span><span class="p">,</span> <span class="mi">2</span> <span class="o">+</span> <span class="mi">3</span>
<span class="go">(1, 5)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">(</span><span class="s2">"the"</span><span class="p">,</span> <span class="mi">1</span><span class="p">,</span> <span class="p">(</span><span class="s2">"and"</span><span class="p">,</span> <span class="s2">"only"</span><span class="p">))</span>
<span class="go">('the', 1, ('and', 'only'))</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span> <span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span> <span class="p">)</span>
<span class="go">&lt;class 'tuple'&gt;</span>
</pre></div>

<p>Empty and one-element tuples have special literal syntax.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="p">()</span>    <span class="c1"># 0 elements</span>
<span class="go">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">(</span><span class="mi">10</span><span class="p">,)</span> <span class="c1"># 1 element</span>
<span class="go">(10,)</span>
</pre></div>

<p>Like lists, tuples have a finite length and support element selection. They
also have a few methods that are also available for lists, such as <tt class="docutils literal">count</tt>
and <tt class="docutils literal">index</tt>.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">code</span> <span class="o">=</span> <span class="p">(</span><span class="s2">"up"</span><span class="p">,</span> <span class="s2">"up"</span><span class="p">,</span> <span class="s2">"down"</span><span class="p">,</span> <span class="s2">"down"</span><span class="p">)</span> <span class="o">+</span> <span class="p">(</span><span class="s2">"left"</span><span class="p">,</span> <span class="s2">"right"</span><span class="p">)</span> <span class="o">*</span> <span class="mi">2</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">len</span><span class="p">(</span><span class="n">code</span><span class="p">)</span>
<span class="go">8</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">code</span><span class="p">[</span><span class="mi">3</span><span class="p">]</span>
<span class="go">'down'</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">code</span><span class="o">.</span><span class="n">count</span><span class="p">(</span><span class="s2">"down"</span><span class="p">)</span>
<span class="go">2</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">code</span><span class="o">.</span><span class="n">index</span><span class="p">(</span><span class="s2">"left"</span><span class="p">)</span>
<span class="go">4</span>
</pre></div>

<p>However, the methods for manipulating the contents of a list are not available
for tuples because tuples are immutable.</p>
<p>While it is not possible to change which elements are in a tuple, it is
possible to change the value of a mutable element contained within a tuple.</p>
<div class="example" data-output="False" data-showallframelabels="True" data-step="-1" id="example_8" style="">
nest = (10, 20, [30, 40])
nest[2].pop()
</div>
<script type="text/javascript">
var example_8_trace = {"code": "nest = (10, 20, [30, 40])\nnest[2].pop()", "trace": [{"event": "step_line", "func_name": "<module>", "globals": {}, "heap": {}, "line": 1, "ordered_globals": [], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"nest": ["REF", 1]}, "heap": {"1": ["TUPLE", 10, 20, ["REF", 2]], "2": ["LIST", 30, 40]}, "line": 2, "ordered_globals": ["nest"], "stack_to_render": [], "stdout": ""}, {"event": "return", "func_name": "<module>", "globals": {"nest": ["REF", 1]}, "heap": {"1": ["TUPLE", 10, 20, ["REF", 2]], "2": ["LIST", 30]}, "line": 2, "ordered_globals": ["nest"], "stack_to_render": [], "stdout": ""}]}</script><p>Tuples are used implicitly in multiple assignment. An assignment of two values
to two names creates a two-element tuple and then unpacks it.</p>
</div>
<div class="section" id="dictionaries">
<h3>2.4.3   Dictionaries</h3>
<p>Dictionaries are Python's built-in data type for storing and manipulating
correspondence relationships.  A dictionary contains key-value pairs, where
both the keys and values are objects.  The purpose of a dictionary is to
provide an abstraction for storing and retrieving values that are indexed not
by consecutive integers, but by descriptive keys.</p>
<p>Strings commonly serve as keys, because strings are our conventional
representation for names of things. This dictionary literal gives the values of
various Roman numerals.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">numerals</span> <span class="o">=</span> <span class="p">{</span><span class="s1">'I'</span><span class="p">:</span> <span class="mf">1.0</span><span class="p">,</span> <span class="s1">'V'</span><span class="p">:</span> <span class="mi">5</span><span class="p">,</span> <span class="s1">'X'</span><span class="p">:</span> <span class="mi">10</span><span class="p">}</span>
</pre></div>

<p>Looking up values by their keys uses the element selection operator that we
previously applied to sequences.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">numerals</span><span class="p">[</span><span class="s1">'X'</span><span class="p">]</span>
<span class="go">10</span>
</pre></div>

<p>A dictionary can have at most one value for each key.  Adding new key-value
pairs and changing the existing value for a key can both be achieved with
assignment statements.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">numerals</span><span class="p">[</span><span class="s1">'I'</span><span class="p">]</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">numerals</span><span class="p">[</span><span class="s1">'L'</span><span class="p">]</span> <span class="o">=</span> <span class="mi">50</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">numerals</span>
<span class="go">{'I': 1, 'X': 10, 'L': 50, 'V': 5}</span>
</pre></div>

<p>Notice that <tt class="docutils literal">'L'</tt> was not added to the end of the output above.  Dictionaries
are unordered collections of key-value pairs.  When we print a dictionary, the
keys and values are rendered in some order, but as users of the language we
cannot predict what that order will be. The order may change when running a
program multiple times.</p>
<p>Dictionaries can appear in environment diagrams as well.</p>
<div class="example" data-output="False" data-showallframelabels="True" data-step="-1" id="example_9" style="">
numerals = {'I': 1, 'V': 5, 'X': 10}
numerals['L'] = 50
</div>
<script type="text/javascript">
var example_9_trace = {"code": "numerals = {'I': 1, 'V': 5, 'X': 10}\nnumerals['L'] = 50", "trace": [{"event": "step_line", "func_name": "<module>", "globals": {}, "heap": {}, "line": 1, "ordered_globals": [], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"numerals": ["REF", 1]}, "heap": {"1": ["DICT", ["I", 1], ["V", 5], ["X", 10]]}, "line": 2, "ordered_globals": ["numerals"], "stack_to_render": [], "stdout": ""}, {"event": "return", "func_name": "<module>", "globals": {"numerals": ["REF", 1]}, "heap": {"1": ["DICT", ["I", 1], ["L", 50], ["V", 5], ["X", 10]]}, "line": 2, "ordered_globals": ["numerals"], "stack_to_render": [], "stdout": ""}]}</script><p>The dictionary type also supports various methods of iterating over the
contents of the dictionary as a whole.  The methods <tt class="docutils literal">keys</tt>, <tt class="docutils literal">values</tt>, and
<tt class="docutils literal">items</tt> all return iterable values.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">sum</span><span class="p">(</span><span class="n">numerals</span><span class="o">.</span><span class="n">values</span><span class="p">())</span>
<span class="go">66</span>
</pre></div>

<p>A list of key-value pairs can be converted into a dictionary by calling the
<tt class="docutils literal">dict</tt> constructor function.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">dict</span><span class="p">([(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">9</span><span class="p">),</span> <span class="p">(</span><span class="mi">4</span><span class="p">,</span> <span class="mi">16</span><span class="p">),</span> <span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">25</span><span class="p">)])</span>
<span class="go">{3: 9, 4: 16, 5: 25}</span>
</pre></div>

<p>Dictionaries do have some restrictions:</p>
<ul class="simple">
<li>A key of a dictionary cannot be or contain a mutable value.</li>
<li>There can be at most one value for a given key.</li>
</ul>
<p>This first restriction is tied to the underlying implementation of dictionaries
in Python. The details of this implementation are not a topic of this text.
Intuitively, consider that the key tells Python where to find that key-value
pair in memory; if the key changes, the location of the pair may be lost.
Tuples are commonly used for keys in dictionaries because lists cannot be used.</p>
<p>The second restriction is a consequence of the dictionary abstraction, which is
designed to store and retrieve values for keys.  We can only retrieve <em>the</em>
value for a key if at most one such value exists in the dictionary.</p>
<p>A useful method implemented by dictionaries is <tt class="docutils literal">get</tt>, which returns either
the value for a key, if the key is present, or a default value.  The arguments
to <tt class="docutils literal">get</tt> are the key and the default value.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">numerals</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">'A'</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
<span class="go">0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">numerals</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s1">'V'</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
<span class="go">5</span>
</pre></div>

<p>Dictionaries also have a comprehension syntax analogous to those of lists.
A key expression and a value expression are separated by a colon. Evaluating a
dictionary comprehension creates a new dictionary object.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="p">{</span><span class="n">x</span><span class="p">:</span> <span class="n">x</span><span class="o">*</span><span class="n">x</span> <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="mi">6</span><span class="p">)}</span>
<span class="go">{3: 9, 4: 16, 5: 25}</span>
</pre></div>

</div>
<div class="section" id="local-state">
<h3>2.4.4   Local State</h3>
<p>Lists and dictionaries have <em>local state</em>: they are changing values that have
some particular contents at any point in the execution of a program. The word
"state" implies an evolving process in which that state may change.</p>
<p>Functions can also have local state.  For instance, let us define a function
that models the process of withdrawing money from a bank account. We will
create a function called <tt class="docutils literal">withdraw</tt>, which takes as its argument an amount to
be withdrawn. If there is enough money in the account to accommodate the
withdrawal, then <tt class="docutils literal">withdraw</tt> will return the balance remaining after the
withdrawal. Otherwise, <tt class="docutils literal">withdraw</tt> will return the message <tt class="docutils literal">'Insufficient
funds'</tt>.  For example, if we begin with $100 in the account, we would like to
obtain the following sequence of return values by calling withdraw:</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">withdraw</span><span class="p">(</span><span class="mi">25</span><span class="p">)</span>
<span class="go">75</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">withdraw</span><span class="p">(</span><span class="mi">25</span><span class="p">)</span>
<span class="go">50</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">withdraw</span><span class="p">(</span><span class="mi">60</span><span class="p">)</span>
<span class="go">'Insufficient funds'</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">withdraw</span><span class="p">(</span><span class="mi">15</span><span class="p">)</span>
<span class="go">35</span>
</pre></div>

<p>Above, the expression <tt class="docutils literal">withdraw(25)</tt>, evaluated twice, yields different
values. Thus, this user-defined function is non-pure. Calling the function not
only returns a value, but also has the side effect of changing the function in
some way, so that the next call with the same argument will return a different
result.  This side effect is a result of <tt class="docutils literal">withdraw</tt> making a change to a
name-value binding outside of the current frame.</p>
<p>For <tt class="docutils literal">withdraw</tt> to make sense, it must be created with an initial account
balance. The function <tt class="docutils literal">make_withdraw</tt> is a higher-order function that takes a
starting balance as an argument.  The function <tt class="docutils literal">withdraw</tt> is its return
value.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">withdraw</span> <span class="o">=</span> <span class="n">make_withdraw</span><span class="p">(</span><span class="mi">100</span><span class="p">)</span>
</pre></div>

<p>An implementation of <tt class="docutils literal">make_withdraw</tt> requires a new kind of statement: a
<tt class="docutils literal">nonlocal</tt> statement.  When we call <tt class="docutils literal">make_withdraw</tt>, we bind the name
<tt class="docutils literal">balance</tt> to the initial amount.  We then define and return a local function,
<tt class="docutils literal">withdraw</tt>, which updates and returns the value of <tt class="docutils literal">balance</tt> when called.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">make_withdraw</span><span class="p">(</span><span class="n">balance</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""Return a withdraw function that draws down balance with each call."""</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">withdraw</span><span class="p">(</span><span class="n">amount</span><span class="p">):</span>
<span class="gp">    </span>        <span class="k">nonlocal</span> <span class="n">balance</span>                 <span class="c1"># Declare the name "balance" nonlocal</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="n">amount</span> <span class="o">&gt;</span> <span class="n">balance</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="s1">'Insufficient funds'</span>
<span class="gp">    </span>        <span class="n">balance</span> <span class="o">=</span> <span class="n">balance</span> <span class="o">-</span> <span class="n">amount</span>       <span class="c1"># Re-bind the existing balance name</span>
<span class="gp">    </span>        <span class="k">return</span> <span class="n">balance</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">withdraw</span>
</pre></div>

<p>The <tt class="docutils literal">nonlocal</tt> statement declares that whenever we change the binding of the
name <tt class="docutils literal">balance</tt>, the binding is changed in the first frame in which
<tt class="docutils literal">balance</tt> is already bound. Recall that without the <tt class="docutils literal">nonlocal</tt> statement,
an assignment statement would always bind a name in the first frame of the
current environment.  The <tt class="docutils literal">nonlocal</tt> statement indicates that the name
appears somewhere in the environment other than the first (local) frame or the
last (global) frame.</p>
<p>The following environment diagrams illustrate the effects of multiple calls to
a function created by <tt class="docutils literal">make_withdraw</tt>.</p>
<div class="example" data-output="False" data-showallframelabels="True" data-step="5" id="example_10" style="">
def make_withdraw(balance):
    def withdraw(amount):
        nonlocal balance
        if amount &gt; balance:
            return 'Insufficient funds'
        balance = balance - amount
        return balance
    return withdraw

wd = make_withdraw(20)
wd(5)
wd(3)
</div>
<script type="text/javascript">
var example_10_trace = {"code": "def make_withdraw(balance):\n    def withdraw(amount):\n        nonlocal balance\n        if amount > balance:\n            return 'Insufficient funds'\n        balance = balance - amount\n        return balance\n    return withdraw\n\nwd = make_withdraw(20)\nwd(5)\nwd(3)", "trace": [{"event": "step_line", "func_name": "<module>", "globals": {}, "heap": {}, "line": 1, "ordered_globals": [], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 10, "ordered_globals": ["make_withdraw"], "stack_to_render": [], "stdout": ""}, {"event": "call", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 1, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 2, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 8, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p"}], "stdout": ""}, {"event": "return", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 8, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 11, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}], "stdout": ""}, {"event": "call", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 2, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 4, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 6, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "return", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 12, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}], "stdout": ""}, {"event": "call", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 2, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 4, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 6, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "return", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"__return__": 12, "amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "return", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 12, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"__return__": 12, "amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3_z"}], "stdout": ""}]}</script><p>The first def statement has the usual effect: it creates a new user-defined
function and binds the name <tt class="docutils literal">make_withdraw</tt> to that function in the global
frame. The subsequent call to <tt class="docutils literal">make_withdraw</tt> creates and returns a locally
defined function <tt class="docutils literal">withdraw</tt>.  The name <tt class="docutils literal">balance</tt> is bound in the parent
frame of this function.  Crucially, there will only be this single binding for
the name <tt class="docutils literal">balance</tt> throughout the rest of this example.</p>
<p>Next, we evaluate an expression that calls this function, bound to the name
<tt class="docutils literal">wd</tt>, on an amount 5.  The body of <tt class="docutils literal">withdraw</tt> is executed in a new
environment that extends the environment in which <tt class="docutils literal">withdraw</tt> was defined.
Tracing the effect of evaluating <tt class="docutils literal">withdraw</tt> illustrates the effect of a
<tt class="docutils literal">nonlocal</tt> statement in Python: a name outside of the first local frame can
be changed by an assignment statement.</p>
<div class="example" data-output="False" data-showallframelabels="True" data-step="10" id="example_11" style="">
def make_withdraw(balance):
    def withdraw(amount):
        nonlocal balance
        if amount &gt; balance:
            return 'Insufficient funds'
        balance = balance - amount
        return balance
    return withdraw

wd = make_withdraw(20)
wd(5)
wd(3)
</div>
<script type="text/javascript">
var example_11_trace = {"code": "def make_withdraw(balance):\n    def withdraw(amount):\n        nonlocal balance\n        if amount > balance:\n            return 'Insufficient funds'\n        balance = balance - amount\n        return balance\n    return withdraw\n\nwd = make_withdraw(20)\nwd(5)\nwd(3)", "trace": [{"event": "step_line", "func_name": "<module>", "globals": {}, "heap": {}, "line": 1, "ordered_globals": [], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 10, "ordered_globals": ["make_withdraw"], "stack_to_render": [], "stdout": ""}, {"event": "call", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 1, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 2, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 8, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p"}], "stdout": ""}, {"event": "return", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 8, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 11, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}], "stdout": ""}, {"event": "call", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 2, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 4, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 6, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "return", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 12, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}], "stdout": ""}, {"event": "call", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 2, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 4, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 6, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "return", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"__return__": 12, "amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "return", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 12, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"__return__": 12, "amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3_z"}], "stdout": ""}]}</script><p>The <tt class="docutils literal">nonlocal</tt> statement changes all of the remaining assignment statements
in the definition of <tt class="docutils literal">withdraw</tt>.  After executing <tt class="docutils literal">nonlocal balance</tt>, any
assignment statement with <tt class="docutils literal">balance</tt> on the left-hand side of <tt class="docutils literal">=</tt> will not
bind <tt class="docutils literal">balance</tt> in the first frame of the current environment.  Instead, it
will find the first frame in which <tt class="docutils literal">balance</tt> was already defined and re-bind
the name in that frame.  If <tt class="docutils literal">balance</tt> has not previously been bound to a
value, then the <tt class="docutils literal">nonlocal</tt> statement will give an error.</p>
<p>By virtue of changing the binding for <tt class="docutils literal">balance</tt>, we have changed the
<tt class="docutils literal">withdraw</tt> function as well.  The next time it is called, the name
<tt class="docutils literal">balance</tt> will evaluate to 15 instead of 20.  Hence, when we call
<tt class="docutils literal">withdraw</tt> a second time, we see that its return value is 12 and not
17.  The change to <tt class="docutils literal">balance</tt> from the first call affects the result of
the second call.</p>
<div class="example" data-output="False" data-showallframelabels="True" data-step="-1" id="example_12" style="">
def make_withdraw(balance):
    def withdraw(amount):
        nonlocal balance
        if amount &gt; balance:
            return 'Insufficient funds'
        balance = balance - amount
        return balance
    return withdraw

wd = make_withdraw(20)
wd(5)
wd(3)
</div>
<script type="text/javascript">
var example_12_trace = {"code": "def make_withdraw(balance):\n    def withdraw(amount):\n        nonlocal balance\n        if amount > balance:\n            return 'Insufficient funds'\n        balance = balance - amount\n        return balance\n    return withdraw\n\nwd = make_withdraw(20)\nwd(5)\nwd(3)", "trace": [{"event": "step_line", "func_name": "<module>", "globals": {}, "heap": {}, "line": 1, "ordered_globals": [], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 10, "ordered_globals": ["make_withdraw"], "stack_to_render": [], "stdout": ""}, {"event": "call", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 1, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 2, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 8, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p"}], "stdout": ""}, {"event": "return", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 8, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 11, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}], "stdout": ""}, {"event": "call", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 2, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 4, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 6, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "return", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 12, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}], "stdout": ""}, {"event": "call", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 2, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 4, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 6, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 15, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "return", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"__return__": 12, "amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "return", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 12, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 15, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"__return__": 12, "amount": 3}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3_z"}], "stdout": ""}]}</script><p>The second call to <tt class="docutils literal">withdraw</tt> does create a second local frame, as usual.
However, both <tt class="docutils literal">withdraw</tt> frames have the same parent.  That is, they both
extend the environment for <tt class="docutils literal">make_withdraw</tt>, which contains the binding for
<tt class="docutils literal">balance</tt>.  Hence, they share that particular name binding.  Calling
<tt class="docutils literal">withdraw</tt> has the side effect of altering the environment that will be
extended by future calls to <tt class="docutils literal">withdraw</tt>.  The <tt class="docutils literal">nonlocal</tt> statement allows
<tt class="docutils literal">withdraw</tt> to change a name binding in the <tt class="docutils literal">make_withdraw</tt> frame.</p>
<p>Ever since we first encountered nested <tt class="docutils literal">def</tt> statements, we have observed
that a locally defined function can look up names outside of its local frames.
No <tt class="docutils literal">nonlocal</tt> statement is required to <em>access</em> a non-local name.  By
contrast, only after a <tt class="docutils literal">nonlocal</tt> statement can a function <em>change</em> the
binding of names in these frames.</p>
<p>By introducing <tt class="docutils literal">nonlocal</tt> statements, we have created a dual role for
assignment statements.  Either they change local bindings, or they change
nonlocal bindings.  In fact, assignment statements already had a dual role:
they either created new bindings or re-bound existing names. Assignment can
also change the contents of lists and dictionaries. The many roles of Python
assignment can obscure the effects of executing an assignment statement. It is
up to you as a programmer to document your code clearly so that the effects of
assignment can be understood by others.</p>
<p><strong>Python Particulars.</strong> This pattern of non-local assignment is a general
feature of programming languages with higher-order functions and lexical scope.
Most other languages do not require a <tt class="docutils literal">nonlocal</tt> statement at all. Instead,
non-local assignment is often the default behavior of assignment statements.</p>
<p>Python also has an unusual restriction regarding the lookup of names: within
the body of a function, all instances of a name must refer to the same frame.
As a result, Python cannot look up the value of a name in a non-local frame,
then bind that same name in the local frame, because the same name would be
accessed in two different frames in the same function.  This restriction
allows Python to pre-compute which frame contains each name before executing
the body of a function. When this restriction is violated, a confusing error
message results.  To demonstrate, the <tt class="docutils literal">make_withdraw</tt> example is repeated
below with the <tt class="docutils literal">nonlocal</tt> statement removed.</p>
<div class="example" data-output="False" data-showallframelabels="True" data-step="7" id="example_13" style="">
def make_withdraw(balance):
    def withdraw(amount):
        if amount &gt; balance:
            return 'Insufficient funds'
        balance = balance - amount
        return balance
    return withdraw

wd = make_withdraw(20)
wd(5)
</div>
<script type="text/javascript">
var example_13_trace = {"code": "def make_withdraw(balance):\n    def withdraw(amount):\n        if amount > balance:\n            return 'Insufficient funds'\n        balance = balance - amount\n        return balance\n    return withdraw\n\nwd = make_withdraw(20)\nwd(5)", "trace": [{"event": "step_line", "func_name": "<module>", "globals": {}, "heap": {}, "line": 1, "ordered_globals": [], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 9, "ordered_globals": ["make_withdraw"], "stack_to_render": [], "stdout": ""}, {"event": "call", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 1, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 2, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p"}], "stdout": ""}, {"event": "return", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 10, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}], "stdout": ""}, {"event": "call", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 2, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 3, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "exception", "exception_msg": "UnboundLocalError: local variable 'balance' referenced before assignment", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 3, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "return", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 3, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": null, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "exception", "exception_msg": "UnboundLocalError: local variable 'balance' referenced before assignment", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 10, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": null, "amount": 5}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}], "stdout": ""}]}</script><p>This <tt class="docutils literal">UnboundLocalError</tt> appears because <tt class="docutils literal">balance</tt> is assigned locally in
line 5, and so Python assumes that all references to <tt class="docutils literal">balance</tt> must
appear in the local frame as well.  This error occurs <em>before</em> line 5 is
ever executed, implying that Python has considered line 5 in some way
before executing line 3. As we study interpreter design, we will see that
pre-computing facts about a function body before executing it is quite common.
In this case, Python's pre-processing restricted the frame in which <tt class="docutils literal">balance</tt>
could appear, and thus prevented the name from being found. Adding a
<tt class="docutils literal">nonlocal</tt> statement corrects this error. The <tt class="docutils literal">nonlocal</tt> statement did not
exist in Python 2.</p>
</div>
<div class="section" id="the-benefits-of-non-local-assignment">
<h3>2.4.5   The Benefits of Non-Local Assignment</h3>
<p>Non-local assignment is an important step on our path to viewing a program as a
collection of independent and autonomous <em>objects</em>, which interact with each
other but each manage their own internal state.</p>
<p>In particular, non-local assignment has given us the ability to maintain some
state that is local to a function, but evolves over successive calls to that
function.  The <tt class="docutils literal">balance</tt> associated with a particular withdraw function is
shared among all calls to that function. However, the binding for balance
associated with an instance of withdraw is inaccessible to the rest of the
program.  Only <tt class="docutils literal">wd</tt> is associated with the frame for <tt class="docutils literal">make_withdraw</tt> in
which it was defined.  If <tt class="docutils literal">make_withdraw</tt> is called again, then it will
create a separate frame with a separate binding for <tt class="docutils literal">balance</tt>.</p>
<p>We can extend our example to illustrate this point.  A second call to
<tt class="docutils literal">make_withdraw</tt> returns a second <tt class="docutils literal">withdraw</tt> function that has a different
parent. We bind this second function to the name <tt class="docutils literal">wd2</tt> in the global frame.</p>
<div class="example" data-output="False" data-showallframelabels="True" data-step="9" id="example_14" style="">
def make_withdraw(balance):
    def withdraw(amount):
        nonlocal balance
        if amount &gt; balance:
            return 'Insufficient funds'
        balance = balance - amount
        return balance
    return withdraw

wd = make_withdraw(20)
wd2 = make_withdraw(7)
wd2(6)
wd(8)
</div>
<script type="text/javascript">
var example_14_trace = {"code": "def make_withdraw(balance):\n    def withdraw(amount):\n        nonlocal balance\n        if amount > balance:\n            return 'Insufficient funds'\n        balance = balance - amount\n        return balance\n    return withdraw\n\nwd = make_withdraw(20)\nwd2 = make_withdraw(7)\nwd2(6)\nwd(8)", "trace": [{"event": "step_line", "func_name": "<module>", "globals": {}, "heap": {}, "line": 1, "ordered_globals": [], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 10, "ordered_globals": ["make_withdraw"], "stack_to_render": [], "stdout": ""}, {"event": "call", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 1, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 2, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 8, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p"}], "stdout": ""}, {"event": "return", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 8, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 11, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}], "stdout": ""}, {"event": "call", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 1, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"balance": 7}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 2, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"balance": 7}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 8, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"balance": 7, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p"}], "stdout": ""}, {"event": "return", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 8, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 7, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 12, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 7, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}], "stdout": ""}, {"event": "call", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 2, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 7, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 4, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 7, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 6, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 7, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 7, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "return", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 7, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"__return__": 1, "amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 13, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"__return__": 1, "amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3_z"}], "stdout": ""}, {"event": "call", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 2, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"__return__": 1, "amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3_z"}, {"encoded_locals": {"amount": 8}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f4"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 4, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"__return__": 1, "amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3_z"}, {"encoded_locals": {"amount": 8}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f4"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 6, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"__return__": 1, "amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3_z"}, {"encoded_locals": {"amount": 8}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f4"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 7, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"__return__": 1, "amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3_z"}, {"encoded_locals": {"amount": 8}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f4"}], "stdout": ""}, {"event": "return", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 7, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"__return__": 1, "amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3_z"}, {"encoded_locals": {"__return__": 12, "amount": 8}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f4"}], "stdout": ""}, {"event": "return", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 13, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"__return__": 1, "amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3_z"}, {"encoded_locals": {"__return__": 12, "amount": 8}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f4_z"}], "stdout": ""}]}</script><p>Now, we see that there are in fact two bindings for the name <tt class="docutils literal">balance</tt> in two
different frames, and each <tt class="docutils literal">withdraw</tt> function has a different parent. The
name <tt class="docutils literal">wd</tt> is bound to a function with a balance of 20, while
<tt class="docutils literal">wd2</tt> is bound to a different function with a balance of 7.</p>
<p>Calling <tt class="docutils literal">wd2</tt> changes the binding of its non-local <tt class="docutils literal">balance</tt> name, but
does not affect the function bound to the name <tt class="docutils literal">withdraw</tt>. A future call to
<tt class="docutils literal">wd</tt> is unaffected by the changing balance of <tt class="docutils literal">wd2</tt>; its balance is still
20.</p>
<div class="example" data-output="False" data-showallframelabels="True" data-step="14" id="example_15" style="">
def make_withdraw(balance):
    def withdraw(amount):
        nonlocal balance
        if amount &gt; balance:
            return 'Insufficient funds'
        balance = balance - amount
        return balance
    return withdraw

wd = make_withdraw(20)
wd2 = make_withdraw(7)
wd2(6)
wd(8)
</div>
<script type="text/javascript">
var example_15_trace = {"code": "def make_withdraw(balance):\n    def withdraw(amount):\n        nonlocal balance\n        if amount > balance:\n            return 'Insufficient funds'\n        balance = balance - amount\n        return balance\n    return withdraw\n\nwd = make_withdraw(20)\nwd2 = make_withdraw(7)\nwd2(6)\nwd(8)", "trace": [{"event": "step_line", "func_name": "<module>", "globals": {}, "heap": {}, "line": 1, "ordered_globals": [], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 10, "ordered_globals": ["make_withdraw"], "stack_to_render": [], "stdout": ""}, {"event": "call", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 1, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 2, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 8, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p"}], "stdout": ""}, {"event": "return", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 8, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 11, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}], "stdout": ""}, {"event": "call", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 1, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"balance": 7}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 2, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"balance": 7}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 8, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"balance": 7, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p"}], "stdout": ""}, {"event": "return", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 8, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 7, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 12, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 7, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}], "stdout": ""}, {"event": "call", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 2, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 7, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 4, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 7, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 6, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 7, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 7, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "return", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 7, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"__return__": 1, "amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 13, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"__return__": 1, "amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3_z"}], "stdout": ""}, {"event": "call", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 2, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"__return__": 1, "amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3_z"}, {"encoded_locals": {"amount": 8}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f4"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 4, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"__return__": 1, "amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3_z"}, {"encoded_locals": {"amount": 8}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f4"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 6, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 20, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"__return__": 1, "amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3_z"}, {"encoded_locals": {"amount": 8}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f4"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 7, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"__return__": 1, "amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3_z"}, {"encoded_locals": {"amount": 8}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f4"}], "stdout": ""}, {"event": "return", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 7, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"__return__": 1, "amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3_z"}, {"encoded_locals": {"__return__": 12, "amount": 8}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f4"}], "stdout": ""}, {"event": "return", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "line": 13, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": ["REF", 3], "balance": 1, "withdraw": ["REF", 3]}, "frame_id": 2, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f2_p_z"}, {"encoded_locals": {"__return__": 1, "amount": 6}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [2], "unique_hash": "withdraw_f3_z"}, {"encoded_locals": {"__return__": 12, "amount": 8}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f4_z"}], "stdout": ""}]}</script><p>In this way, each instance of <tt class="docutils literal">withdraw</tt> maintains its own balance state,
but that state is inaccessible to any other function in the program.  Viewing
this situation at a higher level, we have created an abstraction of a bank
account that manages its own internals but behaves in a way that models accounts
in the world: it changes over time based on its own history of withdrawal
requests.</p>
</div>
<div class="section" id="the-cost-of-non-local-assignment">
<h3>2.4.6   The Cost of Non-Local Assignment</h3>
<p>Our environment model of computation cleanly extends to explain the effects of
non-local assignment.  However, non-local assignment introduces some important
nuances in the way we think about names and values.</p>
<p>Previously, our values did not change; only our names and bindings changed.
When two names <tt class="docutils literal">a</tt> and <tt class="docutils literal">b</tt> were both bound to the value 4, it did not
matter whether they were bound to the same 4 or different 4's.  As far
as we could tell, there was only one 4 object that never changed.</p>
<p>However, functions with state do not behave this way.  When two names <tt class="docutils literal">wd</tt>
and <tt class="docutils literal">wd2</tt> are both bound to a <tt class="docutils literal">withdraw</tt> function, it <em>does</em> matter whether
they are bound to the same function or different instances of that function.
Consider the following example, which contrasts the one we just analyzed.
In this case, calling the function named by <tt class="docutils literal">wd2</tt> did change the value of the
function named by <tt class="docutils literal">wd</tt>, because both names refer to the same function.</p>
<div class="example" data-output="False" data-showallframelabels="True" data-step="-1" id="example_16" style="">
def make_withdraw(balance):
    def withdraw(amount):
        nonlocal balance
        if amount &gt; balance:
            return 'Insufficient funds'
        balance = balance - amount
        return balance
    return withdraw

wd = make_withdraw(12)
wd2 = wd
wd2(1)
wd(1)
</div>
<script type="text/javascript">
var example_16_trace = {"code": "def make_withdraw(balance):\n    def withdraw(amount):\n        nonlocal balance\n        if amount > balance:\n            return 'Insufficient funds'\n        balance = balance - amount\n        return balance\n    return withdraw\n\nwd = make_withdraw(12)\nwd2 = wd\nwd2(1)\nwd(1)", "trace": [{"event": "step_line", "func_name": "<module>", "globals": {}, "heap": {}, "line": 1, "ordered_globals": [], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 10, "ordered_globals": ["make_withdraw"], "stack_to_render": [], "stdout": ""}, {"event": "call", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 1, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 12}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "line": 2, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 12}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["balance"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1"}], "stdout": ""}, {"event": "step_line", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 8, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p"}], "stdout": ""}, {"event": "return", "func_name": "make_withdraw", "globals": {"make_withdraw": ["REF", 1]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 8, "ordered_globals": ["make_withdraw"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 11, "ordered_globals": ["make_withdraw", "wd"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 12, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}], "stdout": ""}, {"event": "call", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 2, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 1}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 4, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 1}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 6, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 12, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 1}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 11, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"amount": 1}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "return", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 11, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 11, "amount": 1}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 13, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 11, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 11, "amount": 1}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}], "stdout": ""}, {"event": "call", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 2, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 11, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 11, "amount": 1}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"amount": 1}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 4, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 11, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 11, "amount": 1}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"amount": 1}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 6, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 11, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 11, "amount": 1}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"amount": 1}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 10, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 11, "amount": 1}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"amount": 1}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "return", "func_name": "withdraw", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 7, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 10, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 11, "amount": 1}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"__return__": 10, "amount": 1}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3"}], "stdout": ""}, {"event": "return", "func_name": "<module>", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "line": 13, "ordered_globals": ["make_withdraw", "wd", "wd2"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 2], "balance": 10, "withdraw": ["REF", 2]}, "frame_id": 1, "func_name": "make_withdraw", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["balance", "withdraw", "__return__"], "parent_frame_id_list": [], "unique_hash": "make_withdraw_f1_p_z"}, {"encoded_locals": {"__return__": 11, "amount": 1}, "frame_id": 2, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f2_z"}, {"encoded_locals": {"__return__": 10, "amount": 1}, "frame_id": 3, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f3_z"}], "stdout": ""}]}</script><p>It is not unusual for two names to co-refer to the same value in the world, and
so it is in our programs.  But, as values change over time, we must be very
careful to understand the effect of a change on other names that might refer to
those values.</p>
<p>The key to correctly analyzing code with non-local assignment is to remember
that only function calls can introduce new frames.  Assignment statements always
change bindings in existing frames.  In this case, unless <tt class="docutils literal">make_withdraw</tt> is
called twice, there can be only one binding for <tt class="docutils literal">balance</tt>.</p>
<p><strong>Sameness and change.</strong> These subtleties arise because, by introducing non-pure
functions that change the non-local environment, we have changed the nature of
expressions.  An expression that contains only pure function calls is
<em>referentially transparent</em>; its value does not change if we substitute one of
its subexpression with the value of that subexpression.</p>
<p>Re-binding operations violate the conditions of referential transparency because
they do more than return a value; they change the environment.  When we
introduce arbitrary re-binding, we encounter a thorny epistemological issue:
what it means for two values to be the same. In our environment model of
computation, two separately defined functions are not the same, because changes
to one may not be reflected in the other.</p>
<p>In general, so long as we never modify data objects, we can regard a compound
data object to be precisely the totality of its pieces. For example, a rational
number is determined by giving its numerator and its denominator. But this view
is no longer valid in the presence of change, where a compound data object has
an "identity" that is something different from the pieces of which it is
composed. A bank account is still "the same" bank account even if we change the
balance by making a withdrawal; conversely, we could have two bank accounts that
happen to have the same balance, but are different objects.</p>
<p>Despite the complications it introduces, non-local assignment is a powerful tool
for creating modular programs. Different parts of a program, which correspond to
different environment frames, can evolve separately throughout program
execution.  Moreover, using functions with local state, we are able to implement
mutable data types.  In fact, we can implement abstract data types that are
equivalent to the built-in <tt class="docutils literal">list</tt> and <tt class="docutils literal">dict</tt> types introduced above.</p>
</div>
<div class="section" id="implementing-lists-and-dictionaries">
<h3>2.4.7   Implementing Lists and Dictionaries</h3>
<p>The Python language does not give us access to the implementation of lists,
only to the sequence abstraction and mutation methods built into the language.
To understand how a mutable list could be represented using functions with
local state, we will now develop an implementation of a mutable linked list.</p>
<p>We will represent a mutable linked list by a function that has a linked list as
its local state.  Lists need to have an identity, like any mutable value.  In
particular, we cannot use <tt class="docutils literal">None</tt> to represent an empty mutable list, because
two empty lists are not identical values (e.g., appending to one does not
append to the other), but <tt class="docutils literal">None is None</tt>.  On the other hand, two different
functions that each have <tt class="docutils literal">empty</tt> as their local state will suffice to
distinguish two empty lists.</p>
<p>If a mutable linked list is a function, what arguments does it take? The answer
exhibits a general pattern in programming: the function is a dispatch function
and its arguments are first a message, followed by additional arguments to
parameterize that method. This message is a string naming what the function
should do. Dispatch functions are effectively many functions in one: the
message determines the behavior of the function, and the additional arguments
are used in that behavior.</p>
<p>Our mutable list will respond to five different messages: <tt class="docutils literal">len</tt>, <tt class="docutils literal">getitem</tt>,
<tt class="docutils literal">push_first</tt>, <tt class="docutils literal">pop_first</tt>, and <tt class="docutils literal">str</tt>.  The first two implement the
behaviors of the sequence abstraction.  The next two add or remove the first
element of the list.  The final message returns a string representation of the
whole linked list.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">mutable_link</span><span class="p">():</span>
<span class="gp">    </span>    <span class="sd">"""Return a functional implementation of a mutable linked list."""</span>
<span class="gp">    </span>    <span class="n">contents</span> <span class="o">=</span> <span class="n">empty</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">dispatch</span><span class="p">(</span><span class="n">message</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="gp">    </span>        <span class="k">nonlocal</span> <span class="n">contents</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="n">message</span> <span class="o">==</span> <span class="s1">'len'</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="n">len_link</span><span class="p">(</span><span class="n">contents</span><span class="p">)</span>
<span class="gp">    </span>        <span class="k">elif</span> <span class="n">message</span> <span class="o">==</span> <span class="s1">'getitem'</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="n">getitem_link</span><span class="p">(</span><span class="n">contents</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
<span class="gp">    </span>        <span class="k">elif</span> <span class="n">message</span> <span class="o">==</span> <span class="s1">'push_first'</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">contents</span> <span class="o">=</span> <span class="n">link</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">contents</span><span class="p">)</span>
<span class="gp">    </span>        <span class="k">elif</span> <span class="n">message</span> <span class="o">==</span> <span class="s1">'pop_first'</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">f</span> <span class="o">=</span> <span class="n">first</span><span class="p">(</span><span class="n">contents</span><span class="p">)</span>
<span class="gp">    </span>            <span class="n">contents</span> <span class="o">=</span> <span class="n">rest</span><span class="p">(</span><span class="n">contents</span><span class="p">)</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="n">f</span>
<span class="gp">    </span>        <span class="k">elif</span> <span class="n">message</span> <span class="o">==</span> <span class="s1">'str'</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="n">join_link</span><span class="p">(</span><span class="n">contents</span><span class="p">,</span> <span class="s2">", "</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">dispatch</span>
</pre></div>

<p>We can also add a convenience function to construct a functionally implemented
linked list from any built-in sequence, simply by adding each element in
reverse order.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">to_mutable_link</span><span class="p">(</span><span class="n">source</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""Return a functional list with the same contents as source."""</span>
<span class="gp">    </span>    <span class="n">s</span> <span class="o">=</span> <span class="n">mutable_link</span><span class="p">()</span>
<span class="gp">    </span>    <span class="k">for</span> <span class="n">element</span> <span class="ow">in</span> <span class="nb">reversed</span><span class="p">(</span><span class="n">source</span><span class="p">):</span>
<span class="gp">    </span>        <span class="n">s</span><span class="p">(</span><span class="s1">'push_first'</span><span class="p">,</span> <span class="n">element</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">s</span>
</pre></div>

<p>In the definition above, the function <tt class="docutils literal">reversed</tt> takes and returns an
iterable value; it is another example of a function that processes sequences.</p>
<p>At this point, we can construct a functionally implemented mutable linked
lists.  Note that the linked list itself is a function.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="n">to_mutable_link</span><span class="p">(</span><span class="n">suits</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="n">s</span><span class="p">)</span>
<span class="go">&lt;class 'function'&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">s</span><span class="p">(</span><span class="s1">'str'</span><span class="p">))</span>
<span class="go">heart, diamond, spade, club</span>
</pre></div>

<p>In addition, we can pass messages to the list <tt class="docutils literal">s</tt> that change its contents,
for instance removing the first element.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">s</span><span class="p">(</span><span class="s1">'pop_first'</span><span class="p">)</span>
<span class="go">'heart'</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">print</span><span class="p">(</span><span class="n">s</span><span class="p">(</span><span class="s1">'str'</span><span class="p">))</span>
<span class="go">diamond, spade, club</span>
</pre></div>

<p>In principle, the operations <tt class="docutils literal">push_first</tt> and <tt class="docutils literal">pop_first</tt> suffice to make
arbitrary changes to a list.  We can always empty out the list entirely and then
replace its old contents with the desired result.</p>
<p><strong>Message passing.</strong> Given some time, we could implement the many useful
mutation operations of Python lists, such as <tt class="docutils literal">extend</tt> and <tt class="docutils literal">insert</tt>.  We
would have a choice: we could implement them all as functions, which use the
existing messages <tt class="docutils literal">pop_first</tt> and <tt class="docutils literal">push_first</tt> to make all changes.
Alternatively, we could add additional <tt class="docutils literal">elif</tt> clauses to the body of
<tt class="docutils literal">dispatch</tt>, each checking for a message (e.g.,  <tt class="docutils literal">'extend'</tt>) and applying the
appropriate change to <tt class="docutils literal">contents</tt> directly.</p>
<p>This second approach, which encapsulates the logic for all operations on a data
value within one function that responds to different messages, is a discipline
called message passing.  A program that uses message passing defines dispatch
functions, each of which may have local state, and organizes computation by
passing "messages" as the first argument to those functions.  The messages are
strings that correspond to particular behaviors.</p>
<p><strong>Implementing Dictionaries.</strong> We can also implement a value with similar
behavior to a dictionary. In this case, we use a list of key-value pairs to
store the contents of the dictionary. Each pair is a two-element list.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">dictionary</span><span class="p">():</span>
<span class="gp">    </span>    <span class="sd">"""Return a functional implementation of a dictionary."""</span>
<span class="gp">    </span>    <span class="n">records</span> <span class="o">=</span> <span class="p">[]</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">getitem</span><span class="p">(</span><span class="n">key</span><span class="p">):</span>
<span class="gp">    </span>        <span class="n">matches</span> <span class="o">=</span> <span class="p">[</span><span class="n">r</span> <span class="k">for</span> <span class="n">r</span> <span class="ow">in</span> <span class="n">records</span> <span class="k">if</span> <span class="n">r</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">==</span> <span class="n">key</span><span class="p">]</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">matches</span><span class="p">)</span> <span class="o">==</span> <span class="mi">1</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">key</span><span class="p">,</span> <span class="n">value</span> <span class="o">=</span> <span class="n">matches</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="n">value</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">setitem</span><span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
<span class="gp">    </span>        <span class="k">nonlocal</span> <span class="n">records</span>
<span class="gp">    </span>        <span class="n">non_matches</span> <span class="o">=</span> <span class="p">[</span><span class="n">r</span> <span class="k">for</span> <span class="n">r</span> <span class="ow">in</span> <span class="n">records</span> <span class="k">if</span> <span class="n">r</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">!=</span> <span class="n">key</span><span class="p">]</span>
<span class="gp">    </span>        <span class="n">records</span> <span class="o">=</span> <span class="n">non_matches</span> <span class="o">+</span> <span class="p">[[</span><span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">]]</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">dispatch</span><span class="p">(</span><span class="n">message</span><span class="p">,</span> <span class="n">key</span><span class="o">=</span><span class="kc">None</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="n">message</span> <span class="o">==</span> <span class="s1">'getitem'</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="n">getitem</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>
<span class="gp">    </span>        <span class="k">elif</span> <span class="n">message</span> <span class="o">==</span> <span class="s1">'setitem'</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">setitem</span><span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">dispatch</span>
</pre></div>

<p>Again, we use the message passing method to organize our implementation.  We
have supported two messages: <tt class="docutils literal">getitem</tt> and <tt class="docutils literal">setitem</tt>.   To insert a value
for a key, we filter out any existing records with the given key, then add one.
In this way, we are assured that each key appears only once in records. To look
up a value for a key, we filter for the record that matches the given key.   We
can now use our implementation to store and retrieve values.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">d</span> <span class="o">=</span> <span class="n">dictionary</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">d</span><span class="p">(</span><span class="s1">'setitem'</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">9</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">d</span><span class="p">(</span><span class="s1">'setitem'</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">16</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">d</span><span class="p">(</span><span class="s1">'getitem'</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
<span class="go">9</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">d</span><span class="p">(</span><span class="s1">'getitem'</span><span class="p">,</span> <span class="mi">4</span><span class="p">)</span>
<span class="go">16</span>
</pre></div>

<p>This implementation of a dictionary is <em>not</em> optimized for fast record lookup,
because each call must filter through all records. The built-in dictionary type
is considerably more efficient. The way in which it is implemented is beyond
the scope of this text.</p>
</div>
<div class="section" id="dispatch-dictionaries">
<h3>2.4.8   Dispatch Dictionaries</h3>
<p>The dispatch function is a general method for implementing a message passing
interface for abstract data. To implement message dispatch, we have thus far
used conditional statements to compare the message string to a fixed set of
known messages.</p>
<p>The built-in dictionary data type provides a general method for looking up a
value for a key. Instead of using conditionals to implement dispatching, we can
use dictionaries with string keys.</p>
<p>The mutable <tt class="docutils literal">account</tt> data type below is implemented as a dictionary.  It
has a constructor <tt class="docutils literal">account</tt> and selector <tt class="docutils literal">check_balance</tt>, as well as
functions to <tt class="docutils literal">deposit</tt> or <tt class="docutils literal">withdraw</tt> funds. Moreover, the local state of
the account is stored in the dictionary alongside the functions that implement
its behavior.</p>
<div class="example" data-output="False" data-showallframelabels="True" data-step="12" id="example_17" style="">
def account(initial_balance):
    def deposit(amount):
        dispatch['balance'] += amount
        return dispatch['balance']
    def withdraw(amount):
        if amount &gt; dispatch['balance']:
            return 'Insufficient funds'
        dispatch['balance'] -= amount
        return dispatch['balance']
    dispatch = {'deposit':   deposit,
                'withdraw':  withdraw,
                'balance':   initial_balance}
    return dispatch

def withdraw(account, amount):
    return account['withdraw'](amount)
def deposit(account, amount):
    return account['deposit'](amount)
def check_balance(account):
    return account['balance']

a = account(20)
deposit(a, 5)
withdraw(a, 17)
check_balance(a)
</div>
<script type="text/javascript">
var example_17_trace = {"code": "def account(initial_balance):\n    def deposit(amount):\n        dispatch['balance'] += amount\n        return dispatch['balance']\n    def withdraw(amount):\n        if amount > dispatch['balance']:\n            return 'Insufficient funds'\n        dispatch['balance'] -= amount\n        return dispatch['balance']\n    dispatch = {'deposit':   deposit,\n                'withdraw':  withdraw,\n                'balance':   initial_balance}\n    return dispatch\n\ndef withdraw(account, amount):\n    return account['withdraw'](amount)\ndef deposit(account, amount):\n    return account['deposit'](amount)\ndef check_balance(account):\n    return account['balance']\n\na = account(20)\ndeposit(a, 5)\nwithdraw(a, 17)\ncheck_balance(a)", "trace": [{"event": "step_line", "func_name": "<module>", "globals": {}, "heap": {}, "line": 1, "ordered_globals": [], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"account": ["REF", 1]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null]}, "line": 15, "ordered_globals": ["account"], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"account": ["REF", 1], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null]}, "line": 17, "ordered_globals": ["account", "withdraw"], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"account": ["REF", 1], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null]}, "line": 19, "ordered_globals": ["account", "withdraw", "deposit"], "stack_to_render": [], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null]}, "line": 22, "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "stack_to_render": [], "stdout": ""}, {"event": "call", "func_name": "account", "globals": {"account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null]}, "line": 1, "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "stack_to_render": [{"encoded_locals": {"initial_balance": 20}, "frame_id": 1, "func_name": "account", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["initial_balance"], "parent_frame_id_list": [], "unique_hash": "account_f1"}], "stdout": ""}, {"event": "step_line", "func_name": "account", "globals": {"account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null]}, "line": 2, "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "stack_to_render": [{"encoded_locals": {"initial_balance": 20}, "frame_id": 1, "func_name": "account", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["initial_balance"], "parent_frame_id_list": [], "unique_hash": "account_f1"}], "stdout": ""}, {"event": "step_line", "func_name": "account", "globals": {"account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1]}, "line": 5, "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "stack_to_render": [{"encoded_locals": {"deposit": ["REF", 5], "initial_balance": 20}, "frame_id": 1, "func_name": "account", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["initial_balance", "deposit"], "parent_frame_id_list": [], "unique_hash": "account_f1_p"}], "stdout": ""}, {"event": "step_line", "func_name": "account", "globals": {"account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1]}, "line": 10, "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "stack_to_render": [{"encoded_locals": {"deposit": ["REF", 5], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["initial_balance", "deposit", "withdraw"], "parent_frame_id_list": [], "unique_hash": "account_f1_p"}], "stdout": ""}, {"event": "step_line", "func_name": "account", "globals": {"account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1]}, "line": 11, "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "stack_to_render": [{"encoded_locals": {"deposit": ["REF", 5], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["initial_balance", "deposit", "withdraw"], "parent_frame_id_list": [], "unique_hash": "account_f1_p"}], "stdout": ""}, {"event": "step_line", "func_name": "account", "globals": {"account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1]}, "line": 12, "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "stack_to_render": [{"encoded_locals": {"deposit": ["REF", 5], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["initial_balance", "deposit", "withdraw"], "parent_frame_id_list": [], "unique_hash": "account_f1_p"}], "stdout": ""}, {"event": "step_line", "func_name": "account", "globals": {"account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 20], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 13, "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "stack_to_render": [{"encoded_locals": {"deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch"], "parent_frame_id_list": [], "unique_hash": "account_f1_p"}], "stdout": ""}, {"event": "return", "func_name": "account", "globals": {"account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 20], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 13, "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": true, "is_parent": true, "is_zombie": false, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 20], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 23, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}], "stdout": ""}, {"event": "call", "func_name": "deposit", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 20], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 17, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account", "amount"], "parent_frame_id_list": [], "unique_hash": "deposit_f2"}], "stdout": ""}, {"event": "step_line", "func_name": "deposit", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 20], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 18, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account", "amount"], "parent_frame_id_list": [], "unique_hash": "deposit_f2"}], "stdout": ""}, {"event": "call", "func_name": "deposit", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 20], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 2, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account", "amount"], "parent_frame_id_list": [], "unique_hash": "deposit_f2"}, {"encoded_locals": {"amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "deposit", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 20], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 3, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account", "amount"], "parent_frame_id_list": [], "unique_hash": "deposit_f2"}, {"encoded_locals": {"amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3"}], "stdout": ""}, {"event": "step_line", "func_name": "deposit", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 4, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account", "amount"], "parent_frame_id_list": [], "unique_hash": "deposit_f2"}, {"encoded_locals": {"amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3"}], "stdout": ""}, {"event": "return", "func_name": "deposit", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 4, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account", "amount"], "parent_frame_id_list": [], "unique_hash": "deposit_f2"}, {"encoded_locals": {"__return__": 25, "amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3"}], "stdout": ""}, {"event": "return", "func_name": "deposit", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 18, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"__return__": 25, "account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "deposit_f2"}, {"encoded_locals": {"__return__": 25, "amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3_z"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 24, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"__return__": 25, "account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "deposit_f2_z"}, {"encoded_locals": {"__return__": 25, "amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3_z"}], "stdout": ""}, {"event": "call", "func_name": "withdraw", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 15, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"__return__": 25, "account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "deposit_f2_z"}, {"encoded_locals": {"__return__": 25, "amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3_z"}, {"encoded_locals": {"account": ["REF", 7], "amount": 17}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account", "amount"], "parent_frame_id_list": [], "unique_hash": "withdraw_f4"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 16, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"__return__": 25, "account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "deposit_f2_z"}, {"encoded_locals": {"__return__": 25, "amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3_z"}, {"encoded_locals": {"account": ["REF", 7], "amount": 17}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account", "amount"], "parent_frame_id_list": [], "unique_hash": "withdraw_f4"}], "stdout": ""}, {"event": "call", "func_name": "withdraw", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 5, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"__return__": 25, "account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "deposit_f2_z"}, {"encoded_locals": {"__return__": 25, "amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3_z"}, {"encoded_locals": {"account": ["REF", 7], "amount": 17}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account", "amount"], "parent_frame_id_list": [], "unique_hash": "withdraw_f4"}, {"encoded_locals": {"amount": 17}, "frame_id": 5, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f5"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 6, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"__return__": 25, "account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "deposit_f2_z"}, {"encoded_locals": {"__return__": 25, "amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3_z"}, {"encoded_locals": {"account": ["REF", 7], "amount": 17}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account", "amount"], "parent_frame_id_list": [], "unique_hash": "withdraw_f4"}, {"encoded_locals": {"amount": 17}, "frame_id": 5, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f5"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 8, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"__return__": 25, "account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "deposit_f2_z"}, {"encoded_locals": {"__return__": 25, "amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3_z"}, {"encoded_locals": {"account": ["REF", 7], "amount": 17}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account", "amount"], "parent_frame_id_list": [], "unique_hash": "withdraw_f4"}, {"encoded_locals": {"amount": 17}, "frame_id": 5, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f5"}], "stdout": ""}, {"event": "step_line", "func_name": "withdraw", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 8], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 9, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"__return__": 25, "account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "deposit_f2_z"}, {"encoded_locals": {"__return__": 25, "amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3_z"}, {"encoded_locals": {"account": ["REF", 7], "amount": 17}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account", "amount"], "parent_frame_id_list": [], "unique_hash": "withdraw_f4"}, {"encoded_locals": {"amount": 17}, "frame_id": 5, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f5"}], "stdout": ""}, {"event": "return", "func_name": "withdraw", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 8], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 9, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"__return__": 25, "account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "deposit_f2_z"}, {"encoded_locals": {"__return__": 25, "amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3_z"}, {"encoded_locals": {"account": ["REF", 7], "amount": 17}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account", "amount"], "parent_frame_id_list": [], "unique_hash": "withdraw_f4"}, {"encoded_locals": {"__return__": 8, "amount": 17}, "frame_id": 5, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f5"}], "stdout": ""}, {"event": "return", "func_name": "withdraw", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 8], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 16, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"__return__": 25, "account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "deposit_f2_z"}, {"encoded_locals": {"__return__": 25, "amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3_z"}, {"encoded_locals": {"__return__": 8, "account": ["REF", 7], "amount": 17}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "withdraw_f4"}, {"encoded_locals": {"__return__": 8, "amount": 17}, "frame_id": 5, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f5_z"}], "stdout": ""}, {"event": "step_line", "func_name": "<module>", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 8], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 25, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"__return__": 25, "account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "deposit_f2_z"}, {"encoded_locals": {"__return__": 25, "amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3_z"}, {"encoded_locals": {"__return__": 8, "account": ["REF", 7], "amount": 17}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "withdraw_f4_z"}, {"encoded_locals": {"__return__": 8, "amount": 17}, "frame_id": 5, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f5_z"}], "stdout": ""}, {"event": "call", "func_name": "check_balance", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 8], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 19, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"__return__": 25, "account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "deposit_f2_z"}, {"encoded_locals": {"__return__": 25, "amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3_z"}, {"encoded_locals": {"__return__": 8, "account": ["REF", 7], "amount": 17}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "withdraw_f4_z"}, {"encoded_locals": {"__return__": 8, "amount": 17}, "frame_id": 5, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f5_z"}, {"encoded_locals": {"account": ["REF", 7]}, "frame_id": 6, "func_name": "check_balance", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account"], "parent_frame_id_list": [], "unique_hash": "check_balance_f6"}], "stdout": ""}, {"event": "step_line", "func_name": "check_balance", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 8], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 20, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"__return__": 25, "account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "deposit_f2_z"}, {"encoded_locals": {"__return__": 25, "amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3_z"}, {"encoded_locals": {"__return__": 8, "account": ["REF", 7], "amount": 17}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "withdraw_f4_z"}, {"encoded_locals": {"__return__": 8, "amount": 17}, "frame_id": 5, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f5_z"}, {"encoded_locals": {"account": ["REF", 7]}, "frame_id": 6, "func_name": "check_balance", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account"], "parent_frame_id_list": [], "unique_hash": "check_balance_f6"}], "stdout": ""}, {"event": "return", "func_name": "check_balance", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 8], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 20, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"__return__": 25, "account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "deposit_f2_z"}, {"encoded_locals": {"__return__": 25, "amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3_z"}, {"encoded_locals": {"__return__": 8, "account": ["REF", 7], "amount": 17}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "withdraw_f4_z"}, {"encoded_locals": {"__return__": 8, "amount": 17}, "frame_id": 5, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f5_z"}, {"encoded_locals": {"__return__": 8, "account": ["REF", 7]}, "frame_id": 6, "func_name": "check_balance", "is_highlighted": true, "is_parent": false, "is_zombie": false, "ordered_varnames": ["account", "__return__"], "parent_frame_id_list": [], "unique_hash": "check_balance_f6"}], "stdout": ""}, {"event": "return", "func_name": "<module>", "globals": {"a": ["REF", 7], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3], "withdraw": ["REF", 2]}, "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 8], ["deposit", ["REF", 5]], ["withdraw", ["REF", 6]]]}, "line": 25, "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "stack_to_render": [{"encoded_locals": {"__return__": ["REF", 7], "deposit": ["REF", 5], "dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6]}, "frame_id": 1, "func_name": "account", "is_highlighted": false, "is_parent": true, "is_zombie": true, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"], "parent_frame_id_list": [], "unique_hash": "account_f1_p_z"}, {"encoded_locals": {"__return__": 25, "account": ["REF", 7], "amount": 5}, "frame_id": 2, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "deposit_f2_z"}, {"encoded_locals": {"__return__": 25, "amount": 5}, "frame_id": 3, "func_name": "deposit", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "deposit_f3_z"}, {"encoded_locals": {"__return__": 8, "account": ["REF", 7], "amount": 17}, "frame_id": 4, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "amount", "__return__"], "parent_frame_id_list": [], "unique_hash": "withdraw_f4_z"}, {"encoded_locals": {"__return__": 8, "amount": 17}, "frame_id": 5, "func_name": "withdraw", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["amount", "__return__"], "parent_frame_id_list": [1], "unique_hash": "withdraw_f5_z"}, {"encoded_locals": {"__return__": 8, "account": ["REF", 7]}, "frame_id": 6, "func_name": "check_balance", "is_highlighted": false, "is_parent": false, "is_zombie": true, "ordered_varnames": ["account", "__return__"], "parent_frame_id_list": [], "unique_hash": "check_balance_f6_z"}], "stdout": ""}]}</script><p>The name <tt class="docutils literal">dispatch</tt> within the body of the <tt class="docutils literal">account</tt> constructor is bound
to a dictionary that contains the messages accepted by an account as keys. The
<em>balance</em> is a number, while the messages <em>deposit</em> and <em>withdraw</em> are bound to
functions. These functions have access to the <tt class="docutils literal">dispatch</tt> dictionary, and so
they can read and change the balance. By storing the balance in the dispatch
dictionary rather than in the <tt class="docutils literal">account</tt> frame directly, we avoid the need for
<tt class="docutils literal">nonlocal</tt> statements in <tt class="docutils literal">deposit</tt> and <tt class="docutils literal">withdraw</tt>.</p>
<p>The operators <tt class="docutils literal">+=</tt> and <tt class="docutils literal"><span class="pre">-=</span></tt> are shorthand in Python (and many other
languages) for combined lookup and re-assignment.  The last two lines below are
equivalent.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="n">a</span> <span class="o">+</span> <span class="mi">1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">+=</span> <span class="mi">1</span>
</pre></div>

</div>
<div class="section" id="propagating-constraints">
<h3>2.4.9   Propagating Constraints</h3>
<p>Mutable data allows us to simulate systems with change, but also allows us to
build new kinds of abstractions.  In this extended example, we combine nonlocal
assignment, lists, and dictionaries to build a <em>constraint-based system</em> that
supports computation in multiple directions. Expressing programs as constraints
is a type of <em>declarative programming</em>, in which a programmer declares the
structure of a problem to be solved, but abstracts away the details of exactly
how the solution to the problem is computed.</p>
<p>Computer programs are traditionally organized as one-directional computations,
which perform operations on pre-specified arguments to produce desired outputs.
On the other hand, we often want to model systems in terms of relations among
quantities. For example, we previously considered the ideal gas law, which
relates the pressure (<tt class="docutils literal">p</tt>), volume (<tt class="docutils literal">v</tt>), quantity (<tt class="docutils literal">n</tt>), and temperature
(<tt class="docutils literal">t</tt>) of an ideal gas via Boltzmann's constant (<tt class="docutils literal">k</tt>):</p>
<pre class="literal-block">
p * v = n * k * t
</pre>
<p>Such an equation is not one-directional. Given any four of the quantities, we
can use this equation to compute the fifth. Yet translating the equation into a
traditional computer language would force us to choose one of the quantities to
be computed in terms of the other four. Thus, a function for computing the
pressure could not be used to compute the temperature, even though the
computations of both quantities arise from the same equation.</p>
<p>In this section, we sketch the design of a general model of linear
relationships.  We define primitive constraints that hold between quantities,
such as an <tt class="docutils literal">adder(a, b, c)</tt> constraint that enforces the mathematical
relationship <tt class="docutils literal">a + b = c</tt>.</p>
<p>We also define a means of combination, so that primitive constraints can be
combined to express more complex relations.  In this way, our program resembles
a programming language.  We combine constraints by constructing a network in
which constraints are joined by connectors. A connector is an object that
"holds" a value and may participate in one or more constraints.</p>
<p>For example, we know that the relationship between Fahrenheit and Celsius
temperatures is:</p>
<pre class="literal-block">
9 * c = 5 * (f - 32)
</pre>
<p>This equation is a complex constraint between <tt class="docutils literal">c</tt> and <tt class="docutils literal">f</tt>. Such a constraint
can be thought of as a network consisting of primitive <tt class="docutils literal">adder</tt>,
<tt class="docutils literal">multiplier</tt>, and <tt class="docutils literal">constant</tt> constraints.</p>
<div class="figure">
<img alt="" src="../img/constraints.png"/>
</div>
<p>In this figure, we see on the left a multiplier box with three terminals,
labeled <tt class="docutils literal">a</tt>, <tt class="docutils literal">b</tt>, and <tt class="docutils literal">c</tt>. These connect the multiplier to the rest of the
network as follows: The <tt class="docutils literal">a</tt> terminal is linked to a connector <tt class="docutils literal">celsius</tt>,
which will hold the Celsius temperature. The <tt class="docutils literal">b</tt> terminal is linked to a
connector <tt class="docutils literal">w</tt>, which is also linked to a constant box that holds 9. The <tt class="docutils literal">c</tt>
terminal, which the multiplier box constrains to be the product of <tt class="docutils literal">a</tt> and
<tt class="docutils literal">b</tt>, is linked to the <tt class="docutils literal">c</tt> terminal of another multiplier box, whose <tt class="docutils literal">b</tt>
is connected to a constant 5 and whose <tt class="docutils literal">a</tt> is connected to one of the terms
in the sum constraint.</p>
<p>Computation by such a network proceeds as follows: When a connector is given a
value (by the user or by a constraint box to which it is linked), it awakens all
of its associated constraints (except for the constraint that just awakened it)
to inform them that it has a value. Each awakened constraint box then polls its
connectors to see if there is enough information to determine a value for a
connector. If so, the box sets that connector, which then awakens all of its
associated constraints, and so on. For instance, in conversion between Celsius
and Fahrenheit, <tt class="docutils literal">w</tt>, <tt class="docutils literal">x</tt>, and <tt class="docutils literal">y</tt> are immediately set by the constant
boxes to 9, 5, and 32, respectively. The connectors awaken the
multipliers and the adder, which determine that there is not enough information
to proceed. If the user (or some other part of the network) sets the <tt class="docutils literal">celsius</tt>
connector to a value (say 25), the leftmost multiplier will be awakened, and
it will set <tt class="docutils literal">u</tt> to <tt class="docutils literal">25 * 9 = 225</tt>.  Then <tt class="docutils literal">u</tt> awakens the second
multiplier, which sets <tt class="docutils literal">v</tt> to 45, and <tt class="docutils literal">v</tt> awakens the adder, which sets
the <tt class="docutils literal">fahrenheit</tt> connector to 77.</p>
<p><strong>Using the Constraint System.</strong> To use the constraint system to carry out the
temperature computation outlined above, we first create two named connectors,
<tt class="docutils literal">celsius</tt> and <tt class="docutils literal">fahrenheit</tt>, by calling the <tt class="docutils literal">connector</tt> constructor.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">celsius</span> <span class="o">=</span> <span class="n">connector</span><span class="p">(</span><span class="s1">'Celsius'</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">fahrenheit</span> <span class="o">=</span> <span class="n">connector</span><span class="p">(</span><span class="s1">'Fahrenheit'</span><span class="p">)</span>
</pre></div>

<p>Then, we link these connectors into a network that mirrors the figure above.
The function <tt class="docutils literal">converter</tt> assembles the various connectors and constraints
in the network.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">converter</span><span class="p">(</span><span class="n">c</span><span class="p">,</span> <span class="n">f</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""Connect c to f with constraints to convert from Celsius to Fahrenheit."""</span>
<span class="gp">    </span>    <span class="n">u</span><span class="p">,</span> <span class="n">v</span><span class="p">,</span> <span class="n">w</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span> <span class="o">=</span> <span class="p">[</span><span class="n">connector</span><span class="p">()</span> <span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">5</span><span class="p">)]</span>
<span class="gp">    </span>    <span class="n">multiplier</span><span class="p">(</span><span class="n">c</span><span class="p">,</span> <span class="n">w</span><span class="p">,</span> <span class="n">u</span><span class="p">)</span>
<span class="gp">    </span>    <span class="n">multiplier</span><span class="p">(</span><span class="n">v</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">u</span><span class="p">)</span>
<span class="gp">    </span>    <span class="n">adder</span><span class="p">(</span><span class="n">v</span><span class="p">,</span> <span class="n">y</span><span class="p">,</span> <span class="n">f</span><span class="p">)</span>
<span class="gp">    </span>    <span class="n">constant</span><span class="p">(</span><span class="n">w</span><span class="p">,</span> <span class="mi">9</span><span class="p">)</span>
<span class="gp">    </span>    <span class="n">constant</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="mi">5</span><span class="p">)</span>
<span class="gp">    </span>    <span class="n">constant</span><span class="p">(</span><span class="n">y</span><span class="p">,</span> <span class="mi">32</span><span class="p">)</span>
</pre></div>

<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">converter</span><span class="p">(</span><span class="n">celsius</span><span class="p">,</span> <span class="n">fahrenheit</span><span class="p">)</span>
</pre></div>

<p>We will use a message passing system to coordinate constraints and connectors.
Constraints are dictionaries that do not hold local states themselves. Their
responses to messages are non-pure functions that change the connectors that
they constrain.</p>
<p>Connectors are dictionaries that hold a current value and respond to messages
that manipulate that value.  Constraints will not change the value of connectors
directly, but instead will do so by sending messages, so that the connector can
notify other constraints in response to the change.  In this way, a connector
represents a number, but also encapsulates connector behavior.</p>
<p>One message we can send to a connector is to set its value.  Here, we (the
<tt class="docutils literal">'user'</tt>) set the value of <tt class="docutils literal">celsius</tt> to 25.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">celsius</span><span class="p">[</span><span class="s1">'set_val'</span><span class="p">](</span><span class="s1">'user'</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
<span class="go">Celsius = 25</span>
<span class="go">Fahrenheit = 77.0</span>
</pre></div>

<p>Not only does the value of <tt class="docutils literal">celsius</tt> change to 25, but its value
propagates through the network, and so the value of <tt class="docutils literal">fahrenheit</tt> is changed as
well.  These changes are printed because we named these two connectors when we
constructed them.</p>
<p>Now we can try to set <tt class="docutils literal">fahrenheit</tt> to a new value, say 212.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">fahrenheit</span><span class="p">[</span><span class="s1">'set_val'</span><span class="p">](</span><span class="s1">'user'</span><span class="p">,</span> <span class="mi">212</span><span class="p">)</span>
<span class="go">Contradiction detected: 77.0 vs 212</span>
</pre></div>

<p>The connector complains that it has sensed a contradiction: Its value is
77.0, and someone is trying to set it to 212. If we really want to reuse
the network with new values, we can tell <tt class="docutils literal">celsius</tt> to forget its old value:</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">celsius</span><span class="p">[</span><span class="s1">'forget'</span><span class="p">](</span><span class="s1">'user'</span><span class="p">)</span>
<span class="go">Celsius is forgotten</span>
<span class="go">Fahrenheit is forgotten</span>
</pre></div>

<p>The connector <tt class="docutils literal">celsius</tt> finds that the <tt class="docutils literal">user</tt>, who set its value originally,
is now retracting that value, so <tt class="docutils literal">celsius</tt> agrees to lose its value, and it
informs the rest of the network of this fact. This information eventually
propagates to <tt class="docutils literal">fahrenheit</tt>, which now finds that it has no reason for
continuing to believe that its own value is 77. Thus, it also gives up its
value.</p>
<p>Now that <tt class="docutils literal">fahrenheit</tt> has no value, we are free to set it to 212:</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="n">fahrenheit</span><span class="p">[</span><span class="s1">'set_val'</span><span class="p">](</span><span class="s1">'user'</span><span class="p">,</span> <span class="mi">212</span><span class="p">)</span>
<span class="go">Fahrenheit = 212</span>
<span class="go">Celsius = 100.0</span>
</pre></div>

<p>This new value, when propagated through the network, forces <tt class="docutils literal">celsius</tt> to have
a value of 100. We have used the very same network to compute <tt class="docutils literal">celsius</tt>
given <tt class="docutils literal">fahrenheit</tt> and to compute <tt class="docutils literal">fahrenheit</tt> given <tt class="docutils literal">celsius</tt>. This
non-directionality of computation is the distinguishing feature of
constraint-based systems.</p>
<p><strong>Implementing the Constraint System.</strong> As we have seen, connectors are
dictionaries that map message names to function and data values.  We will
implement connectors that respond to the following messages:</p>
<ul class="simple">
<li><tt class="docutils literal"><span class="pre">connector['set_val'](source,</span> value)</tt> indicates that the <tt class="docutils literal">source</tt> is
requesting the connector to set its current value to <tt class="docutils literal">value</tt>.</li>
<li><tt class="docutils literal"><span class="pre">connector['has_val']()</span></tt> returns whether the connector already has a value.</li>
<li><tt class="docutils literal"><span class="pre">connector['val']</span></tt> is the current value of the connector.</li>
<li><tt class="docutils literal"><span class="pre">connector['forget'](source)</span></tt> tells the connector that the <tt class="docutils literal">source</tt> is
requesting it to forget its value.</li>
<li><tt class="docutils literal"><span class="pre">connector['connect'](source)</span></tt> tells the connector to participate in a new
constraint, the <tt class="docutils literal">source</tt>.</li>
</ul>
<p>Constraints are also dictionaries, which receive information from connectors by
means of two messages:</p>
<ul class="simple">
<li><tt class="docutils literal"><span class="pre">constraint['new_val']()</span></tt> indicates that some connector that is connected to
the constraint has a new value.</li>
<li><tt class="docutils literal"><span class="pre">constraint['forget']()</span></tt> indicates that some connector that is connected to
the constraint has forgotten its value.</li>
</ul>
<p>When constraints receive these messages, they propagate them appropriately to
other connectors.</p>
<p>The <tt class="docutils literal">adder</tt> function constructs an adder constraint over three connectors,
where the first two must add to the third: <tt class="docutils literal">a + b = c</tt>.  To support
multidirectional constraint propagation, the adder must also specify that it
subtracts <tt class="docutils literal">a</tt> from <tt class="docutils literal">c</tt> to get <tt class="docutils literal">b</tt> and likewise subtracts <tt class="docutils literal">b</tt> from <tt class="docutils literal">c</tt>
to get <tt class="docutils literal">a</tt>.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">operator</span> <span class="kn">import</span> <span class="n">add</span><span class="p">,</span> <span class="n">sub</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">adder</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">c</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""The constraint that a + b = c."""</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">make_ternary_constraint</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">c</span><span class="p">,</span> <span class="n">add</span><span class="p">,</span> <span class="n">sub</span><span class="p">,</span> <span class="n">sub</span><span class="p">)</span>
</pre></div>

<p>We would like to implement a generic ternary (three-way) constraint, which
uses the three connectors and three functions from <tt class="docutils literal">adder</tt> to create a
constraint that accepts <tt class="docutils literal">new_val</tt> and <tt class="docutils literal">forget</tt> messages.  The response to
messages are local functions, which are placed in a dictionary called
<tt class="docutils literal">constraint</tt>.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">make_ternary_constraint</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">c</span><span class="p">,</span> <span class="n">ab</span><span class="p">,</span> <span class="n">ca</span><span class="p">,</span> <span class="n">cb</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""The constraint that ab(a,b)=c and ca(c,a)=b and cb(c,b) = a."""</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">new_value</span><span class="p">():</span>
<span class="gp">    </span>        <span class="n">av</span><span class="p">,</span> <span class="n">bv</span><span class="p">,</span> <span class="n">cv</span> <span class="o">=</span> <span class="p">[</span><span class="n">connector</span><span class="p">[</span><span class="s1">'has_val'</span><span class="p">]()</span> <span class="k">for</span> <span class="n">connector</span> <span class="ow">in</span> <span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">c</span><span class="p">)]</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="n">av</span> <span class="ow">and</span> <span class="n">bv</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">c</span><span class="p">[</span><span class="s1">'set_val'</span><span class="p">](</span><span class="n">constraint</span><span class="p">,</span> <span class="n">ab</span><span class="p">(</span><span class="n">a</span><span class="p">[</span><span class="s1">'val'</span><span class="p">],</span> <span class="n">b</span><span class="p">[</span><span class="s1">'val'</span><span class="p">]))</span>
<span class="gp">    </span>        <span class="k">elif</span> <span class="n">av</span> <span class="ow">and</span> <span class="n">cv</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">b</span><span class="p">[</span><span class="s1">'set_val'</span><span class="p">](</span><span class="n">constraint</span><span class="p">,</span> <span class="n">ca</span><span class="p">(</span><span class="n">c</span><span class="p">[</span><span class="s1">'val'</span><span class="p">],</span> <span class="n">a</span><span class="p">[</span><span class="s1">'val'</span><span class="p">]))</span>
<span class="gp">    </span>        <span class="k">elif</span> <span class="n">bv</span> <span class="ow">and</span> <span class="n">cv</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">a</span><span class="p">[</span><span class="s1">'set_val'</span><span class="p">](</span><span class="n">constraint</span><span class="p">,</span> <span class="n">cb</span><span class="p">(</span><span class="n">c</span><span class="p">[</span><span class="s1">'val'</span><span class="p">],</span> <span class="n">b</span><span class="p">[</span><span class="s1">'val'</span><span class="p">]))</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">forget_value</span><span class="p">():</span>
<span class="gp">    </span>        <span class="k">for</span> <span class="n">connector</span> <span class="ow">in</span> <span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">c</span><span class="p">):</span>
<span class="gp">    </span>            <span class="n">connector</span><span class="p">[</span><span class="s1">'forget'</span><span class="p">](</span><span class="n">constraint</span><span class="p">)</span>
<span class="gp">    </span>    <span class="n">constraint</span> <span class="o">=</span> <span class="p">{</span><span class="s1">'new_val'</span><span class="p">:</span> <span class="n">new_value</span><span class="p">,</span> <span class="s1">'forget'</span><span class="p">:</span> <span class="n">forget_value</span><span class="p">}</span>
<span class="gp">    </span>    <span class="k">for</span> <span class="n">connector</span> <span class="ow">in</span> <span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">c</span><span class="p">):</span>
<span class="gp">    </span>        <span class="n">connector</span><span class="p">[</span><span class="s1">'connect'</span><span class="p">](</span><span class="n">constraint</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">constraint</span>
</pre></div>

<p>The dictionary called <tt class="docutils literal">constraint</tt> is a dispatch dictionary, but also the
constraint object itself. It responds to the two messages that constraints
receive, but is also passed as the <tt class="docutils literal">source</tt> argument in calls to its
connectors.</p>
<p>The constraint's local function <tt class="docutils literal">new_value</tt> is called whenever the constraint
is informed that one of its connectors has a value. This function first checks
to see if both <tt class="docutils literal">a</tt> and <tt class="docutils literal">b</tt> have values. If so, it tells <tt class="docutils literal">c</tt> to set its
value to the return value of function <tt class="docutils literal">ab</tt>, which is <tt class="docutils literal">add</tt> in the case of an
<tt class="docutils literal">adder</tt>. The constraint passes <em>itself</em> (<tt class="docutils literal">constraint</tt>) as the <tt class="docutils literal">source</tt>
argument of the connector, which is the adder object. If <tt class="docutils literal">a</tt> and <tt class="docutils literal">b</tt> do not
both have values, then the constraint checks <tt class="docutils literal">a</tt> and <tt class="docutils literal">c</tt>, and so on.</p>
<p>If the constraint is informed that one of its connectors has forgotten its
value, it requests that all of its connectors now forget their values. (Only
those values that were set by this constraint are actually lost.)</p>
<p>A <tt class="docutils literal">multiplier</tt> is very similar to an <tt class="docutils literal">adder</tt>.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">operator</span> <span class="kn">import</span> <span class="n">mul</span><span class="p">,</span> <span class="n">truediv</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">multiplier</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">c</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""The constraint that a * b = c."""</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">make_ternary_constraint</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">c</span><span class="p">,</span> <span class="n">mul</span><span class="p">,</span> <span class="n">truediv</span><span class="p">,</span> <span class="n">truediv</span><span class="p">)</span>
</pre></div>

<p>A constant is a constraint as well, but one that is never sent any messages,
because it involves only a single connector that it sets on construction.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">constant</span><span class="p">(</span><span class="n">connector</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""The constraint that connector = value."""</span>
<span class="gp">    </span>    <span class="n">constraint</span> <span class="o">=</span> <span class="p">{}</span>
<span class="gp">    </span>    <span class="n">connector</span><span class="p">[</span><span class="s1">'set_val'</span><span class="p">](</span><span class="n">constraint</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">constraint</span>
</pre></div>

<p>These three constraints are sufficient to implement our temperature conversion
network.</p>
<p><strong>Representing connectors.</strong> A connector is represented as a dictionary that
contains a value, but also has response functions with local state.  The
connector must track the <tt class="docutils literal">informant</tt> that gave it its current value, and a
list of <tt class="docutils literal">constraints</tt> in which it participates.</p>
<p>The constructor <tt class="docutils literal">connector</tt> has local functions for setting and
forgetting values, which are the responses to messages from constraints.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">connector</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="kc">None</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""A connector between constraints."""</span>
<span class="gp">    </span>    <span class="n">informant</span> <span class="o">=</span> <span class="kc">None</span>
<span class="gp">    </span>    <span class="n">constraints</span> <span class="o">=</span> <span class="p">[]</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">set_value</span><span class="p">(</span><span class="n">source</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
<span class="gp">    </span>        <span class="k">nonlocal</span> <span class="n">informant</span>
<span class="gp">    </span>        <span class="n">val</span> <span class="o">=</span> <span class="n">connector</span><span class="p">[</span><span class="s1">'val'</span><span class="p">]</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="n">val</span> <span class="ow">is</span> <span class="kc">None</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">informant</span><span class="p">,</span> <span class="n">connector</span><span class="p">[</span><span class="s1">'val'</span><span class="p">]</span> <span class="o">=</span> <span class="n">source</span><span class="p">,</span> <span class="n">value</span>
<span class="gp">    </span>            <span class="k">if</span> <span class="n">name</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
<span class="gp">    </span>                <span class="nb">print</span><span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="s1">'='</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
<span class="gp">    </span>            <span class="n">inform_all_except</span><span class="p">(</span><span class="n">source</span><span class="p">,</span> <span class="s1">'new_val'</span><span class="p">,</span> <span class="n">constraints</span><span class="p">)</span>
<span class="gp">    </span>        <span class="k">else</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">if</span> <span class="n">val</span> <span class="o">!=</span> <span class="n">value</span><span class="p">:</span>
<span class="gp">    </span>                <span class="nb">print</span><span class="p">(</span><span class="s1">'Contradiction detected:'</span><span class="p">,</span> <span class="n">val</span><span class="p">,</span> <span class="s1">'vs'</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">forget_value</span><span class="p">(</span><span class="n">source</span><span class="p">):</span>
<span class="gp">    </span>        <span class="k">nonlocal</span> <span class="n">informant</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="n">informant</span> <span class="o">==</span> <span class="n">source</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">informant</span><span class="p">,</span> <span class="n">connector</span><span class="p">[</span><span class="s1">'val'</span><span class="p">]</span> <span class="o">=</span> <span class="kc">None</span><span class="p">,</span> <span class="kc">None</span>
<span class="gp">    </span>            <span class="k">if</span> <span class="n">name</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">:</span>
<span class="gp">    </span>                <span class="nb">print</span><span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="s1">'is forgotten'</span><span class="p">)</span>
<span class="gp">    </span>            <span class="n">inform_all_except</span><span class="p">(</span><span class="n">source</span><span class="p">,</span> <span class="s1">'forget'</span><span class="p">,</span> <span class="n">constraints</span><span class="p">)</span>
<span class="gp">    </span>    <span class="n">connector</span> <span class="o">=</span> <span class="p">{</span><span class="s1">'val'</span><span class="p">:</span> <span class="kc">None</span><span class="p">,</span>
<span class="gp">    </span>                 <span class="s1">'set_val'</span><span class="p">:</span> <span class="n">set_value</span><span class="p">,</span>
<span class="gp">    </span>                 <span class="s1">'forget'</span><span class="p">:</span> <span class="n">forget_value</span><span class="p">,</span>
<span class="gp">    </span>                 <span class="s1">'has_val'</span><span class="p">:</span> <span class="k">lambda</span><span class="p">:</span> <span class="n">connector</span><span class="p">[</span><span class="s1">'val'</span><span class="p">]</span> <span class="ow">is</span> <span class="ow">not</span> <span class="kc">None</span><span class="p">,</span>
<span class="gp">    </span>                 <span class="s1">'connect'</span><span class="p">:</span> <span class="k">lambda</span> <span class="n">source</span><span class="p">:</span> <span class="n">constraints</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">source</span><span class="p">)}</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">connector</span>
</pre></div>

<p>A connector is again a dispatch dictionary for the five messages used by
constraints to communicate with connectors. Four responses are functions, and
the final response is the value itself.</p>
<p>The local function <tt class="docutils literal">set_value</tt> is called when there is a request to set the
connector's value. If the connector does not currently have a value, it will set
its value and remember as <tt class="docutils literal">informant</tt> the source constraint that requested the
value to be set. Then the connector will notify all of its participating
constraints except the constraint that requested the value to be set. This is
accomplished using the following iterative function.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">inform_all_except</span><span class="p">(</span><span class="n">source</span><span class="p">,</span> <span class="n">message</span><span class="p">,</span> <span class="n">constraints</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""Inform all constraints of the message, except source."""</span>
<span class="gp">    </span>    <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="n">constraints</span><span class="p">:</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="n">c</span> <span class="o">!=</span> <span class="n">source</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">c</span><span class="p">[</span><span class="n">message</span><span class="p">]()</span>
</pre></div>

<p>If a connector is asked to forget its value, it calls the local function
<tt class="docutils literal"><span class="pre">forget-value</span></tt>, which first checks to make sure that the request is coming
from the same constraint that set the value originally. If so, the connector
informs its associated constraints about the loss of the value.</p>
<p>The response to the message <tt class="docutils literal">has_val</tt> indicates whether the connector has a
value.  The response to the message <tt class="docutils literal">connect</tt> adds the source constraint to
the list of constraints.</p>
<p>The constraint program we have designed introduces many ideas that will appear
again in object-oriented programming.  Constraints and connectors are both
abstractions that are manipulated through messages.  When the value of a
connector is changed, it is changed via a message that not only changes the
value, but validates it (checking the source) and propagates its effects
(informing other constraints).  In fact, we will use a similar architecture of
dictionaries with string-valued keys and functional values to implement an
object-oriented system later in this chapter.</p>
</div>
</div>
  <p><i>Continue</i>:
  	<a href="25-object-oriented-programming.html">
  		2.5 Object-Oriented Programming
  	</a>
      </div>
    </section>

    <div class="wrap">
      <footer id="contentinfo" class="body">
          Composing Programs by <a href="http://www.denero.org/">John
          DeNero</a>, based on the textbook <a
          href="http://mitpress.mit.edu/sicp/">Structure and
          Interpretation of Computer Programs</a> by Harold Abelson and
          Gerald Jay Sussman, is licensed under a <a rel="license"
          href="http://creativecommons.org/licenses/by-sa/3.0/">Creative
          Commons Attribution-ShareAlike 3.0 Unported License</a>.
      </footer><!-- /#contentinfo -->
    </div>
  </div>
</body>

<!-- Mirrored from www.composingprograms.com/pages/24-mutable-data.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:45:01 GMT -->
</html>