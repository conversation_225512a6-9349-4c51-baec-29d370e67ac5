jsPlumbUtil={isArray:function(b){return Object.prototype.toString.call(b)==="[object Array]"},isString:function(a){return typeof a==="string"},isObject:function(a){return Object.prototype.toString.call(a)==="[object Object]"},convertStyle:function(b,a){if("transparent"===b){return b}var g=b,f=function(h){return h.length==1?"0"+h:h},c=function(h){return f(Number(h).toString(16))},d=/(rgb[a]?\()(.*)(\))/;if(b.match(d)){var e=b.match(d)[2].split(",");g="#"+c(e[0])+c(e[1])+c(e[2]);if(!a&&e.length==4){g=g+c(e[3])}}return g},gradient:function(b,a){b=jsPlumbUtil.isArray(b)?b:[b.x,b.y];a=jsPlumbUtil.isArray(a)?a:[a.x,a.y];return(a[1]-b[1])/(a[0]-b[0])},normal:function(b,a){return -1/jsPlumbUtil.gradient(b,a)},lineLength:function(b,a){b=jsPlumbUtil.isArray(b)?b:[b.x,b.y];a=jsPlumbUtil.isArray(a)?a:[a.x,a.y];return Math.sqrt(Math.pow(a[1]-b[1],2)+Math.pow(a[0]-b[0],2))},segment:function(b,a){b=jsPlumbUtil.isArray(b)?b:[b.x,b.y];a=jsPlumbUtil.isArray(a)?a:[a.x,a.y];if(a[0]>b[0]){return(a[1]>b[1])?2:1}else{return(a[1]>b[1])?3:4}},intersects:function(f,e){var c=f.x,a=f.x+f.w,k=f.y,h=f.y+f.h,d=e.x,b=e.x+e.w,i=e.y,g=e.y+e.h;return((c<=d&&d<=a)&&(k<=i&&i<=h))||((c<=b&&b<=a)&&(k<=i&&i<=h))||((c<=d&&d<=a)&&(k<=g&&g<=h))||((c<=b&&d<=a)&&(k<=g&&g<=h))||((d<=c&&c<=b)&&(i<=k&&k<=g))||((d<=a&&a<=b)&&(i<=k&&k<=g))||((d<=c&&c<=b)&&(i<=h&&h<=g))||((d<=a&&c<=b)&&(i<=h&&h<=g))},segmentMultipliers:[null,[1,-1],[1,1],[-1,1],[-1,-1]],inverseSegmentMultipliers:[null,[-1,-1],[-1,1],[1,1],[1,-1]],pointOnLine:function(a,e,b){var d=jsPlumbUtil.gradient(a,e),i=jsPlumbUtil.segment(a,e),h=b>0?jsPlumbUtil.segmentMultipliers[i]:jsPlumbUtil.inverseSegmentMultipliers[i],c=Math.atan(d),f=Math.abs(b*Math.sin(c))*h[1],g=Math.abs(b*Math.cos(c))*h[0];return{x:a.x+g,y:a.y+f}},perpendicularLineTo:function(c,d,e){var b=jsPlumbUtil.gradient(c,d),f=Math.atan(-1/b),g=e/2*Math.sin(f),a=e/2*Math.cos(f);return[{x:d.x+a,y:d.y+g},{x:d.x-a,y:d.y-g}]},findWithFunction:function(b,d){if(b){for(var c=0;c<b.length;c++){if(d(b[c])){return c}}}return -1},indexOf:function(a,b){return jsPlumbUtil.findWithFunction(a,function(c){return c==b})},removeWithFunction:function(c,d){var b=jsPlumbUtil.findWithFunction(c,d);if(b>-1){c.splice(b,1)}return b!=-1},remove:function(b,c){var a=jsPlumbUtil.indexOf(b,c);if(a>-1){b.splice(a,1)}return a!=-1},addWithFunction:function(c,b,a){if(jsPlumbUtil.findWithFunction(c,a)==-1){c.push(b)}},addToList:function(d,b,c){var a=d[b];if(a==null){a=[],d[b]=a}a.push(c);return a},EventGenerator:function(){var c={},b=this;var a=["ready"];this.bind=function(d,e){jsPlumbUtil.addToList(c,d,e);return b};this.fire=function(g,h,d){if(c[g]){for(var f=0;f<c[g].length;f++){if(jsPlumbUtil.findWithFunction(a,function(i){return i===g})!=-1){c[g][f](h,d)}else{try{c[g][f](h,d)}catch(k){jsPlumbUtil.log("jsPlumb: fire failed for event "+g+" : "+k)}}}}return b};this.unbind=function(d){if(d){delete c[d]}else{delete c;c={}}return b};this.getListener=function(d){return c[d]}},logEnabled:true,log:function(){if(jsPlumbUtil.logEnabled&&typeof console!="undefined"){try{var b=arguments[arguments.length-1];console.log(b)}catch(a){}}},group:function(a){if(jsPlumbUtil.logEnabled&&typeof console!="undefined"){console.group(a)}},groupEnd:function(a){if(jsPlumbUtil.logEnabled&&typeof console!="undefined"){console.groupEnd(a)}},time:function(a){if(jsPlumbUtil.logEnabled&&typeof console!="undefined"){console.time(a)}},timeEnd:function(a){if(jsPlumbUtil.logEnabled&&typeof console!="undefined"){console.timeEnd(a)}}};(function(){var A=!!document.createElement("canvas").getContext,e=!!window.SVGAngle||document.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#BasicStructure","1.1"),b=function(){if(b.vml==undefined){var L=document.body.appendChild(document.createElement("div"));L.innerHTML='<v:shape id="vml_flag1" adj="1" />';var K=L.firstChild;K.style.behavior="url(#default#VML)";b.vml=K?typeof K.adj=="object":true;L.parentNode.removeChild(L)}return b.vml};var i=jsPlumbUtil.findWithFunction,J=jsPlumbUtil.indexOf,D=jsPlumbUtil.removeWithFunction,m=jsPlumbUtil.remove,u=jsPlumbUtil.addWithFunction,l=jsPlumbUtil.addToList,n=jsPlumbUtil.isArray,C=jsPlumbUtil.isString,w=jsPlumbUtil.isObject;if(!window.console){window.console={time:function(){},timeEnd:function(){},group:function(){},groupEnd:function(){},log:function(){}}}var x=null,d=function(K,L){return p.CurrentLibrary.getAttribute(F(K),L)},f=function(L,M,K){p.CurrentLibrary.setAttribute(F(L),M,K)},B=function(L,K){p.CurrentLibrary.addClass(F(L),K)},k=function(L,K){return p.CurrentLibrary.hasClass(F(L),K)},o=function(L,K){p.CurrentLibrary.removeClass(F(L),K)},F=function(K){return p.CurrentLibrary.getElementObject(K)},t=function(K){return p.CurrentLibrary.getOffset(F(K))},a=function(K){return p.CurrentLibrary.getSize(F(K))},q=jsPlumbUtil.log,I=jsPlumbUtil.group,h=jsPlumbUtil.groupEnd,H=jsPlumbUtil.time,v=jsPlumbUtil.timeEnd,r=function(){return""+(new Date()).getTime()},E=function(Z){var U=this,aa=arguments,R=false,O=Z.parameters||{},M=U.idPrefix,W=M+(new Date()).getTime(),V=null,ab=null;U._jsPlumb=Z._jsPlumb;U.getId=function(){return W};U.tooltip=Z.tooltip;U.hoverClass=Z.hoverClass||U._jsPlumb.Defaults.HoverClass||p.Defaults.HoverClass;jsPlumbUtil.EventGenerator.apply(this);this.clone=function(){var ac=new Object();U.constructor.apply(ac,aa);return ac};this.getParameter=function(ac){return O[ac]},this.getParameters=function(){return O},this.setParameter=function(ac,ad){O[ac]=ad},this.setParameters=function(ac){O=ac},this.overlayPlacements=[];var N=Z.beforeDetach;this.isDetachAllowed=function(ac){var ad=U._jsPlumb.checkCondition("beforeDetach",ac);if(N){try{ad=N(ac)}catch(ae){q("jsPlumb: beforeDetach callback failed",ae)}}return ad};var Q=Z.beforeDrop;this.isDropAllowed=function(ah,ae,af,ac,ad){var ag=U._jsPlumb.checkCondition("beforeDrop",{sourceId:ah,targetId:ae,scope:af,connection:ac,dropEndpoint:ad});if(Q){try{ag=Q({sourceId:ah,targetId:ae,scope:af,connection:ac,dropEndpoint:ad})}catch(ai){q("jsPlumb: beforeDrop callback failed",ai)}}return ag};var X=function(){if(V&&ab){var ac={};p.extend(ac,V);p.extend(ac,ab);delete U.hoverPaintStyle;if(ac.gradient&&V.fillStyle){delete ac.gradient}ab=ac}};this.setPaintStyle=function(ac,ad){V=ac;U.paintStyleInUse=V;X();if(!ad){U.repaint()}};this.getPaintStyle=function(){return V};this.setHoverPaintStyle=function(ac,ad){ab=ac;X();if(!ad){U.repaint()}};this.getHoverPaintStyle=function(){return ab};this.setHover=function(ac,ae,ad){if(!U._jsPlumb.currentlyDragging&&!U._jsPlumb.isHoverSuspended()){R=ac;if(U.hoverClass!=null&&U.canvas!=null){if(ac){L.addClass(U.canvas,U.hoverClass)}else{L.removeClass(U.canvas,U.hoverClass)}}if(ab!=null){U.paintStyleInUse=ac?ab:V;ad=ad||r();U.repaint({timestamp:ad,recalc:false})}if(U.getAttachedElements&&!ae){Y(ac,r(),U)}}};this.isHover=function(){return R};var L=p.CurrentLibrary,K=["click","dblclick","mouseenter","mouseout","mousemove","mousedown","mouseup","contextmenu"],T={mouseout:"mouseexit"},P=function(ae,af,ad){var ac=T[ad]||ad;L.bind(ae,ad,function(ag){af.fire(ac,af,ag)})},S=function(ae,ad){var ac=T[ad]||ad;L.unbind(ae,ad)};this.attachListeners=function(ad,ae){for(var ac=0;ac<K.length;ac++){P(ad,ae,K[ac])}};var Y=function(ag,af,ac){var ae=U.getAttachedElements();if(ae){for(var ad=0;ad<ae.length;ad++){if(!ac||ac!=ae[ad]){ae[ad].setHover(ag,true,af)}}}};this.reattachListenersForElement=function(ad){if(arguments.length>1){for(var ac=0;ac<K.length;ac++){S(ad,K[ac])}for(var ac=1;ac<arguments.length;ac++){U.attachListeners(ad,arguments[ac])}}}},z=function(P){E.apply(this,arguments);var U=this;this.overlays=[];var N=function(Z){var X=null;if(n(Z)){var W=Z[0],Y=p.extend({component:U,_jsPlumb:U._jsPlumb},Z[1]);if(Z.length==3){p.extend(Y,Z[2])}X=new p.Overlays[U._jsPlumb.getRenderMode()][W](Y);if(Y.events){for(var V in Y.events){X.bind(V,Y.events[V])}}}else{if(Z.constructor==String){X=new p.Overlays[U._jsPlumb.getRenderMode()][Z]({component:U,_jsPlumb:U._jsPlumb})}else{X=Z}}U.overlays.push(X)},O=function(Z){var V=U.defaultOverlayKeys||[],Y=Z.overlays,W=function(aa){return U._jsPlumb.Defaults[aa]||p.Defaults[aa]||[]};if(!Y){Y=[]}for(var X=0;X<V.length;X++){Y.unshift.apply(Y,W(V[X]))}return Y};var L=O(P);if(L){for(var Q=0;Q<L.length;Q++){N(L[Q])}}var K=function(X){var V=-1;for(var W=0;W<U.overlays.length;W++){if(X===U.overlays[W].id){V=W;break}}return V};this.addOverlay=function(V){N(V);U.repaint()};this.getOverlay=function(W){var V=K(W);return V>=0?U.overlays[V]:null};this.getOverlays=function(){return U.overlays};this.hideOverlay=function(W){var V=U.getOverlay(W);if(V){V.hide()}};this.hideOverlays=function(){for(var V=0;V<U.overlays.length;V++){U.overlays[V].hide()}};this.showOverlay=function(W){var V=U.getOverlay(W);if(V){V.show()}};this.showOverlays=function(){for(var V=0;V<U.overlays.length;V++){U.overlays[V].show()}};this.removeAllOverlays=function(){for(var V in U.overlays){U.overlays[V].cleanup()}U.overlays.splice(0,U.overlays.length);U.repaint()};this.removeOverlay=function(W){var V=K(W);if(V!=-1){var X=U.overlays[V];X.cleanup();U.overlays.splice(V,1)}};this.removeOverlays=function(){for(var V=0;V<arguments.length;V++){U.removeOverlay(arguments[V])}};var M="__label",T=function(X){var V={cssClass:X.cssClass,labelStyle:this.labelStyle,id:M,component:U,_jsPlumb:U._jsPlumb},W=p.extend(V,X);return new p.Overlays[U._jsPlumb.getRenderMode()].Label(W)};if(P.label){var R=P.labelLocation||U.defaultLabelLocation||0.5,S=P.labelStyle||U._jsPlumb.Defaults.LabelStyle||p.Defaults.LabelStyle;this.overlays.push(T({label:P.label,location:R,labelStyle:S}))}this.setLabel=function(V){var W=U.getOverlay(M);if(!W){var X=V.constructor==String||V.constructor==Function?{label:V}:V;W=T(X);this.overlays.push(W)}else{if(V.constructor==String||V.constructor==Function){W.setLabel(V)}else{if(V.label){W.setLabel(V.label)}if(V.location){W.setLocation(V.location)}}}U.repaint()};this.getLabel=function(){var V=U.getOverlay(M);return V!=null?V.getLabel():null};this.getLabelOverlay=function(){return U.getOverlay(M)}},G=function(M,K,L){M.bind("click",function(N,O){K.fire("click",K,O)});M.bind("dblclick",function(N,O){K.fire("dblclick",K,O)});M.bind("contextmenu",function(N,O){K.fire("contextmenu",K,O)});M.bind("mouseenter",function(N,O){if(!K.isHover()){L(true);K.fire("mouseenter",K,O)}});M.bind("mouseexit",function(N,O){if(K.isHover()){L(false);K.fire("mouseexit",K,O)}})};var g=0,c=function(){var K=g+1;g++;return K};var y=function(L){this.Defaults={Anchor:"BottomCenter",Anchors:[null,null],ConnectionsDetachable:true,ConnectionOverlays:[],Connector:"Bezier",Container:null,DragOptions:{},DropOptions:{},Endpoint:"Dot",EndpointOverlays:[],Endpoints:[null,null],EndpointStyle:{fillStyle:"#456"},EndpointStyles:[null,null],EndpointHoverStyle:null,EndpointHoverStyles:[null,null],HoverPaintStyle:null,LabelStyle:{color:"black"},LogEnabled:false,Overlays:[],MaxConnections:1,PaintStyle:{lineWidth:8,strokeStyle:"#456"},RenderMode:"svg",Scope:"jsPlumb_DefaultScope"};if(L){p.extend(this.Defaults,L)}this.logEnabled=this.Defaults.LogEnabled;jsPlumbUtil.EventGenerator.apply(this);var bn=this,aL=c(),aO=bn.bind,aC={};for(var aB in this.Defaults){aC[aB]=this.Defaults[aB]}this.bind=function(bw,bv){if("ready"===bw&&M){bv()}else{aO.apply(bn,[bw,bv])}};bn.importDefaults=function(bw){for(var bv in bw){bn.Defaults[bv]=bw[bv]}};bn.restoreDefaults=function(){bn.Defaults=p.extend({},aC)};var P=null,ar=function(){p.repaintEverything()},bp=true,aP=function(){if(bp){ar()}},be=null,M=false,aY={},aT={},aU={},ah={},br={},bf={},bm={},bu=[],ae=[],Q=this.Defaults.Scope,X=null,V=function(by,bw,bx){var bv=by[bw];if(bv==null){bv=[];by[bw]=bv}bv.push(bx);return bv},aW=function(bw,bv){if(bn.Defaults.Container){p.CurrentLibrary.appendElement(bw,bn.Defaults.Container)}else{if(!bv){document.body.appendChild(bw)}else{p.CurrentLibrary.appendElement(bw,bv)}}},aD=1,ak=function(){return""+aD++},aI=function(bv){return bv._nodes?bv._nodes:bv},a6=false,bg=function(bw,bv){a6=bw;if(bv){bn.repaintEverything()}},ba=function(bx,bz,by){if(!a6){var bA=d(bx,"id"),bv=bn.dragManager.getElementsForDraggable(bA);if(by==null){by=r()}bn.anchorManager.redraw(bA,bz,by);if(bv){for(var bw in bv){bn.anchorManager.redraw(bv[bw].id,bz,by,bv[bw].offset)}}}},aG=function(bw,by){var bz=null;if(n(bw)){bz=[];for(var bv=0;bv<bw.length;bv++){var bx=F(bw[bv]),bA=d(bx,"id");bz.push(by(bx,bA))}}else{var bx=F(bw),bA=d(bx,"id");bz=by(bx,bA)}return bz},av=function(bv){return aU[bv]},bb=function(bz,bv,bC){var bE=bv==null?false:bv,bA=p.CurrentLibrary;if(bE){if(bA.isDragSupported(bz)&&!bA.isAlreadyDraggable(bz)){var bD=bC||bn.Defaults.DragOptions||p.Defaults.DragOptions;bD=p.extend({},bD);var bB=bA.dragEvents.drag,bw=bA.dragEvents.stop,by=bA.dragEvents.start;bD[by]=am(bD[by],function(){bn.setHoverSuspended(true)});bD[bB]=am(bD[bB],function(){var bF=bA.getUIPosition(arguments);ba(bz,bF);B(bz,"jsPlumb_dragged")});bD[bw]=am(bD[bw],function(){var bF=bA.getUIPosition(arguments);ba(bz,bF);o(bz,"jsPlumb_dragged");bn.setHoverSuspended(false)});var bx=K(bz);bm[bx]=true;var bE=bm[bx];bD.disabled=bE==null?false:!bE;bA.initDraggable(bz,bD,false);bn.dragManager.register(bz)}}},aA=function(bB,bw){var bv=p.extend({},bB);if(bw){p.extend(bv,bw)}if(bv.source&&bv.source.endpoint){bv.sourceEndpoint=bv.source}if(bv.source&&bv.target.endpoint){bv.targetEndpoint=bv.target}if(bB.uuids){bv.sourceEndpoint=av(bB.uuids[0]);bv.targetEndpoint=av(bB.uuids[1])}if(bv.sourceEndpoint&&bv.sourceEndpoint.isFull()){q(bn,"could not add connection; source endpoint is full");return}if(bv.targetEndpoint&&bv.targetEndpoint.isFull()){q(bn,"could not add connection; target endpoint is full");return}if(bv.sourceEndpoint&&bv.sourceEndpoint.connectorOverlays){bv.overlays=bv.overlays||[];for(var bz=0;bz<bv.sourceEndpoint.connectorOverlays.length;bz++){bv.overlays.push(bv.sourceEndpoint.connectorOverlays[bz])}}bv.tooltip=bB.tooltip;if(!bv.tooltip&&bv.sourceEndpoint&&bv.sourceEndpoint.connectorTooltip){bv.tooltip=bv.sourceEndpoint.connectorTooltip}if(bv.target&&!bv.target.endpoint&&!bv.targetEndpoint&&!bv.newConnection){var bA=K(bv.target),bC=aZ[bA],bx=aH[bA];if(bC){if(!aj[bA]){return}var by=bx!=null?bx:bn.addEndpoint(bv.target,bC);if(bh[bA]){aH[bA]=by}bv.targetEndpoint=by;by._makeTargetCreator=true}}if(bv.source&&!bv.source.endpoint&&!bv.sourceEndpoint&&!bv.newConnection){var bA=K(bv.source),bC=az[bA],bx=a5[bA];if(bC){if(!ac[bA]){return}var by=bx!=null?bx:bn.addEndpoint(bv.source,bC);if(a9[bA]){a5[bA]=by}bv.sourceEndpoint=by}}return bv},ad=function(bz){var by=bn.Defaults.ConnectionType||bn.getDefaultConnectionType(),bx=bn.Defaults.EndpointType||af,bw=p.CurrentLibrary.getParent;if(bz.container){bz.parent=bz.container}else{if(bz.sourceEndpoint){bz.parent=bz.sourceEndpoint.parent}else{if(bz.source.constructor==bx){bz.parent=bz.source.parent}else{bz.parent=bw(bz.source)}}}bz._jsPlumb=bn;var bv=new by(bz);bv.id="con_"+ak();bs("click","click",bv);bs("dblclick","dblclick",bv);bs("contextmenu","contextmenu",bv);return bv},bt=function(bw,bx,bv){bx=bx||{};if(!bw.suspendedEndpoint){V(aY,bw.scope,bw)}if(!bx.doNotFireConnectionEvent&&bx.fireEvent!==false){bn.fire("jsPlumbConnection",{connection:bw,source:bw.source,target:bw.target,sourceId:bw.sourceId,targetId:bw.targetId,sourceEndpoint:bw.endpoints[0],targetEndpoint:bw.endpoints[1]},bv)}bn.anchorManager.newConnection(bw);ba(bw.source)},bs=function(bv,bw,bx){bx.bind(bv,function(bz,by){bn.fire(bw,bx,by)})},aw=function(bx){if(bx.container){return bx.container}else{var bv=p.CurrentLibrary.getTagName(bx.source),bw=p.CurrentLibrary.getParent(bx.source);if(bv&&bv.toLowerCase()==="td"){return p.CurrentLibrary.getParent(bw)}else{return bw}}},aF=function(bx){var bw=bn.Defaults.EndpointType||af;bx.parent=aw(bx);bx._jsPlumb=bn;var bv=new bw(bx);bv.id="ep_"+ak();bs("click","endpointClick",bv);bs("dblclick","endpointDblClick",bv);bs("contextmenu","contextmenu",bv);return bv},U=function(bx,bA,bz){var bv=aT[bx];if(bv&&bv.length){for(var by=0;by<bv.length;by++){for(var bw=0;bw<bv[by].connections.length;bw++){var bB=bA(bv[by].connections[bw]);if(bB){return}}if(bz){bz(bv[by])}}}},Y=function(bw){for(var bv in aT){U(bv,bw)}},au=function(bv,bw){if(bv!=null&&bv.parentNode!=null){bv.parentNode.removeChild(bv)}},aX=function(bx,bw){for(var bv=0;bv<bx.length;bv++){au(bx[bv],bw)}},bk=function(bw,bv){return aG(bw,function(bx,by){bm[by]=bv;if(p.CurrentLibrary.isDragSupported(bx)){p.CurrentLibrary.setDraggable(bx,bv)}})},a3=function(bx,by,bv){by=by==="block";var bw=null;if(bv){if(by){bw=function(bA){bA.setVisible(true,true,true)}}else{bw=function(bA){bA.setVisible(false,true,true)}}}var bz=d(bx,"id");U(bz,function(bB){if(by&&bv){var bA=bB.sourceId===bz?1:0;if(bB.endpoints[bA].isVisible()){bB.setVisible(true)}}else{bB.setVisible(by)}},bw)},bi=function(bv){return aG(bv,function(bx,bw){var by=bm[bw]==null?false:bm[bw];by=!by;bm[bw]=by;p.CurrentLibrary.setDraggable(bx,by);return by})},aQ=function(bv,bx){var bw=null;if(bx){bw=function(by){var bz=by.isVisible();by.setVisible(!bz)}}U(bv,function(bz){var by=bz.isVisible();bz.setVisible(!by)},bw)},W=function(bA){var by=bA.timestamp,bv=bA.recalc,bz=bA.offset,bw=bA.elId;if(!bv){if(by&&by===br[bw]){return ah[bw]}}if(bv||!bz){var bx=F(bw);if(bx!=null){ae[bw]=a(bx);ah[bw]=t(bx);br[bw]=by}}else{ah[bw]=bz;if(ae[bw]==null){var bx=F(bw);if(bx!=null){ae[bw]=a(bx)}}}if(ah[bw]&&!ah[bw].right){ah[bw].right=ah[bw].left+ae[bw][0];ah[bw].bottom=ah[bw].top+ae[bw][1];ah[bw].width=ae[bw][0];ah[bw].height=ae[bw][1];ah[bw].centerx=ah[bw].left+(ah[bw].width/2);ah[bw].centery=ah[bw].top+(ah[bw].height/2)}return ah[bw]},aN=function(bv){var bw=ah[bv];if(!bw){bw=W({elId:bv})}return{o:bw,s:ae[bv]}},K=function(bv,bw,by){var bx=F(bv);var bz=d(bx,"id");if(!bz||bz=="undefined"){if(arguments.length==2&&arguments[1]!=undefined){bz=bw}else{if(arguments.length==1||(arguments.length==3&&!arguments[2])){bz="jsPlumb_"+aL+"_"+ak()}}f(bx,"id",bz)}return bz},am=function(bx,bv,bw){bx=bx||function(){};bv=bv||function(){};return function(){var by=null;try{by=bv.apply(this,arguments)}catch(bz){q(bn,"jsPlumb function failed : "+bz)}if(bw==null||(by!==bw)){try{bx.apply(this,arguments)}catch(bz){q(bn,"wrapped function failed : "+bz)}}return by}};this.connectorClass="_jsPlumb_connector";this.endpointClass="_jsPlumb_endpoint";this.overlayClass="_jsPlumb_overlay";this.Anchors={};this.Connectors={canvas:{},svg:{},vml:{}};this.Endpoints={canvas:{},svg:{},vml:{}};this.Overlays={canvas:{},svg:{},vml:{}};this.addClass=function(bw,bv){return p.CurrentLibrary.addClass(bw,bv)};this.removeClass=function(bw,bv){return p.CurrentLibrary.removeClass(bw,bv)};this.hasClass=function(bw,bv){return p.CurrentLibrary.hasClass(bw,bv)};this.addEndpoint=function(bx,by,bH){bH=bH||{};var bw=p.extend({},bH);p.extend(bw,by);bw.endpoint=bw.endpoint||bn.Defaults.Endpoint||p.Defaults.Endpoint;bw.paintStyle=bw.paintStyle||bn.Defaults.EndpointStyle||p.Defaults.EndpointStyle;bx=aI(bx);var bz=[],bC=bx.length&&bx.constructor!=String?bx:[bx];for(var bA=0;bA<bC.length;bA++){var bF=F(bC[bA]),bv=K(bF);bw.source=bF;W({elId:bv});var bE=aF(bw);if(bw.parentAnchor){bE.parentAnchor=bw.parentAnchor}V(aT,bv,bE);var bD=ah[bv],bB=ae[bv];var bG=bE.anchor.compute({xy:[bD.left,bD.top],wh:bB,element:bE});bE.paint({anchorLoc:bG});bz.push(bE);bn.dragManager.endpointAdded(bF)}return bz.length==1?bz[0]:bz};this.addEndpoints=function(bz,bw,bv){var by=[];for(var bx=0;bx<bw.length;bx++){var bA=bn.addEndpoint(bz,bw[bx],bv);if(n(bA)){Array.prototype.push.apply(by,bA)}else{by.push(bA)}}return by};this.animate=function(bx,bw,bv){var by=F(bx),bB=d(bx,"id");bv=bv||{};var bA=p.CurrentLibrary.dragEvents.step;var bz=p.CurrentLibrary.dragEvents.complete;bv[bA]=am(bv[bA],function(){bn.repaint(bB)});bv[bz]=am(bv[bz],function(){bn.repaint(bB)});p.CurrentLibrary.animate(by,bw,bv)};this.checkCondition=function(bx,bz){var bv=bn.getListener(bx);var by=true;if(bv&&bv.length>0){try{for(var bw=0;bw<bv.length;bw++){by=by&&bv[bw](bz)}}catch(bA){q(bn,"cannot check condition ["+bx+"]"+bA)}}return by};this.connect=function(by,bw){var bv=aA(by,bw);if(bv){if(bv.deleteEndpointsOnDetach==null){bv.deleteEndpointsOnDetach=true}var bx=ad(bv);bt(bx,bv);return bx}};this.deleteEndpoint=function(bw){var bB=(typeof bw=="string")?aU[bw]:bw;if(bB){var by=bB.getUuid();if(by){aU[by]=null}bB.detachAll();aX(bB.endpoint.getDisplayElements());bn.anchorManager.deleteEndpoint(bB);for(var bA in aT){var bv=aT[bA];if(bv){var bz=[];for(var bx=0;bx<bv.length;bx++){if(bv[bx]!=bB){bz.push(bv[bx])}}aT[bA]=bz}}bn.dragManager.endpointDeleted(bB)}};this.deleteEveryEndpoint=function(){for(var bx in aT){var bv=aT[bx];if(bv&&bv.length){for(var bw=0;bw<bv.length;bw++){bn.deleteEndpoint(bv[bw])}}}delete aT;aT={};delete aU;aU={}};var a7=function(by,bA,bv){var bx=bn.Defaults.ConnectionType||bn.getDefaultConnectionType(),bw=by.constructor==bx,bz=bw?{connection:by,source:by.source,target:by.target,sourceId:by.sourceId,targetId:by.targetId,sourceEndpoint:by.endpoints[0],targetEndpoint:by.endpoints[1]}:by;if(bA){bn.fire("jsPlumbConnectionDetached",bz,bv)}bn.anchorManager.connectionDetached(bz)},a4=function(bv){bn.fire("connectionDrag",bv)},aR=function(bv){bn.fire("connectionDragStop",bv)};this.detach=function(){if(arguments.length==0){return}var bz=bn.Defaults.ConnectionType||bn.getDefaultConnectionType(),bA=arguments[0].constructor==bz,by=arguments.length==2?bA?(arguments[1]||{}):arguments[0]:arguments[0],bD=(by.fireEvent!==false),bx=by.forceDetach,bw=bA?arguments[0]:by.connection;if(bw){if(bx||(bw.isDetachAllowed(bw)&&bw.endpoints[0].isDetachAllowed(bw)&&bw.endpoints[1].isDetachAllowed(bw))){if(bx||bn.checkCondition("beforeDetach",bw)){bw.endpoints[0].detach(bw,false,true,bD)}}}else{var bv=p.extend({},by);if(bv.uuids){av(bv.uuids[0]).detachFrom(av(bv.uuids[1]),bD)}else{if(bv.sourceEndpoint&&bv.targetEndpoint){bv.sourceEndpoint.detachFrom(bv.targetEndpoint)}else{var bC=K(bv.source),bB=K(bv.target);U(bC,function(bE){if((bE.sourceId==bC&&bE.targetId==bB)||(bE.targetId==bC&&bE.sourceId==bB)){if(bn.checkCondition("beforeDetach",bE)){bE.endpoints[0].detach(bE,false,true,bD)}}})}}}};this.detachAllConnections=function(bx,by){by=by||{};bx=F(bx);var bz=d(bx,"id"),bv=aT[bz];if(bv&&bv.length){for(var bw=0;bw<bv.length;bw++){bv[bw].detachAll(by.fireEvent)}}};this.detachEveryConnection=function(bx){bx=bx||{};for(var by in aT){var bv=aT[by];if(bv&&bv.length){for(var bw=0;bw<bv.length;bw++){bv[bw].detachAll(bx.fireEvent)}}}delete aY;aY={}};this.draggable=function(bx,bv){if(typeof bx=="object"&&bx.length){for(var bw=0;bw<bx.length;bw++){var by=F(bx[bw]);if(by){bb(by,true,bv)}}}else{if(bx._nodes){for(var bw=0;bw<bx._nodes.length;bw++){var by=F(bx._nodes[bw]);if(by){bb(by,true,bv)}}}else{var by=F(bx);if(by){bb(by,true,bv)}}}};this.extend=function(bw,bv){return p.CurrentLibrary.extend(bw,bv)};this.getDefaultEndpointType=function(){return af};this.getDefaultConnectionType=function(){return ax};var bq=function(bz,by,bw,bv){for(var bx=0;bx<bz.length;bx++){bz[bx][by].apply(bz[bx],bw)}return bv(bz)},S=function(bz,by,bw){var bv=[];for(var bx=0;bx<bz.length;bx++){bv.push([bz[bx][by].apply(bz[bx],bw),bz[bx]])}return bv},an=function(bx,bw,bv){return function(){return bq(bx,bw,arguments,bv)}},ay=function(bw,bv){return function(){return S(bw,bv,arguments)}};this.getConnections=function(bH,bw){if(!bH){bH={}}else{if(bH.constructor==String){bH={scope:bH}}}var bE=function(bI){var bJ=[];if(bI){if(typeof bI=="string"){if(bI==="*"){return bI}bJ.push(bI)}else{bJ=bI}}return bJ},bF=bH.scope||bn.getDefaultScope(),bD=bE(bF),bv=bE(bH.source),bB=bE(bH.target),bx=function(bJ,bI){if(bJ==="*"){return true}return bJ.length>0?J(bJ,bI)!=-1:true},bA=(!bw&&bD.length>1)?{}:[],bG=function(bJ,bK){if(!bw&&bD.length>1){var bI=bA[bJ];if(bI==null){bI=[];bA[bJ]=bI}bI.push(bK)}else{bA.push(bK)}};for(var bz in aY){if(bx(bD,bz)){for(var by=0;by<aY[bz].length;by++){var bC=aY[bz][by];if(bx(bv,bC.sourceId)&&bx(bB,bC.targetId)){bG(bz,bC)}}}}return bA};var aK=function(bv){return{setHover:an(bv,"setHover",aK),removeAllOverlays:an(bv,"removeAllOverlays",aK),setLabel:an(bv,"setLabel",aK),addOverlay:an(bv,"addOverlay",aK),removeOverlay:an(bv,"removeOverlay",aK),removeOverlays:an(bv,"removeOverlays",aK),showOverlay:an(bv,"showOverlay",aK),hideOverlay:an(bv,"hideOverlay",aK),showOverlays:an(bv,"showOverlays",aK),hideOverlays:an(bv,"hideOverlays",aK),setPaintStyle:an(bv,"setPaintStyle",aK),setHoverPaintStyle:an(bv,"setHoverPaintStyle",aK),setDetachable:an(bv,"setDetachable",aK),setConnector:an(bv,"setConnector",aK),setParameter:an(bv,"setParameter",aK),setParameters:an(bv,"setParameters",aK),detach:function(){for(var bw=0;bw<bv.length;bw++){bn.detach(bv[bw])}},getLabel:ay(bv,"getLabel"),getOverlay:ay(bv,"getOverlay"),isHover:ay(bv,"isHover"),isDetachable:ay(bv,"isDetachable"),getParameter:ay(bv,"getParameter"),getParameters:ay(bv,"getParameters"),getPaintStyle:ay(bv,"getPaintStyle"),getHoverPaintStyle:ay(bv,"getHoverPaintStyle"),length:bv.length,each:function(bx){for(var bw=0;bw<bv.length;bw++){bx(bv[bw])}return aK(bv)},get:function(bw){return bv[bw]}}};this.select=function(bv){bv=bv||{};bv.scope=bv.scope||"*";var bw=bn.getConnections(bv,true);return aK(bw)};this.getAllConnections=function(){return aY};this.getDefaultScope=function(){return Q};this.getEndpoint=av;this.getEndpoints=function(bv){return aT[K(bv)]};this.getId=K;this.getOffset=function(bw){var bv=ah[bw];return W({elId:bw})};this.getSelector=function(bv){return p.CurrentLibrary.getSelector(bv)};this.getSize=function(bw){var bv=ae[bw];if(!bv){W({elId:bw})}return ae[bw]};this.appendElement=aW;var aS=false;this.isHoverSuspended=function(){return aS};this.setHoverSuspended=function(bv){aS=bv};this.isCanvasAvailable=function(){return A};this.isSVGAvailable=function(){return e};this.isVMLAvailable=b;this.hide=function(bv,bw){a3(bv,"none",bw)};this.idstamp=ak;this.init=function(){if(!M){bn.setRenderMode(bn.Defaults.RenderMode);var bv=function(bw){p.CurrentLibrary.bind(document,bw,function(bC){if(!bn.currentlyDragging&&X==p.CANVAS){for(var bB in aY){var bD=aY[bB];for(var bz=0;bz<bD.length;bz++){var by=bD[bz].connector[bw](bC);if(by){return}}}for(var bA in aT){var bx=aT[bA];for(var bz=0;bz<bx.length;bz++){if(bx[bz].endpoint[bw](bC)){return}}}}})};bv("click");bv("dblclick");bv("mousemove");bv("mousedown");bv("mouseup");bv("contextmenu");M=true;bn.fire("ready")}};this.log=P;this.jsPlumbUIComponent=E;this.makeAnchor=function(){if(arguments.length==0){return null}var bA=arguments[0],bx=arguments[1],bw=arguments[2],by=null;if(bA.compute&&bA.getOrientation){return bA}else{if(typeof bA=="string"){by=p.Anchors[arguments[0]]({elementId:bx,jsPlumbInstance:bn})}else{if(n(bA)){if(n(bA[0])||C(bA[0])){if(bA.length==2&&C(bA[0])&&w(bA[1])){var bv=p.extend({elementId:bx,jsPlumbInstance:bn},bA[1]);by=p.Anchors[bA[0]](bv)}else{by=new aq(bA,null,bx)}}else{var bz={x:bA[0],y:bA[1],orientation:(bA.length>=4)?[bA[2],bA[3]]:[0,0],offsets:(bA.length==6)?[bA[4],bA[5]]:[0,0],elementId:bx};by=new aa(bz);by.clone=function(){return new aa(bz)}}}}}if(!by.id){by.id="anchor_"+ak()}return by};this.makeAnchors=function(by,bw,bv){var bz=[];for(var bx=0;bx<by.length;bx++){if(typeof by[bx]=="string"){bz.push(p.Anchors[by[bx]]({elementId:bw,jsPlumbInstance:bv}))}else{if(n(by[bx])){bz.push(bn.makeAnchor(by[bx],bw,bv))}}}return bz};this.makeDynamicAnchor=function(bv,bw){return new aq(bv,bw)};var aZ={},aH={},bh={},ap={},ab=function(bv,bw){bv.paintStyle=bv.paintStyle||bn.Defaults.EndpointStyles[bw]||bn.Defaults.EndpointStyle||p.Defaults.EndpointStyles[bw]||p.Defaults.EndpointStyle;bv.hoverPaintStyle=bv.hoverPaintStyle||bn.Defaults.EndpointHoverStyles[bw]||bn.Defaults.EndpointHoverStyle||p.Defaults.EndpointHoverStyles[bw]||p.Defaults.EndpointHoverStyle;bv.anchor=bv.anchor||bn.Defaults.Anchors[bw]||bn.Defaults.Anchor||p.Defaults.Anchors[bw]||p.Defaults.Anchor;bv.endpoint=bv.endpoint||bn.Defaults.Endpoints[bw]||bn.Defaults.Endpoint||p.Defaults.Endpoints[bw]||p.Defaults.Endpoint};this.makeTarget=function(by,bz,bF){var bw=p.extend({_jsPlumb:bn},bF);p.extend(bw,bz);ab(bw,1);var bD=p.CurrentLibrary,bE=bw.scope||bn.Defaults.Scope,bA=!(bw.deleteEndpointsOnDetach===false),bx=bw.maxConnections||-1,bv=function(bK){var bI=K(bK);aZ[bI]=bw;bh[bI]=bw.uniqueEndpoint,ap[bI]=bx,aj[bI]=true,proxyComponent=new E(bw);var bH=p.extend({},bw.dropOptions||{}),bG=function(){var bN=p.CurrentLibrary.getDropEvent(arguments),bP=bn.select({target:bI}).length;if(!aj[bI]||ap[bI]>0&&bP>=ap[bI]){console.log("target element "+bI+" is full.");return false}bn.currentlyDragging=false;var bZ=F(bD.getDragObject(arguments)),bO=d(bZ,"dragId"),bX=d(bZ,"originalScope"),bU=bf[bO],bM=bU.endpoints[0],bL=bw.endpoint?p.extend({},bw.endpoint):{};bM.anchor.locked=false;if(bX){bD.setDragScope(bZ,bX)}var bS=proxyComponent.isDropAllowed(bU.sourceId,K(bK),bU.scope,bU,null);if(bU.endpointsToDeleteOnDetach){if(bM===bU.endpointsToDeleteOnDetach[0]){bU.endpointsToDeleteOnDetach[0]=null}else{if(bM===bU.endpointsToDeleteOnDetach[1]){bU.endpointsToDeleteOnDetach[1]=null}}}if(bU.suspendedEndpoint){bU.targetId=bU.suspendedEndpoint.elementId;bU.target=bD.getElementObject(bU.suspendedEndpoint.elementId);bU.endpoints[1]=bU.suspendedEndpoint}if(bS){bM.detach(bU,false,true,false);var bY=aH[bI]||bn.addEndpoint(bK,bw);if(bw.uniqueEndpoint){aH[bI]=bY}bY._makeTargetCreator=true;if(bY.anchor.positionFinder!=null){var bV=bD.getUIPosition(arguments),bR=bD.getOffset(bK),bW=bD.getSize(bK),bQ=bY.anchor.positionFinder(bV,bR,bW,bY.anchor.constructorParams);bY.anchor.x=bQ[0];bY.anchor.y=bQ[1]}var bT=bn.connect({source:bM,target:bY,scope:bX,previousConnection:bU,container:bU.parent,deleteEndpointsOnDetach:bA,doNotFireConnectionEvent:bM.endpointWillMoveAfterConnection});if(bU.endpoints[1]._makeTargetCreator&&bU.endpoints[1].connections.length<2){bn.deleteEndpoint(bU.endpoints[1])}if(bA){bT.endpointsToDeleteOnDetach=[bM,bY]}bT.repaint()}else{if(bU.suspendedEndpoint){if(bM.isReattach){bU.setHover(false);bU.floatingAnchorIndex=null;bU.suspendedEndpoint.addConnection(bU);bn.repaint(bM.elementId)}else{bM.detach(bU,false,true,true,bN)}}}};var bJ=bD.dragEvents.drop;bH.scope=bH.scope||bE;bH[bJ]=am(bH[bJ],bG);bD.initDroppable(bK,bH,true)};by=aI(by);var bC=by.length&&by.constructor!=String?by:[by];for(var bB=0;bB<bC.length;bB++){bv(F(bC[bB]))}return bn};this.unmakeTarget=function(bw,bx){bw=p.CurrentLibrary.getElementObject(bw);var bv=K(bw);if(!bx){delete aZ[bv];delete bh[bv];delete ap[bv];delete aj[bv]}return bn};this.makeTargets=function(bx,by,bv){for(var bw=0;bw<bx.length;bw++){bn.makeTarget(bx[bw],by,bv)}};var az={},a5={},a9={},ac={},N={},aj={};this.makeSource=function(bz,bC,bv){var bA=p.extend({},bv);p.extend(bA,bC);ab(bA,0);var by=p.CurrentLibrary,bB=function(bK){var bE=K(bK),bL=bA.parent,bD=bL!=null?bn.getId(by.getElementObject(bL)):bE;az[bD]=bA;a9[bD]=bA.uniqueEndpoint;ac[bD]=true;var bF=by.dragEvents.stop,bJ=by.dragEvents.drag,bM=p.extend({},bA.dragOptions||{}),bH=bM.drag,bN=bM.stop,bO=null,bI=false;bM.scope=bM.scope||bA.scope;bM[bJ]=am(bM[bJ],function(){if(bH){bH.apply(this,arguments)}bI=false});bM[bF]=am(bM[bF],function(){if(bN){bN.apply(this,arguments)}bn.currentlyDragging=false;if(bO.connections.length==0){bn.deleteEndpoint(bO)}else{by.unbind(bO.canvas,"mousedown");var bQ=bA.anchor||bn.Defaults.Anchor,bR=bO.anchor,bT=bO.connections[0];bO.anchor=bn.makeAnchor(bQ,bE,bn);if(bA.parent){var bS=by.getElementObject(bA.parent);if(bS){var bP=bO.elementId;var bU=bA.container||bn.Defaults.Container||p.Defaults.Container;bO.setElement(bS,bU);bO.endpointWillMoveAfterConnection=false;bn.anchorManager.rehomeEndpoint(bP,bS);bT.previousConnection=null;D(aY[bT.scope],function(bV){return bV.id===bT.id});bn.anchorManager.connectionDetached({sourceId:bT.sourceId,targetId:bT.targetId,connection:bT});bt(bT)}}bO.repaint();bn.repaint(bO.elementId);bn.repaint(bT.targetId)}});var bG=function(bS){if(!ac[bD]){return}if(bC.filter){var bP=bC.filter(by.getOriginalEvent(bS),bK);if(bP===false){return}}var bW=W({elId:bE});var bV=((bS.pageX||bS.page.x)-bW.left)/bW.width,bU=((bS.pageY||bS.page.y)-bW.top)/bW.height,b0=bV,bZ=bU;if(bA.parent){var bT=by.getElementObject(bA.parent),bR=K(bT);bW=W({elId:bR});b0=((bS.pageX||bS.page.x)-bW.left)/bW.width,bZ=((bS.pageY||bS.page.y)-bW.top)/bW.height}var bY={};p.extend(bY,bA);bY.isSource=true;bY.anchor=[bV,bU,0,0];bY.parentAnchor=[b0,bZ,0,0];bY.dragOptions=bM;if(bA.parent){var bQ=bY.container||bn.Defaults.Container||p.Defaults.Container;if(bQ){bY.container=bQ}else{bY.container=p.CurrentLibrary.getParent(bA.parent)}}bO=bn.addEndpoint(bE,bY);bI=true;bO.endpointWillMoveAfterConnection=bA.parent!=null;bO.endpointWillMoveTo=bA.parent?by.getElementObject(bA.parent):null;var bX=function(){if(bI){bn.deleteEndpoint(bO)}};bn.registerListener(bO.canvas,"mouseup",bX);bn.registerListener(bK,"mouseup",bX);by.trigger(bO.canvas,"mousedown",bS)};bn.registerListener(bK,"mousedown",bG);N[bE]=bG};bz=aI(bz);var bw=bz.length&&bz.constructor!=String?bz:[bz];for(var bx=0;bx<bw.length;bx++){bB(F(bw[bx]))}return bn};this.unmakeSource=function(bw,bx){bw=p.CurrentLibrary.getElementObject(bw);var by=K(bw),bv=N[by];if(bv){bn.unregisterListener(_el,"mousedown",bv)}if(!bx){delete az[by];delete a9[by];delete ac[by];delete N[by]}return bn};this.unmakeEverySource=function(){for(var bv in ac){bn.unmakeSource(bv,true)}az={};a9={};ac={};N={}};this.unmakeEveryTarget=function(){for(var bv in aj){bn.unmakeTarget(bv,true)}aZ={};bh={};ap={};aj={};return bn};this.makeSources=function(bx,by,bv){for(var bw=0;bw<bx.length;bw++){bn.makeSource(bx[bw],by,bv)}return bn};var aM=function(bz,by,bA,bv){var bw=bz=="source"?ac:aj;if(C(by)){bw[by]=bv?!bw[by]:bA}else{if(by.length){by=aI(by);for(var bx=0;bx<by.length;bx++){var bB=_el=p.CurrentLibrary.getElementObject(by[bx]),bB=K(_el);bw[bB]=bv?!bw[bB]:bA}}}return bn};this.setSourceEnabled=function(bv,bw){return aM("source",bv,bw)};this.toggleSourceEnabled=function(bv){aM("source",bv,null,true);return bn.isSourceEnabled(bv)};this.isSource=function(bv){bv=p.CurrentLibrary.getElementObject(bv);return ac[K(bv)]!=null};this.isSourceEnabled=function(bv){bv=p.CurrentLibrary.getElementObject(bv);return ac[K(bv)]===true};this.setTargetEnabled=function(bv,bw){return aM("target",bv,bw)};this.toggleTargetEnabled=function(bv){return aM("target",bv,null,true);return bn.isTargetEnabled(bv)};this.isTarget=function(bv){bv=p.CurrentLibrary.getElementObject(bv);return aj[K(bv)]!=null};this.isTargetEnabled=function(bv){bv=p.CurrentLibrary.getElementObject(bv);return aj[K(bv)]===true};this.ready=function(bv){bn.bind("ready",bv)},this.repaint=function(bw){var bx=function(by){ba(F(by))};if(typeof bw=="object"){for(var bv=0;bv<bw.length;bv++){bx(bw[bv])}}else{bx(bw)}};this.repaintEverything=function(){for(var bv in aT){ba(F(bv),null,null)}};this.removeAllEndpoints=function(bx){var bv=d(bx,"id"),by=aT[bv];if(by){for(var bw=0;bw<by.length;bw++){bn.deleteEndpoint(by[bw])}}aT[bv]=[]};this.removeEveryEndpoint=this.deleteEveryEndpoint;this.removeEndpoint=function(bv,bw){bn.deleteEndpoint(bw)};var ag={},bd=function(){for(var bw in ag){for(var bv=0;bv<ag[bw].length;bv++){var bx=ag[bw][bv];p.CurrentLibrary.unbind(bx.el,bx.event,bx.listener)}}ag={}};this.registerListener=function(bw,bv,bx){p.CurrentLibrary.bind(bw,bv,bx);V(ag,bv,{el:bw,event:bv,listener:bx})};this.unregisterListener=function(bw,bv,bx){p.CurrentLibrary.unbind(bw,bv,bx);D(ag,function(by){return by.type==bv&&by.listener==bx})};this.reset=function(){bn.deleteEveryEndpoint();bn.unbind();aZ={};aH={};bh={};ap={};az={};a5={};a9={};bd();bn.anchorManager.reset();bn.dragManager.reset()};this.setDefaultScope=function(bv){Q=bv};this.setDraggable=bk;this.setId=function(bz,bv,bB){var bC=bz.constructor==String?bz:bn.getId(bz),by=bn.getConnections({source:bC,scope:"*"},true),bx=bn.getConnections({target:bC,scope:"*"},true);bv=""+bv;if(!bB){bz=p.CurrentLibrary.getElementObject(bC);p.CurrentLibrary.setAttribute(bz,"id",bv)}bz=p.CurrentLibrary.getElementObject(bv);aT[bv]=aT[bC]||[];for(var bw=0;bw<aT[bv].length;bw++){aT[bv][bw].elementId=bv;aT[bv][bw].element=bz;aT[bv][bw].anchor.elementId=bv}delete aT[bC];bn.anchorManager.changeId(bC,bv);var bA=function(bG,bD,bF){for(var bE=0;bE<bG.length;bE++){bG[bE].endpoints[bD].elementId=bv;bG[bE].endpoints[bD].element=bz;bG[bE][bF+"Id"]=bv;bG[bE][bF]=bz}};bA(by,0,"source");bA(bx,1,"target")};this.setIdChanged=function(bw,bv){bn.setId(bw,bv,true)};this.setDebugLog=function(bv){P=bv};this.setRepaintFunction=function(bv){ar=bv};this.setSuspendDrawing=bg;this.CANVAS="canvas";this.SVG="svg";this.VML="vml";this.setRenderMode=function(bv){if(bv){bv=bv.toLowerCase()}else{return}if(bv!==p.CANVAS&&bv!==p.SVG&&bv!==p.VML){throw new Error("render mode must be one of jsPlumb.CANVAS, jsPlumb.SVG or jsPlumb.VML")}if(bv===p.SVG){if(e){X=p.SVG}else{if(A){X=p.CANVAS}else{if(b()){X=p.VML}}}}else{if(bv===p.CANVAS&&A){X=p.CANVAS}else{if(b()){X=p.VML}}}return X};this.getRenderMode=function(){return X};this.show=function(bv,bw){a3(bv,"block",bw)};this.sizeCanvas=function(bx,bv,bz,bw,by){if(bx){bx.style.height=by+"px";bx.height=by;bx.style.width=bw+"px";bx.width=bw;bx.style.left=bv+"px";bx.style.top=bz+"px"}};this.getTestHarness=function(){return{endpointsByElement:aT,endpointCount:function(bv){var bw=aT[bv];return bw?bw.length:0},connectionCount:function(bv){bv=bv||Q;var bw=aY[bv];return bw?bw.length:0},getId:K,makeAnchor:self.makeAnchor,makeDynamicAnchor:self.makeDynamicAnchor}};this.toggle=aQ;this.toggleVisible=aQ;this.toggleDraggable=bi;this.unload=function(){};this.wrap=am;this.addListener=this.bind;var bo=function(bA,bx){var by=null,bv=bA;if(bx.tagName.toLowerCase()==="svg"&&bx.parentNode){by=bx.parentNode}else{if(bx.offsetParent){by=bx.offsetParent}}if(by!=null){var bw=by.tagName.toLowerCase()==="body"?{left:0,top:0}:t(by),bz=by.tagName.toLowerCase()==="body"?{left:0,top:0}:{left:by.scrollLeft,top:by.scrollTop};bv[0]=bA[0]-bw.left+bz.left;bv[1]=bA[1]-bw.top+bz.top}return bv};var aa=function(bz){var bx=this;this.x=bz.x||0;this.y=bz.y||0;this.elementId=bz.elementId;var bw=bz.orientation||[0,0];var by=null,bv=null;this.offsets=bz.offsets||[0,0];bx.timestamp=null;this.compute=function(bE){var bD=bE.xy,bA=bE.wh,bB=bE.element,bC=bE.timestamp;if(bC&&bC===bx.timestamp){return bv}bv=[bD[0]+(bx.x*bA[0])+bx.offsets[0],bD[1]+(bx.y*bA[1])+bx.offsets[1]];bv=bo(bv,bB.canvas);bx.timestamp=bC;return bv};this.getOrientation=function(bA){return bw};this.equals=function(bA){if(!bA){return false}var bB=bA.getOrientation();var bC=this.getOrientation();return this.x==bA.x&&this.y==bA.y&&this.offsets[0]==bA.offsets[0]&&this.offsets[1]==bA.offsets[1]&&bC[0]==bB[0]&&bC[1]==bB[1]};this.getCurrentLocation=function(){return bv}};var a1=function(bB){var bz=bB.reference,bA=bB.referenceCanvas,bx=a(F(bA)),bw=0,bC=0,bv=null,by=null;this.x=0;this.y=0;this.isFloating=true;this.compute=function(bG){var bF=bG.xy,bE=bG.element,bD=[bF[0]+(bx[0]/2),bF[1]+(bx[1]/2)];bD=bo(bD,bE.canvas);by=bD;return bD};this.getOrientation=function(bE){if(bv){return bv}else{var bD=bz.getOrientation(bE);return[Math.abs(bD[0])*bw*-1,Math.abs(bD[1])*bC*-1]}};this.over=function(bD){bv=bD.getOrientation()};this.out=function(){bv=null};this.getCurrentLocation=function(){return by}};var aq=function(bx,bw,bC){this.isSelective=true;this.isDynamic=true;var bF=[],bE=this,bD=function(bG){return bG.constructor==aa?bG:bn.makeAnchor(bG,bC,bn)};for(var bB=0;bB<bx.length;bB++){bF[bB]=bD(bx[bB])}this.addAnchor=function(bG){bF.push(bD(bG))};this.getAnchors=function(){return bF};this.locked=false;var by=bF.length>0?bF[0]:null,bA=bF.length>0?0:-1,bE=this,bz=function(bI,bG,bM,bL,bH){var bK=bL[0]+(bI.x*bH[0]),bJ=bL[1]+(bI.y*bH[1]);return Math.sqrt(Math.pow(bG-bK,2)+Math.pow(bM-bJ,2))},bv=bw||function(bQ,bH,bI,bJ,bG){var bL=bI[0]+(bJ[0]/2),bK=bI[1]+(bJ[1]/2);var bN=-1,bP=Infinity;for(var bM=0;bM<bG.length;bM++){var bO=bz(bG[bM],bL,bK,bQ,bH);if(bO<bP){bN=bM+0;bP=bO}}return bG[bN]};this.compute=function(bK){var bJ=bK.xy,bG=bK.wh,bI=bK.timestamp,bH=bK.txy,bL=bK.twh;if(bE.locked||bH==null||bL==null){return by.compute(bK)}else{bK.timestamp=null}by=bv(bJ,bG,bH,bL,bF);bE.x=by.x;bE.y=by.y;return by.compute(bK)};this.getCurrentLocation=function(){return by!=null?by.getCurrentLocation():null};this.getOrientation=function(bG){return by!=null?by.getOrientation(bG):[0,0]};this.over=function(bG){if(by!=null){by.over(bG)}};this.out=function(){if(by!=null){by.out()}}};var bj={},ai={},aJ={},T={HORIZONTAL:"horizontal",VERTICAL:"vertical",DIAGONAL:"diagonal",IDENTITY:"identity"},bl=function(bE,bF,bB,by){if(bE===bF){return{orientation:T.IDENTITY,a:["top","top"]}}var bw=Math.atan2((by.centery-bB.centery),(by.centerx-bB.centerx)),bz=Math.atan2((bB.centery-by.centery),(bB.centerx-by.centerx)),bA=((bB.left<=by.left&&bB.right>=by.left)||(bB.left<=by.right&&bB.right>=by.right)||(bB.left<=by.left&&bB.right>=by.right)||(by.left<=bB.left&&by.right>=bB.right)),bG=((bB.top<=by.top&&bB.bottom>=by.top)||(bB.top<=by.bottom&&bB.bottom>=by.bottom)||(bB.top<=by.top&&bB.bottom>=by.bottom)||(by.top<=bB.top&&by.bottom>=bB.bottom));if(!(bA||bG)){var bD=null,bx=false,bv=false,bC=null;if(by.left>bB.left&&by.top>bB.top){bD=["right","top"]}else{if(by.left>bB.left&&bB.top>by.top){bD=["top","left"]}else{if(by.left<bB.left&&by.top<bB.top){bD=["top","right"]}else{if(by.left<bB.left&&by.top>bB.top){bD=["left","top"]}}}}return{orientation:T.DIAGONAL,a:bD,theta:bw,theta2:bz}}else{if(bA){return{orientation:T.HORIZONTAL,a:bB.top<by.top?["bottom","top"]:["top","bottom"],theta:bw,theta2:bz}}else{return{orientation:T.VERTICAL,a:bB.left<by.left?["right","left"]:["left","right"],theta:bw,theta2:bz}}}},a2=function(bJ,bF,bD,bE,bK,bG,bx){var bL=[],bw=bF[bK?0:1]/(bE.length+1);for(var bH=0;bH<bE.length;bH++){var bM=(bH+1)*bw,bv=bG*bF[bK?1:0];if(bx){bM=bF[bK?0:1]-bM}var bC=(bK?bM:bv),bz=bD[0]+bC,bB=bC/bF[0],bA=(bK?bv:bM),by=bD[1]+bA,bI=bA/bF[1];bL.push([bz,by,bB,bI,bE[bH][1],bE[bH][2]])}return bL},a8=function(bw,bv){return bw[0]>bv[0]?1:-1},Z=function(bv){return function(bx,bw){var by=true;if(bv){if(bx[0][0]<bw[0][0]){by=true}else{by=bx[0][1]>bw[0][1]}}else{if(bx[0][0]>bw[0][0]){by=true}else{by=bx[0][1]>bw[0][1]}}return by===false?-1:1}},O=function(bw,bv){var by=bw[0][0]<0?-Math.PI-bw[0][0]:Math.PI-bw[0][0],bx=bv[0][0]<0?-Math.PI-bv[0][0]:Math.PI-bv[0][0];if(by>bx){return 1}else{return bw[0][1]>bv[0][1]?1:-1}},a0={top:a8,right:Z(true),bottom:Z(true),left:O},ao=function(bv,bw){return bv.sort(bw)},al=function(bw,bv){var by=ae[bw],bz=ah[bw],bx=function(bG,bN,bC,bF,bL,bK,bB){if(bF.length>0){var bJ=ao(bF,a0[bG]),bH=bG==="right"||bG==="top",bA=a2(bG,bN,bC,bJ,bL,bK,bH);var bO=function(bR,bQ){var bP=bo([bQ[0],bQ[1]],bR.canvas);ai[bR.id]=[bP[0],bP[1],bQ[2],bQ[3]];aJ[bR.id]=bB};for(var bD=0;bD<bA.length;bD++){var bI=bA[bD][4],bM=bI.endpoints[0].elementId===bw,bE=bI.endpoints[1].elementId===bw;if(bM){bO(bI.endpoints[0],bA[bD])}else{if(bE){bO(bI.endpoints[1],bA[bD])}}}}};bx("bottom",by,[bz.left,bz.top],bv.bottom,true,1,[0,1]);bx("top",by,[bz.left,bz.top],bv.top,true,0,[0,-1]);bx("left",by,[bz.left,bz.top],bv.left,false,0,[-1,0]);bx("right",by,[bz.left,bz.top],bv.right,false,1,[1,0])},aE=function(){var bv={},bz={},bw=this,by={};this.reset=function(){bv={};bz={};by={}};this.newConnection=function(bD){var bF=bD.sourceId,bC=bD.targetId,bA=bD.endpoints,bE=true,bB=function(bG,bH,bJ,bI,bK){if((bF==bC)&&bJ.isContinuous){p.CurrentLibrary.removeElement(bA[1].canvas);bE=false}V(bz,bI,[bK,bH,bJ.constructor==aq])};bB(0,bA[0],bA[0].anchor,bC,bD);if(bE){bB(1,bA[1],bA[1].anchor,bF,bD)}};this.connectionDetached=function(bA){var bB=bA.connection||bA;var bG=bB.sourceId,bH=bB.targetId,bK=bB.endpoints,bF=function(bL,bM,bO,bN,bP){if(bO.constructor==a1){}else{D(bz[bN],function(bQ){return bQ[0].id==bP.id})}};bF(1,bK[1],bK[1].anchor,bG,bB);bF(0,bK[0],bK[0].anchor,bH,bB);var bC=bB.sourceId,bD=bB.targetId,bJ=bB.endpoints[0].id,bE=bB.endpoints[1].id,bI=function(bN,bL){if(bN){var bM=function(bO){return bO[4]==bL};D(bN.top,bM);D(bN.left,bM);D(bN.bottom,bM);D(bN.right,bM)}};bI(by[bC],bJ);bI(by[bD],bE);bw.redraw(bC);bw.redraw(bD)};this.add=function(bB,bA){V(bv,bA,bB)};this.changeId=function(bB,bA){bz[bA]=bz[bB];bv[bA]=bv[bB];delete bz[bB];delete bv[bB]};this.getConnectionsFor=function(bA){return bz[bA]||[]};this.getEndpointsFor=function(bA){return bv[bA]||[]};this.deleteEndpoint=function(bA){D(bv[bA.elementId],function(bB){return bB.id==bA.id})};this.clearFor=function(bA){delete bv[bA];bv[bA]=[]};var bx=function(bU,bH,bP,bE,bK,bL,bN,bJ,bW,bM,bD,bT){var bR=-1,bC=-1,bF=bE.endpoints[bN],bO=bF.id,bI=[1,0][bN],bA=[[bH,bP],bE,bK,bL,bO],bB=bU[bW],bV=bF._continuousAnchorEdge?bU[bF._continuousAnchorEdge]:null;if(bV){var bS=i(bV,function(bX){return bX[4]==bO});if(bS!=-1){bV.splice(bS,1);for(var bQ=0;bQ<bV.length;bQ++){u(bD,bV[bQ][1],function(bX){return bX.id==bV[bQ][1].id});u(bT,bV[bQ][1].endpoints[bN],function(bX){return bX.id==bV[bQ][1].endpoints[bN].id})}}}for(var bQ=0;bQ<bB.length;bQ++){if(bN==1&&bB[bQ][3]===bL&&bC==-1){bC=bQ}u(bD,bB[bQ][1],function(bX){return bX.id==bB[bQ][1].id});u(bT,bB[bQ][1].endpoints[bN],function(bX){return bX.id==bB[bQ][1].endpoints[bN].id})}if(bR!=-1){bB[bR]=bA}else{var bG=bJ?bC!=-1?bC:0:bB.length;bB.splice(bG,0,bA)}bF._continuousAnchorEdge=bW};this.redraw=function(bP,bR,bC,bF){var b0=bv[bP]||[],bZ=bz[bP]||[],bB=[],bY=[],bD=[];bC=bC||r();bF=bF||{left:0,top:0};if(bR){bR={left:bR.left+bF.left,top:bR.top+bF.top}}W({elId:bP,offset:bR,recalc:false,timestamp:bC});var bK=ah[bP],bG=ae[bP],bM={};for(var bW=0;bW<bZ.length;bW++){var bH=bZ[bW][0],bJ=bH.sourceId,bE=bH.targetId,bI=bH.endpoints[0].anchor.isContinuous,bO=bH.endpoints[1].anchor.isContinuous;if(bI||bO){var bX=bJ+"_"+bE,bU=bE+"_"+bJ,bT=bM[bX],bN=bH.sourceId==bP?1:0;if(bI&&!by[bJ]){by[bJ]={top:[],right:[],bottom:[],left:[]}}if(bO&&!by[bE]){by[bE]={top:[],right:[],bottom:[],left:[]}}if(bP!=bE){W({elId:bE,timestamp:bC})}if(bP!=bJ){W({elId:bJ,timestamp:bC})}var bL=aN(bE),bA=aN(bJ);if(bE==bJ&&(bI||bO)){bx(by[bJ],-Math.PI/2,0,bH,false,bE,0,false,"top",bJ,bB,bY)}else{if(!bT){bT=bl(bJ,bE,bA.o,bL.o);bM[bX]=bT}if(bI){bx(by[bJ],bT.theta,0,bH,false,bE,0,false,bT.a[0],bJ,bB,bY)}if(bO){bx(by[bE],bT.theta2,-1,bH,true,bJ,1,true,bT.a[1],bE,bB,bY)}}if(bI){u(bD,bJ,function(b1){return b1===bJ})}if(bO){u(bD,bE,function(b1){return b1===bE})}u(bB,bH,function(b1){return b1.id==bH.id});if((bI&&bN==0)||(bO&&bN==1)){u(bY,bH.endpoints[bN],function(b1){return b1.id==bH.endpoints[bN].id})}}}for(var bW=0;bW<bD.length;bW++){al(bD[bW],by[bD[bW]])}for(var bW=0;bW<b0.length;bW++){b0[bW].paint({timestamp:bC,offset:bK,dimensions:bG})}for(var bW=0;bW<bY.length;bW++){bY[bW].paint({timestamp:bC,offset:bK,dimensions:bG})}for(var bW=0;bW<bZ.length;bW++){var bQ=bZ[bW][1];if(bQ.anchor.constructor==aq){bQ.paint({elementWithPrecedence:bP});u(bB,bZ[bW][0],function(b1){return b1.id==bZ[bW][0].id});for(var bV=0;bV<bQ.connections.length;bV++){if(bQ.connections[bV]!==bZ[bW][0]){u(bB,bQ.connections[bV],function(b1){return b1.id==bQ.connections[bV].id})}}}else{if(bQ.anchor.constructor==aa){u(bB,bZ[bW][0],function(b1){return b1.id==bZ[bW][0].id})}}}var bS=bf[bP];if(bS){bS.paint({timestamp:bC,recalc:false,elId:bP})}for(var bW=0;bW<bB.length;bW++){bB[bW].paint({elId:bP,timestamp:bC,recalc:false})}};this.rehomeEndpoint=function(bA,bE){var bB=bv[bA]||[],bC=bn.getId(bE);for(var bD=0;bD<bB.length;bD++){bw.add(bB[bD],bC)}bB.splice(0,bB.length)}};bn.anchorManager=new aE();bn.continuousAnchorFactory={get:function(bw){var bv=bj[bw.elementId];if(!bv){bv={type:"Continuous",compute:function(bx){return ai[bx.element.id]||[0,0]},getCurrentLocation:function(bx){return ai[bx.id]||[0,0]},getOrientation:function(bx){return aJ[bx.id]||[0,0]},isDynamic:true,isContinuous:true};bj[bw.elementId]=bv}return bv}};var aV=function(){var by={},bx=[],bw={},bv={};this.register=function(bB){var bA=p.CurrentLibrary;bB=bA.getElementObject(bB);var bD=bn.getId(bB),bz=bA.getDOMElement(bB);if(!by[bD]){by[bD]=bB;bx.push(bB);bw[bD]={}}var bC=function(bH){var bK=bA.getElementObject(bH),bJ=bA.getOffset(bK);for(var bE=0;bE<bH.childNodes.length;bE++){if(bH.childNodes[bE].nodeType!=3){var bG=bA.getElementObject(bH.childNodes[bE]),bI=bn.getId(bG,null,true);if(bI&&bv[bI]&&bv[bI]>0){var bF=bA.getOffset(bG);bw[bD][bI]={id:bI,offset:{left:bF.left-bJ.left,top:bF.top-bJ.top}}}}}};bC(bz)};this.endpointAdded=function(bB){var bF=p.CurrentLibrary,bI=document.body,bz=bn.getId(bB),bH=bF.getDOMElement(bB),bA=bH.parentNode,bD=bA==bI;bv[bz]=bv[bz]?bv[bz]+1:1;while(bA!=bI){var bE=bn.getId(bA);if(by[bE]){var bK=-1,bG=bF.getElementObject(bA),bC=bF.getOffset(bG);if(bw[bE][bz]==null){var bJ=p.CurrentLibrary.getOffset(bB);bw[bE][bz]={id:bz,offset:{left:bJ.left-bC.left,top:bJ.top-bC.top}}}break}bA=bA.parentNode}};this.endpointDeleted=function(bA){if(bv[bA.elementId]){bv[bA.elementId]--;if(bv[bA.elementId]<=0){for(var bz in bw){delete bw[bz][bA.elementId]}}}};this.getElementsForDraggable=function(bz){return bw[bz]};this.reset=function(){by={};bx=[];bw={};bv={}}};bn.dragManager=new aV();var ax=function(bN){var bG=this,bx=true;bG.idPrefix="_jsplumb_c_";bG.defaultLabelLocation=0.5;bG.defaultOverlayKeys=["Overlays","ConnectionOverlays"];this.parent=bN.parent;z.apply(this,arguments);this.isVisible=function(){return bx};this.setVisible=function(bP){bx=bP;bG[bP?"showOverlays":"hideOverlays"]();if(bG.connector&&bG.connector.canvas){bG.connector.canvas.style.display=bP?"block":"none"}};this.source=F(bN.source);this.target=F(bN.target);if(bN.sourceEndpoint){this.source=bN.sourceEndpoint.endpointWillMoveTo||bN.sourceEndpoint.getElement()}if(bN.targetEndpoint){this.target=bN.targetEndpoint.getElement()}bG.previousConnection=bN.previousConnection;var bD=bN.cost;bG.getCost=function(){return bD};bG.setCost=function(bP){bD=bP};var bB=bN.bidirectional===false?false:true;bG.isBidirectional=function(){return bB};this.sourceId=d(this.source,"id");this.targetId=d(this.target,"id");this.getAttachedElements=function(){return bG.endpoints};this.scope=bN.scope;this.endpoints=[];this.endpointStyles=[];var bM=function(bQ,bP){if(bQ){return bn.makeAnchor(bQ,bP,bn)}},bK=function(bP,bV,bQ,bS,bT,bR,bU){if(bP){bG.endpoints[bV]=bP;bP.addConnection(bG)}else{if(!bQ.endpoints){bQ.endpoints=[null,null]}var b1=bQ.endpoints[bV]||bQ.endpoint||bn.Defaults.Endpoints[bV]||p.Defaults.Endpoints[bV]||bn.Defaults.Endpoint||p.Defaults.Endpoint;if(!bQ.endpointStyles){bQ.endpointStyles=[null,null]}if(!bQ.endpointHoverStyles){bQ.endpointHoverStyles=[null,null]}var bZ=bQ.endpointStyles[bV]||bQ.endpointStyle||bn.Defaults.EndpointStyles[bV]||p.Defaults.EndpointStyles[bV]||bn.Defaults.EndpointStyle||p.Defaults.EndpointStyle;if(bZ.fillStyle==null&&bR!=null){bZ.fillStyle=bR.strokeStyle}if(bZ.outlineColor==null&&bR!=null){bZ.outlineColor=bR.outlineColor}if(bZ.outlineWidth==null&&bR!=null){bZ.outlineWidth=bR.outlineWidth}var bY=bQ.endpointHoverStyles[bV]||bQ.endpointHoverStyle||bn.Defaults.EndpointHoverStyles[bV]||p.Defaults.EndpointHoverStyles[bV]||bn.Defaults.EndpointHoverStyle||p.Defaults.EndpointHoverStyle;if(bU!=null){if(bY==null){bY={}}if(bY.fillStyle==null){bY.fillStyle=bU.strokeStyle}}var bX=bQ.anchors?bQ.anchors[bV]:bQ.anchor?bQ.anchor:bM(bn.Defaults.Anchors[bV],bT)||bM(p.Defaults.Anchors[bV],bT)||bM(bn.Defaults.Anchor,bT)||bM(p.Defaults.Anchor,bT),b0=bQ.uuids?bQ.uuids[bV]:null,bW=aF({paintStyle:bZ,hoverPaintStyle:bY,endpoint:b1,connections:[bG],uuid:b0,anchor:bX,source:bS,scope:bQ.scope,container:bQ.container,reattach:bQ.reattach,detachable:bQ.detachable});bG.endpoints[bV]=bW;if(bQ.drawEndpoints===false){bW.setVisible(false,true,true)}return bW}};var bI=bK(bN.sourceEndpoint,0,bN,bG.source,bG.sourceId,bN.paintStyle,bN.hoverPaintStyle);if(bI){V(aT,this.sourceId,bI)}var by=((bG.sourceId==bG.targetId)&&bN.targetEndpoint==null)?bI:bN.targetEndpoint,bH=bK(by,1,bN,bG.target,bG.targetId,bN.paintStyle,bN.hoverPaintStyle);if(bH){V(aT,this.targetId,bH)}if(!this.scope){this.scope=this.endpoints[0].scope}if(bN.deleteEndpointsOnDetach){bG.endpointsToDeleteOnDetach=[bI,bH]}var bw=bn.Defaults.ConnectionsDetachable;if(bN.detachable===false){bw=false}if(bG.endpoints[0].connectionsDetachable===false){bw=false}if(bG.endpoints[1].connectionsDetachable===false){bw=false}if(bD==null){bD=bG.endpoints[0].getConnectionCost()}if(bN.bidirectional==null){bB=bG.endpoints[0].areConnectionsBidirectional()}this.isDetachable=function(){return bw===true};this.setDetachable=function(bP){bw=bP===true};var bO=p.extend({},this.endpoints[0].getParameters());p.extend(bO,this.endpoints[1].getParameters());p.extend(bO,bG.getParameters());bG.setParameters(bO);var bE=bG.setHover;bG.setHover=function(bP){bG.connector.setHover.apply(bG.connector,arguments);bE.apply(bG,arguments)};var bL=function(bP){if(x==null){bG.setHover(bP,false)}};this.setConnector=function(bP,bQ){if(bG.connector!=null){aX(bG.connector.getDisplayElements(),bG.parent)}var bR={_jsPlumb:bG._jsPlumb,parent:bN.parent,cssClass:bN.cssClass,container:bN.container,tooltip:bG.tooltip};if(C(bP)){this.connector=new p.Connectors[X][bP](bR)}else{if(n(bP)){this.connector=new p.Connectors[X][bP[0]](p.extend(bP[1],bR))}}bG.canvas=bG.connector.canvas;G(bG.connector,bG,bL);if(!bQ){bG.repaint()}};bG.setConnector(this.endpoints[0].connector||this.endpoints[1].connector||bN.connector||bn.Defaults.Connector||p.Defaults.Connector,true);this.setPaintStyle(this.endpoints[0].connectorStyle||this.endpoints[1].connectorStyle||bN.paintStyle||bn.Defaults.PaintStyle||p.Defaults.PaintStyle,true);this.setHoverPaintStyle(this.endpoints[0].connectorHoverStyle||this.endpoints[1].connectorHoverStyle||bN.hoverPaintStyle||bn.Defaults.HoverPaintStyle||p.Defaults.HoverPaintStyle,true);this.paintStyleInUse=this.getPaintStyle();this.moveParent=function(bS){var bR=p.CurrentLibrary,bQ=bR.getParent(bG.connector.canvas);if(bG.connector.bgCanvas){bR.removeElement(bG.connector.bgCanvas,bQ);bR.appendElement(bG.connector.bgCanvas,bS)}bR.removeElement(bG.connector.canvas,bQ);bR.appendElement(bG.connector.canvas,bS);for(var bP=0;bP<bG.overlays.length;bP++){if(bG.overlays[bP].isAppendedAtTopLevel){bR.removeElement(bG.overlays[bP].canvas,bQ);bR.appendElement(bG.overlays[bP].canvas,bS);if(bG.overlays[bP].reattachListeners){bG.overlays[bP].reattachListeners(bG.connector)}}}if(bG.connector.reattachListeners){bG.connector.reattachListeners()}};W({elId:this.sourceId});W({elId:this.targetId});var bA=ah[this.sourceId],bz=ae[this.sourceId],bv=ah[this.targetId],bC=ae[this.targetId],bF=r(),bJ=this.endpoints[0].anchor.compute({xy:[bA.left,bA.top],wh:bz,element:this.endpoints[0],elementId:this.endpoints[0].elementId,txy:[bv.left,bv.top],twh:bC,tElement:this.endpoints[1],timestamp:bF});this.endpoints[0].paint({anchorLoc:bJ,timestamp:bF});bJ=this.endpoints[1].anchor.compute({xy:[bv.left,bv.top],wh:bC,element:this.endpoints[1],elementId:this.endpoints[1].elementId,txy:[bA.left,bA.top],twh:bz,tElement:this.endpoints[0],timestamp:bF});this.endpoints[1].paint({anchorLoc:bJ,timestamp:bF});this.paint=function(b6){b6=b6||{};var bX=b6.elId,bY=b6.ui,bV=b6.recalc,bQ=b6.timestamp,bZ=false,b5=bZ?this.sourceId:this.targetId,bU=bZ?this.targetId:this.sourceId,bR=bZ?0:1,b7=bZ?1:0;var b8=W({elId:bX,offset:bY,recalc:bV,timestamp:bQ}),bW=W({elId:b5,timestamp:bQ});var b1=this.endpoints[b7],bP=this.endpoints[bR],bT=b1.anchor.getCurrentLocation(b1),b4=bP.anchor.getCurrentLocation(bP);var bS=0;for(var b3=0;b3<bG.overlays.length;b3++){var b0=bG.overlays[b3];if(b0.isVisible()){bS=Math.max(bS,b0.computeMaxSize(bG.connector))}}var b2=this.connector.compute(bT,b4,this.endpoints[b7],this.endpoints[bR],this.endpoints[b7].anchor,this.endpoints[bR].anchor,bG.paintStyleInUse.lineWidth,bS,b8,bW);bG.connector.paint(b2,bG.paintStyleInUse);for(var b3=0;b3<bG.overlays.length;b3++){var b0=bG.overlays[b3];if(b0.isVisible){bG.overlayPlacements[b3]=b0.draw(bG.connector,bG.paintStyleInUse,b2)}}};this.repaint=function(bQ){bQ=bQ||{};var bP=!(bQ.recalc===false);this.paint({elId:this.sourceId,recalc:bP,timestamp:bQ.timestamp})}};var bc=function(bw){var bv=false;return{drag:function(){if(bv){bv=false;return true}var bx=p.CurrentLibrary.getUIPosition(arguments),by=bw.element;if(by){p.CurrentLibrary.setOffset(by,bx);ba(F(by),bx)}},stopDrag:function(){bv=true}}};var at=function(bz,by,bA,bx,bv){var bw=new a1({reference:by,referenceCanvas:bx});return aF({paintStyle:bz,endpoint:bA,anchor:bw,source:bv,scope:"__floating"})};var R=function(bx,bv){var bz=document.createElement("div");bz.style.position="absolute";var bw=F(bz);aW(bz,bv);var by=K(bw);W({elId:by});bx.id=by;bx.element=bw};var af=function(b0){var bO=this;bO.idPrefix="_jsplumb_e_";bO.defaultLabelLocation=[0.5,0.5];bO.defaultOverlayKeys=["Overlays","EndpointOverlays"];this.parent=b0.parent;z.apply(this,arguments);b0=b0||{};var bA=true,by=!(b0.enabled===false);this.isVisible=function(){return bA};this.setVisible=function(b3,b6,b2){bA=b3;if(bO.canvas){bO.canvas.style.display=b3?"block":"none"}bO[b3?"showOverlays":"hideOverlays"]();if(!b6){for(var b5=0;b5<bO.connections.length;b5++){bO.connections[b5].setVisible(b3);if(!b2){var b4=bO===bO.connections[b5].endpoints[0]?1:0;if(bO.connections[b5].endpoints[b4].connections.length==1){bO.connections[b5].endpoints[b4].setVisible(b3,true,true)}}}}};this.isEnabled=function(){return by};this.setEnabled=function(b2){by=b2};var bN=b0.source,bH=b0.uuid,bY=null,bC=null;if(bH){aU[bH]=bO}var bF=d(bN,"id");this.elementId=bF;this.element=bN;var bx=b0.connectionCost;this.getConnectionCost=function(){return bx};this.setConnectionCost=function(b2){bx=b2};var bX=b0.connectionsBidirectional===false?false:true;this.areConnectionsBidirectional=function(){return bX};this.setConnectionsBidirectional=function(b2){bX=b2};bO.anchor=b0.anchor?bn.makeAnchor(b0.anchor,bF,bn):b0.anchors?bn.makeAnchor(b0.anchors,bF,bn):bn.makeAnchor("TopCenter",bF,bn);if(!b0._transient){bn.anchorManager.add(bO,bF)}var bL=null,bQ=null;this.setEndpoint=function(b2){var b3={_jsPlumb:bO._jsPlumb,parent:b0.parent,container:b0.container,tooltip:b0.tooltip,connectorTooltip:b0.connectorTooltip,endpoint:bO};if(C(b2)){bL=new p.Endpoints[X][b2](b3)}else{if(n(b2)){b3=p.extend(b2[1],b3);bL=new p.Endpoints[X][b2[0]](b3)}else{bL=b2.clone()}}var b4=p.extend({},b3);bL.clone=function(){var b5=new Object();bL.constructor.apply(b5,[b4]);return b5};bO.endpoint=bL;bO.type=bO.endpoint.type};this.setEndpoint(b0.endpoint||bn.Defaults.Endpoint||p.Defaults.Endpoint||"Dot");bQ=bL;var bM=bO.setHover;bO.setHover=function(){bO.endpoint.setHover.apply(bO.endpoint,arguments);bM.apply(bO,arguments)};var b1=function(b2){if(bO.connections.length>0){bO.connections[0].setHover(b2,false)}else{bO.setHover(b2)}};G(bO.endpoint,bO,b1);this.setPaintStyle(b0.paintStyle||b0.style||bn.Defaults.EndpointStyle||p.Defaults.EndpointStyle,true);this.setHoverPaintStyle(b0.hoverPaintStyle||bn.Defaults.EndpointHoverStyle||p.Defaults.EndpointHoverStyle,true);this.paintStyleInUse=this.getPaintStyle();var bJ=this.getPaintStyle();this.connectorStyle=b0.connectorStyle;this.connectorHoverStyle=b0.connectorHoverStyle;this.connectorOverlays=b0.connectorOverlays;this.connector=b0.connector;this.connectorTooltip=b0.connectorTooltip;this.isSource=b0.isSource||false;this.isTarget=b0.isTarget||false;var bU=b0.maxConnections||bn.Defaults.MaxConnections;this.getAttachedElements=function(){return bO.connections};this.canvas=this.endpoint.canvas;this.connections=b0.connections||[];this.scope=b0.scope||Q;this.timestamp=null;bO.isReattach=b0.reattach||false;bO.connectionsDetachable=bn.Defaults.ConnectionsDetachable;if(b0.connectionsDetachable===false||b0.detachable===false){bO.connectionsDetachable=false}var bI=b0.dragAllowedWhenFull||true;this.computeAnchor=function(b2){return bO.anchor.compute(b2)};this.addConnection=function(b2){bO.connections.push(b2)};this.detach=function(b3,b8,b4,cb,b2){var ca=i(bO.connections,function(cd){return cd.id==b3.id}),b9=false;cb=(cb!==false);if(ca>=0){if(b4||b3._forceDetach||b3.isDetachable()||b3.isDetachAllowed(b3)){var cc=b3.endpoints[0]==bO?b3.endpoints[1]:b3.endpoints[0];if(b4||b3._forceDetach||(bO.isDetachAllowed(b3))){bO.connections.splice(ca,1);if(!b8){cc.detach(b3,true,b4);if(b3.endpointsToDeleteOnDetach){for(var b7=0;b7<b3.endpointsToDeleteOnDetach.length;b7++){var b5=b3.endpointsToDeleteOnDetach[b7];if(b5&&b5.connections.length==0){bn.deleteEndpoint(b5)}}}}aX(b3.connector.getDisplayElements(),b3.parent);D(aY[b3.scope],function(cd){return cd.id==b3.id});b9=true;var b6=(!b8&&cb);a7(b3,b6,b2)}}}return b9};this.detachAll=function(b3,b2){while(bO.connections.length>0){bO.detach(bO.connections[0],false,true,b3,b2)}};this.detachFrom=function(b5,b4,b2){var b6=[];for(var b3=0;b3<bO.connections.length;b3++){if(bO.connections[b3].endpoints[1]==b5||bO.connections[b3].endpoints[0]==b5){b6.push(bO.connections[b3])}}for(var b3=0;b3<b6.length;b3++){if(bO.detach(b6[b3],false,true,b4,b2)){b6[b3].setHover(false,false)}}};this.detachFromConnection=function(b3){var b2=i(bO.connections,function(b4){return b4.id==b3.id});if(b2>=0){bO.connections.splice(b2,1)}};this.getElement=function(){return bN};this.setElement=function(b5,b2){var b7=K(b5);D(aT[bO.elementId],function(b8){return b8.id==bO.id});bN=F(b5);bF=K(bN);bO.elementId=bF;var b6=aw({source:b7,container:b2}),b4=bz.getParent(bO.canvas);bz.removeElement(bO.canvas,b4);bz.appendElement(bO.canvas,b6);for(var b3=0;b3<bO.connections.length;b3++){bO.connections[b3].moveParent(b6);bO.connections[b3].sourceId=bF;bO.connections[b3].source=bN}V(aT,b7,bO)};this.getUuid=function(){return bH};this.makeInPlaceCopy=function(){var b4=bO.anchor.getCurrentLocation(bO),b3=bO.anchor.getOrientation(bO),b2={compute:function(){return[b4[0],b4[1]]},getCurrentLocation:function(){return[b4[0],b4[1]]},getOrientation:function(){return b3}};return aF({anchor:b2,source:bN,paintStyle:this.getPaintStyle(),endpoint:bL,_transient:true,scope:bO.scope})};this.isConnectedTo=function(b4){var b3=false;if(b4){for(var b2=0;b2<bO.connections.length;b2++){if(bO.connections[b2].endpoints[1]==b4){b3=true;break}}}return b3};this.isFloating=function(){return bY!=null};this.connectorSelector=function(){var b2=bO.connections[0];if(bO.isTarget&&b2){return b2}else{return(bO.connections.length<bU)||bU==-1?null:b2}};this.isFull=function(){return !(bO.isFloating()||bU<1||bO.connections.length<bU)};this.setDragAllowedWhenFull=function(b2){bI=b2};this.setStyle=bO.setPaintStyle;this.equals=function(b2){return this.anchor.equals(b2.anchor)};var bK=function(b3){var b2=0;if(b3!=null){for(var b4=0;b4<bO.connections.length;b4++){if(bO.connections[b4].sourceId==b3||bO.connections[b4].targetId==b3){b2=b4;break}}}return bO.connections[b2]};this.paint=function(b5){b5=b5||{};var cb=b5.timestamp,ca=!(b5.recalc===false);if(!cb||bO.timestamp!==cb){W({elId:bF,timestamp:cb,recalc:ca});var ch=b5.offset||ah[bF];if(ch){var b8=b5.anchorPoint,b6=b5.connectorPaintStyle;if(b8==null){var b2=b5.dimensions||ae[bF];if(ch==null||b2==null){W({elId:bF,timestamp:cb});ch=ah[bF];b2=ae[bF]}var b4={xy:[ch.left,ch.top],wh:b2,element:bO,timestamp:cb};if(ca&&bO.anchor.isDynamic&&bO.connections.length>0){var ce=bK(b5.elementWithPrecedence),cg=ce.endpoints[0]==bO?1:0,b7=cg==0?ce.sourceId:ce.targetId,cd=ah[b7],cf=ae[b7];b4.txy=[cd.left,cd.top];b4.twh=cf;b4.tElement=ce.endpoints[cg]}b8=bO.anchor.compute(b4)}var cc=bL.compute(b8,bO.anchor.getOrientation(bO),bO.paintStyleInUse,b6||bO.paintStyleInUse);bL.paint(cc,bO.paintStyleInUse,bO.anchor);bO.timestamp=cb;for(var b9=0;b9<bO.overlays.length;b9++){var b3=bO.overlays[b9];if(b3.isVisible){bO.overlayPlacements[b9]=b3.draw(bO.endpoint,bO.paintStyleInUse,cc)}}}}};this.repaint=this.paint;this.removeConnection=this.detach;if(p.CurrentLibrary.isDragSupported(bN)){var bT={id:null,element:null},bS=null,bw=false,bB=null,bv=bc(bT);var bD=function(){bS=bO.connectorSelector();var b2=true;if(!bO.isEnabled()){b2=false}if(bS==null&&!b0.isSource){b2=false}if(b0.isSource&&bO.isFull()&&!bI){b2=false}if(bS!=null&&!bS.isDetachable()){b2=false}if(b2===false){if(p.CurrentLibrary.stopDrag){p.CurrentLibrary.stopDrag()}bv.stopDrag();return false}if(bS&&!bO.isFull()&&b0.isSource){bS=null}W({elId:bF});bC=bO.makeInPlaceCopy();bC.paint();R(bT,bO.parent);var b8=F(bC.canvas),b6=p.CurrentLibrary.getOffset(b8),b3=bo([b6.left,b6.top],bC.canvas);p.CurrentLibrary.setOffset(bT.element,{left:b3[0],top:b3[1]});if(bO.parentAnchor){bO.anchor=bn.makeAnchor(bO.parentAnchor,bO.elementId,bn)}f(F(bO.canvas),"dragId",bT.id);f(F(bO.canvas),"elId",bF);if(b0.proxy){bO.setPaintStyle(b0.proxy.paintStyle)}bY=at(bO.getPaintStyle(),bO.anchor,bL,bO.canvas,bT.element);if(bS==null){bO.anchor.locked=true;bO.setHover(false,false);bS=ad({sourceEndpoint:bO,targetEndpoint:bY,source:bO.endpointWillMoveTo||F(bN),target:bT.element,anchors:[bO.anchor,bY.anchor],paintStyle:b0.connectorStyle,hoverPaintStyle:b0.connectorHoverStyle,connector:b0.connector,overlays:b0.connectorOverlays})}else{bw=true;bS.connector.setHover(false,false);bE(F(bC.canvas),false,true);var b5=bS.endpoints[0].id==bO.id?0:1;bS.floatingAnchorIndex=b5;bO.detachFromConnection(bS);var b9=F(bO.canvas),b7=p.CurrentLibrary.getDragScope(b9);f(b9,"originalScope",b7);var b4=p.CurrentLibrary.getDropScope(b9);p.CurrentLibrary.setDragScope(b9,b4);if(b5==0){bB=[bS.source,bS.sourceId,bW,b7];bS.source=bT.element;bS.sourceId=bT.id}else{bB=[bS.target,bS.targetId,bW,b7];bS.target=bT.element;bS.targetId=bT.id}bS.endpoints[b5==0?1:0].anchor.locked=true;bS.suspendedEndpoint=bS.endpoints[b5];bS.suspendedEndpoint.setHover(false);bS.endpoints[b5]=bY;a4(bS)}bf[bT.id]=bS;bY.addConnection(bS);V(aT,bT.id,bY);bn.currentlyDragging=true};var bz=p.CurrentLibrary,bV=b0.dragOptions||{},bP=p.extend({},bz.defaultDragOptions),bR=bz.dragEvents.start,bZ=bz.dragEvents.stop,bG=bz.dragEvents.drag;bV=p.extend(bP,bV);bV.scope=bV.scope||bO.scope;bV[bR]=am(bV[bR],bD);bV[bG]=am(bV[bG],bv.drag);bV[bZ]=am(bV[bZ],function(){var b3=bz.getDropEvent(arguments);bn.currentlyDragging=false;D(aT[bT.id],function(b4){return b4.id==bY.id});aX([bT.element[0],bY.canvas],bN);au(bC.canvas,bN);bn.anchorManager.clearFor(bT.id);var b2=bS.floatingAnchorIndex==null?1:bS.floatingAnchorIndex;bS.endpoints[b2==0?1:0].anchor.locked=false;bO.setPaintStyle(bJ);if(bS.endpoints[b2]==bY){if(bw&&bS.suspendedEndpoint){if(b2==0){bS.source=bB[0];bS.sourceId=bB[1]}else{bS.target=bB[0];bS.targetId=bB[1]}p.CurrentLibrary.setDragScope(bB[2],bB[3]);bS.endpoints[b2]=bS.suspendedEndpoint;if(bO.isReattach||bS._forceDetach||!bS.endpoints[b2==0?1:0].detach(bS,false,false,true,b3)){bS.setHover(false);bS.floatingAnchorIndex=null;bS.suspendedEndpoint.addConnection(bS);bn.repaint(bB[1])}bS._forceDetach=null}else{aX(bS.connector.getDisplayElements(),bO.parent);bO.detachFromConnection(bS)}}bO.anchor.locked=false;bO.paint({recalc:false});bS.setHover(false,false);aR(bS);bS=null;bC=null;delete aT[bY.elementId];bY.anchor=null;bY=null;bn.currentlyDragging=false});var bW=F(bO.canvas);p.CurrentLibrary.initDraggable(bW,bV,true)}var bE=function(b4,b9,b7,ca){if((b0.isTarget||b9)&&p.CurrentLibrary.isDropSupported(bN)){var b5=b0.dropOptions||bn.Defaults.DropOptions||p.Defaults.DropOptions;b5=p.extend({},b5);b5.scope=b5.scope||bO.scope;var b3=p.CurrentLibrary.dragEvents.drop,b8=p.CurrentLibrary.dragEvents.over,b2=p.CurrentLibrary.dragEvents.out,b6=function(){var cb=p.CurrentLibrary.getDropEvent(arguments),co=F(p.CurrentLibrary.getDragObject(arguments)),cc=d(co,"dragId"),cf=d(co,"elId"),cn=d(co,"originalScope"),ci=bf[cc];if(ci!=null){var ck=ci.floatingAnchorIndex==null?1:ci.floatingAnchorIndex,cl=ck==0?1:0;if(cn){p.CurrentLibrary.setDragScope(co,cn)}var cm=ca!=null?ca.isEnabled():true;if(!bO.isFull()&&!(ck==0&&!bO.isSource)&&!(ck==1&&!bO.isTarget)&&cm){var cg=true;if(ci.suspendedEndpoint&&ci.suspendedEndpoint.id!=bO.id){if(ck==0){ci.source=ci.suspendedEndpoint.element;ci.sourceId=ci.suspendedEndpoint.elementId}else{ci.target=ci.suspendedEndpoint.element;ci.targetId=ci.suspendedEndpoint.elementId}if(!ci.isDetachAllowed(ci)||!ci.endpoints[ck].isDetachAllowed(ci)||!ci.suspendedEndpoint.isDetachAllowed(ci)||!bn.checkCondition("beforeDetach",ci)){cg=false}}if(ck==0){ci.source=bO.element;ci.sourceId=bO.elementId}else{ci.target=bO.element;ci.targetId=bO.elementId}cg=cg&&bO.isDropAllowed(ci.sourceId,ci.targetId,ci.scope,ci,bO);if(cg){ci.endpoints[ck].detachFromConnection(ci);if(ci.suspendedEndpoint){ci.suspendedEndpoint.detachFromConnection(ci)}ci.endpoints[ck]=bO;bO.addConnection(ci);var ce=bO.getParameters();for(var cj in ce){ci.setParameter(cj,ce[cj])}if(!ci.suspendedEndpoint){if(ce.draggable){p.CurrentLibrary.initDraggable(bO.element,bV,true)}}else{var ch=ci.suspendedEndpoint.getElement(),cd=ci.suspendedEndpoint.elementId;a7({source:ck==0?ch:ci.source,target:ck==1?ch:ci.target,sourceId:ck==0?cd:ci.sourceId,targetId:ck==1?cd:ci.targetId,sourceEndpoint:ck==0?ci.suspendedEndpoint:ci.endpoints[0],targetEndpoint:ck==1?ci.suspendedEndpoint:ci.endpoints[1],connection:ci},true,cb)}bt(ci,null,cb)}else{if(ci.suspendedEndpoint){ci.endpoints[ck]=ci.suspendedEndpoint;ci.setHover(false);ci._forceDetach=true;if(ck==0){ci.source=ci.suspendedEndpoint.element;ci.sourceId=ci.suspendedEndpoint.elementId}else{ci.target=ci.suspendedEndpoint.element;ci.targetId=ci.suspendedEndpoint.elementId}ci.suspendedEndpoint.addConnection(ci);ci.endpoints[0].repaint();ci.repaint();bn.repaint(ci.source.elementId);ci._forceDetach=false}}ci.floatingAnchorIndex=null}bn.currentlyDragging=false;delete bf[cc]}};b5[b3]=am(b5[b3],b6);b5[b8]=am(b5[b8],function(){if(bO.isTarget){var cc=p.CurrentLibrary.getDragObject(arguments),ce=d(F(cc),"dragId"),cd=bf[ce];if(cd!=null){var cb=cd.floatingAnchorIndex==null?1:cd.floatingAnchorIndex;cd.endpoints[cb].anchor.over(bO.anchor)}}});b5[b2]=am(b5[b2],function(){if(bO.isTarget){var cc=p.CurrentLibrary.getDragObject(arguments),ce=d(F(cc),"dragId"),cd=bf[ce];if(cd!=null){var cb=cd.floatingAnchorIndex==null?1:cd.floatingAnchorIndex;cd.endpoints[cb].anchor.out()}}});p.CurrentLibrary.initDroppable(b4,b5,true,b7)}};bE(F(bO.canvas),true,!(b0._transient||bO.anchor.isFloating),bO);return bO}};var p=window.jsPlumb=new y();p.getInstance=function(L){var K=new y(L);K.init();return K};var s=function(K,P,M,L,O,N){return function(R){R=R||{};var Q=R.jsPlumbInstance.makeAnchor([K,P,M,L,0,0],R.elementId,R.jsPlumbInstance);Q.type=O;if(N){N(Q,R)}return Q}};p.Anchors.TopCenter=s(0.5,0,0,-1,"TopCenter");p.Anchors.BottomCenter=s(0.5,1,0,1,"BottomCenter");p.Anchors.LeftMiddle=s(0,0.5,-1,0,"LeftMiddle");p.Anchors.RightMiddle=s(1,0.5,1,0,"RightMiddle");p.Anchors.Center=s(0.5,0.5,0,0,"Center");p.Anchors.TopRight=s(1,0,0,-1,"TopRight");p.Anchors.BottomRight=s(1,1,0,1,"BottomRight");p.Anchors.TopLeft=s(0,0,0,-1,"TopLeft");p.Anchors.BottomLeft=s(0,1,0,1,"BottomLeft");p.Defaults.DynamicAnchors=function(K){return K.jsPlumbInstance.makeAnchors(["TopCenter","RightMiddle","BottomCenter","LeftMiddle"],K.elementId,K.jsPlumbInstance)};p.Anchors.AutoDefault=function(L){var K=L.jsPlumbInstance.makeDynamicAnchor(p.Defaults.DynamicAnchors(L));K.type="AutoDefault";return K};p.Anchors.Assign=s(0,0,0,0,"Assign",function(L,M){var K=M.position||"Fixed";L.positionFinder=K.constructor==String?M.jsPlumbInstance.AnchorPositionFinders[K]:K;L.constructorParams=M});p.Anchors.Continuous=function(K){return K.jsPlumbInstance.continuousAnchorFactory.get(K)};p.AnchorPositionFinders={Fixed:function(N,K,M,L){return[(N.left-K.left)/M[0],(N.top-K.top)/M[1]]},Grid:function(K,T,O,L){var S=K.left-T.left,R=K.top-T.top,Q=O[0]/(L.grid[0]),P=O[1]/(L.grid[1]),N=Math.floor(S/Q),M=Math.floor(R/P);return[((N*Q)+(Q/2))/O[0],((M*P)+(P/2))/O[1]]}}})();(function(){jsPlumb.DOMElementComponent=function(c){jsPlumb.jsPlumbUIComponent.apply(this,arguments);this.mousemove=this.dblclick=this.click=this.mousedown=this.mouseup=function(d){}};jsPlumb.Connectors.Straight=function(){this.type="Straight";var r=this,i=null,e,k,p,n,l,f,q,h,g,d,c,o,m;this.compute=function(A,J,s,z,F,t,D,v){var I=Math.abs(A[0]-J[0]),C=Math.abs(A[1]-J[1]),B=0.45*I,u=0.45*C;I*=1.9;C*=1.9;var G=Math.min(A[0],J[0])-B;var E=Math.min(A[1],J[1])-u;var H=Math.max(2*D,v);if(I<H){I=H;G=A[0]+((J[0]-A[0])/2)-(H/2);B=(I-Math.abs(A[0]-J[0]))/2}if(C<H){C=H;E=A[1]+((J[1]-A[1])/2)-(H/2);u=(C-Math.abs(A[1]-J[1]))/2}h=A[0]<J[0]?B:I-B;g=A[1]<J[1]?u:C-u;d=A[0]<J[0]?I-B:B;c=A[1]<J[1]?C-u:u;i=[G,E,I,C,h,g,d,c];n=d-h,l=c-g;e=jsPlumbUtil.gradient({x:h,y:g},{x:d,y:c}),k=-1/e;p=-1*((e*h)-g);f=Math.atan(e);q=Math.atan(k);m=Math.sqrt((n*n)+(l*l));return i};this.pointOnPath=function(t,u){if(t==0&&!u){return{x:h,y:g}}else{if(t==1&&!u){return{x:d,y:c}}else{var s=u?t>0?t:m+t:t*m;return jsPlumbUtil.pointOnLine({x:h,y:g},{x:d,y:c},s)}}};this.gradientAtPoint=function(s){return e};this.pointAlongPathFrom=function(s,w,v){var u=r.pointOnPath(s,v),t=s==1?{x:h+((d-h)*10),y:g+((g-c)*10)}:{x:d,y:c};return jsPlumbUtil.pointOnLine(u,t,w)}};jsPlumb.Connectors.Bezier=function(v){var p=this;v=v||{};this.majorAnchor=v.curviness||150;this.minorAnchor=10;var t=null;this.type="Bezier";this._findControlPoint=function(H,w,C,x,A,F,y){var E=F.getOrientation(x),G=y.getOrientation(A),B=E[0]!=G[0]||E[1]==G[1],z=[],I=p.majorAnchor,D=p.minorAnchor;if(!B){if(E[0]==0){z.push(w[0]<C[0]?H[0]+D:H[0]-D)}else{z.push(H[0]-(I*E[0]))}if(E[1]==0){z.push(w[1]<C[1]?H[1]+D:H[1]-D)}else{z.push(H[1]+(I*G[1]))}}else{if(G[0]==0){z.push(C[0]<w[0]?H[0]+D:H[0]-D)}else{z.push(H[0]+(I*G[0]))}if(G[1]==0){z.push(C[1]<w[1]?H[1]+D:H[1]-D)}else{z.push(H[1]+(I*E[1]))}}return z};var q,l,f,o,n,f,e,s,r,u,d,h,g,k,i;this.compute=function(S,z,M,A,Q,x,w,L){w=w||0;u=Math.abs(S[0]-z[0])+w;d=Math.abs(S[1]-z[1])+w;s=Math.min(S[0],z[0])-(w/2);r=Math.min(S[1],z[1])-(w/2);f=S[0]<z[0]?u-(w/2):(w/2);e=S[1]<z[1]?d-(w/2):(w/2);o=S[0]<z[0]?(w/2):u-(w/2);n=S[1]<z[1]?(w/2):d-(w/2);q=p._findControlPoint([f,e],S,z,M,A,Q,x);l=p._findControlPoint([o,n],z,S,A,M,x,Q);var K=Math.min(f,o),J=Math.min(q[0],l[0]),F=Math.min(K,J),R=Math.max(f,o),O=Math.max(q[0],l[0]),C=Math.max(R,O);if(C>u){u=C}if(F<0){s+=F;var H=Math.abs(F);u+=H;q[0]+=H;f+=H;o+=H;l[0]+=H}var P=Math.min(e,n),N=Math.min(q[1],l[1]),B=Math.min(P,N),G=Math.max(e,n),E=Math.max(q[1],l[1]),y=Math.max(G,E);if(y>d){d=y}if(B<0){r+=B;var D=Math.abs(B);d+=D;q[1]+=D;e+=D;n+=D;l[1]+=D}if(L&&u<L){var I=(L-u)/2;u=L;s-=I;f=f+I;o=o+I;q[0]=q[0]+I;l[0]=l[0]+I}if(L&&d<L){var I=(L-d)/2;d=L;r-=I;e=e+I;n=n+I;q[1]=q[1]+I;l[1]=l[1]+I}t=[s,r,u,d,f,e,o,n,q[0],q[1],l[0],l[1]];return t};var c=function(){return[{x:f,y:e},{x:q[0],y:q[1]},{x:l[0],y:l[1]},{x:o,y:n}]};var m=function(x,w,y){if(y){w=jsBezier.locationAlongCurveFrom(x,w>0?0:1,w)}return w};this.pointOnPath=function(w,y){var x=c();w=m(x,w,y);return jsBezier.pointOnCurve(x,w)};this.gradientAtPoint=function(w,y){var x=c();w=m(x,w,y);return jsBezier.gradientAtPoint(x,w)};this.pointAlongPathFrom=function(w,z,y){var x=c();w=m(x,w,y);return jsBezier.pointAlongCurveFrom(x,w,z)}};jsPlumb.Connectors.Flowchart=function(v){this.type="Flowchart";v=v||{};var n=this,c=v.stub||v.minStubLength||30,f=jsPlumbUtil.isArray(c)?c[0]:c,k=jsPlumbUtil.isArray(c)?c[1]:c,p=v.gap||0,q=[],i=0,d=[],m=[],r=[],o,l,u=-Infinity,s=-Infinity,w=Infinity,t=Infinity,x=function(z,y,D,C){var B=0;for(var A=0;A<q.length;A++){m[A]=q[A][5]/i;d[A]=[B,(B+=(q[A][5]/i))]}},h=function(){r.push(q.length);for(var y=0;y<q.length;y++){r.push(q[y][0]);r.push(q[y][1])}},g=function(I,F,H,G,E,D){var A=q.length==0?H:q[q.length-1][0],z=q.length==0?G:q[q.length-1][1],B=I==A?Infinity:0,C=Math.abs(I==A?F-z:I-A);q.push([I,F,A,z,B,C]);i+=C;u=Math.max(u,I);s=Math.max(s,F);w=Math.min(w,I);t=Math.min(t,F)},e=function(A,C){if(C){A=A>0?A/i:(i+A)/i}var y=d.length-1,z=1;for(var B=0;B<d.length;B++){if(d[B][1]>=A){y=B;z=(A-d[B][0])/m[B];break}}return{segment:q[y],proportion:z,index:y}};this.compute=function(W,ak,z,Q,av,K,U,P,ap,am){q=[];d=[];i=0;m=[];u=s=-Infinity;w=t=Infinity;o=ak[0]<W[0];l=ak[1]<W[1];var aa=U||1,E=(aa/2)+(f+k),B=(aa/2)+(k+f),D=(aa/2)+(f+k),A=(aa/2)+(k+f),N=av.orientation||av.getOrientation(z),aw=K.orientation||K.getOrientation(Q),aj=o?ak[0]:W[0],ai=l?ak[1]:W[1],al=Math.abs(ak[0]-W[0])+E+B,au=Math.abs(ak[1]-W[1])+D+A;if(N[0]==0&&N[1]==0||aw[0]==0&&aw[1]==0){var ac=al>au?0:1,ae=[1,0][ac];N=[];aw=[];N[ac]=W[ac]>ak[ac]?-1:1;aw[ac]=W[ac]>ak[ac]?1:-1;N[ae]=0;aw[ae]=0}if(al<P){E+=(P-al)/2;al=P}if(au<P){D+=(P-au)/2;au=P}var I=o?(al-B)+(p*N[0]):E+(p*N[0]),H=l?(au-A)+(p*N[1]):D+(p*N[1]),ar=o?E+(p*aw[0]):(al-B)+(p*aw[0]),aq=l?D+(p*aw[1]):(au-A)+(p*aw[1]),Z=I+(N[0]*f),Y=H+(N[1]*f),L=ar+(aw[0]*k),J=aq+(aw[1]*k),V=Math.abs(I-ar)>(f+k),X=Math.abs(H-aq)>(f+k),ah=Z+((L-Z)/2),af=Y+((J-Y)/2),O=((N[0]*aw[0])+(N[1]*aw[1])),ab=O==-1,ad=O==0,C=O==1;aj-=E;ai-=D;r=[aj,ai,al,au,I,H,ar,aq];var ao=[];var S=N[0]==0?"y":"x",M=ab?"opposite":C?"orthogonal":"perpendicular",F=jsPlumbUtil.segment([I,H],[ar,aq]),ag=N[S=="x"?0:1]==-1,R={x:[null,4,3,2,1],y:[null,2,1,4,3]};if(ag){F=R[S][F]}g(Z,Y,I,H,ar,aq);var T=function(az,ay,y,ax){return az+(ay*((1-y)*ax)+Math.max(f,k))},G={oppositex:function(){if(z.elementId==Q.elementId){var y=Y+((1-av.y)*ap.height)+Math.max(f,k);return[[Z,y],[L,y]]}else{if(V&&(F==1||F==2)){return[[ah,H],[ah,aq]]}else{return[[Z,af],[L,af]]}}},orthogonalx:function(){if(F==1||F==2){return[[L,Y]]}else{return[[Z,J]]}},perpendicularx:function(){var y=(aq+H)/2;if((F==1&&aw[1]==1)||(F==2&&aw[1]==-1)){if(Math.abs(ar-I)>Math.max(f,k)){return[[L,Y]]}else{return[[Z,Y],[Z,y],[L,y]]}}else{if((F==3&&aw[1]==-1)||(F==4&&aw[1]==1)){return[[Z,y],[L,y]]}else{if((F==3&&aw[1]==1)||(F==4&&aw[1]==-1)){return[[Z,J]]}else{if((F==1&&aw[1]==-1)||(F==2&&aw[1]==1)){if(Math.abs(ar-I)>Math.max(f,k)){return[[ah,Y],[ah,J]]}else{return[[Z,J]]}}}}}},oppositey:function(){if(z.elementId==Q.elementId){var y=Z+((1-av.x)*ap.width)+Math.max(f,k);return[[y,Y],[y,J]]}else{if(X&&(F==2||F==3)){return[[I,af],[ar,af]]}else{return[[ah,Y],[ah,J]]}}},orthogonaly:function(){if(F==2||F==3){return[[Z,J]]}else{return[[L,Y]]}},perpendiculary:function(){var y=(ar+I)/2;if((F==2&&aw[0]==-1)||(F==3&&aw[0]==1)){if(Math.abs(ar-I)>Math.max(f,k)){return[[Z,J]]}else{return[[Z,af],[L,af]]}}else{if((F==1&&aw[0]==-1)||(F==4&&aw[0]==1)){var y=(ar+I)/2;return[[y,Y],[y,J]]}else{if((F==1&&aw[0]==1)||(F==4&&aw[0]==-1)){return[[L,Y]]}else{if((F==2&&aw[0]==1)||(F==3&&aw[0]==-1)){if(Math.abs(aq-H)>Math.max(f,k)){return[[Z,af],[L,af]]}else{return[[L,Y]]}}}}}}};var an=G[M+S]();if(an){for(var at=0;at<an.length;at++){g(an[at][0],an[at][1],I,H,ar,aq)}}g(L,J,I,H,ar,aq);g(ar,aq,I,H,ar,aq);h();x(I,H,ar,aq);if(s>r[3]){r[3]=s+(U*2)}if(u>r[2]){r[2]=u+(U*2)}return r};this.pointOnPath=function(y,z){return n.pointAlongPathFrom(y,0,z)};this.gradientAtPoint=function(y,z){return q[e(y,z)["index"]][4]};this.pointAlongPathFrom=function(F,y,E){var G=e(F,E),C=G.segment,z=G.proportion,B=q[G.index][5],A=q[G.index][4];var D={x:A==Infinity?C[2]:C[2]>C[0]?C[0]+((1-z)*B)-y:C[2]+(z*B)+y,y:A==0?C[3]:C[3]>C[1]?C[1]+((1-z)*B)-y:C[3]+(z*B)+y,segmentInfo:G};return D}};jsPlumb.Endpoints.Dot=function(d){this.type="Dot";var c=this;d=d||{};this.radius=d.radius||10;this.defaultOffset=0.5*this.radius;this.defaultInnerRadius=this.radius/3;this.compute=function(i,f,l,h){var g=l.radius||c.radius,e=i[0]-g,k=i[1]-g;return[e,k,g*2,g*2,g]}};jsPlumb.Endpoints.Rectangle=function(d){this.type="Rectangle";var c=this;d=d||{};this.width=d.width||20;this.height=d.height||20;this.compute=function(k,g,m,i){var h=m.width||c.width,f=m.height||c.height,e=k[0]-(h/2),l=k[1]-(f/2);return[e,l,h,f]}};var a=function(e){jsPlumb.DOMElementComponent.apply(this,arguments);var c=this;var d=[];this.getDisplayElements=function(){return d};this.appendDisplayElement=function(f){d.push(f)}};jsPlumb.Endpoints.Image=function(g){this.type="Image";a.apply(this,arguments);var l=this,f=false,e=g.width,d=g.height,i=null,c=g.endpoint;this.img=new Image();l.ready=false;this.img.onload=function(){l.ready=true;e=e||l.img.width;d=d||l.img.height;if(i){i(l)}};c.setImage=function(m,o){var n=m.constructor==String?m:m.src;i=o;l.img.src=m;if(l.canvas!=null){l.canvas.setAttribute("src",m)}};c.setImage(g.src||g.url,g.onload);this.compute=function(o,m,p,n){l.anchorPoint=o;if(l.ready){return[o[0]-e/2,o[1]-d/2,e,d]}else{return[0,0,0,0]}};l.canvas=document.createElement("img"),f=false;l.canvas.style.margin=0;l.canvas.style.padding=0;l.canvas.style.outline=0;l.canvas.style.position="absolute";var h=g.cssClass?" "+g.cssClass:"";l.canvas.className=jsPlumb.endpointClass+h;if(e){l.canvas.setAttribute("width",e)}if(d){l.canvas.setAttribute("height",d)}jsPlumb.appendElement(l.canvas,g.parent);l.attachListeners(l.canvas,l);var k=function(p,o,n){if(!f){l.canvas.setAttribute("src",l.img.src);l.appendDisplayElement(l.canvas);f=true}var m=l.anchorPoint[0]-(e/2),q=l.anchorPoint[1]-(d/2);jsPlumb.sizeCanvas(l.canvas,m,q,e,d)};this.paint=function(o,n,m){if(l.ready){k(o,n,m)}else{window.setTimeout(function(){l.paint(o,n,m)},200)}}};jsPlumb.Endpoints.Blank=function(d){var c=this;this.type="Blank";a.apply(this,arguments);this.compute=function(g,e,h,f){return[g[0],g[1],10,0]};c.canvas=document.createElement("div");c.canvas.style.display="block";c.canvas.style.width="1px";c.canvas.style.height="1px";c.canvas.style.background="transparent";c.canvas.style.position="absolute";c.canvas.className=c._jsPlumb.endpointClass;jsPlumb.appendElement(c.canvas,d.parent);this.paint=function(g,f,e){jsPlumb.sizeCanvas(c.canvas,g[0],g[1],g[2],g[3])}};jsPlumb.Endpoints.Triangle=function(c){this.type="Triangle";c=c||{};c.width=c.width||55;c.height=c.height||55;this.width=c.width;this.height=c.height;this.compute=function(i,f,l,h){var g=l.width||self.width,e=l.height||self.height,d=i[0]-(g/2),k=i[1]-(e/2);return[d,k,g,e]}};var b=function(e){var d=true,c=this;this.isAppendedAtTopLevel=true;this.component=e.component;this.loc=e.location==null?0.5:e.location;this.endpointLoc=e.endpointLocation==null?[0.5,0.5]:e.endpointLocation;this.setVisible=function(f){d=f;c.component.repaint()};this.isVisible=function(){return d};this.hide=function(){c.setVisible(false)};this.show=function(){c.setVisible(true)};this.incrementLocation=function(f){c.loc+=f;c.component.repaint()};this.setLocation=function(f){c.loc=f;c.component.repaint()};this.getLocation=function(){return c.loc}};jsPlumb.Overlays.Arrow=function(g){this.type="Arrow";b.apply(this,arguments);this.isAppendedAtTopLevel=false;g=g||{};var d=this;this.length=g.length||20;this.width=g.width||20;this.id=g.id;var f=(g.direction||1)<0?-1:1,e=g.paintStyle||{lineWidth:1},c=g.foldback||0.623;this.computeMaxSize=function(){return d.width*1.5};this.cleanup=function(){};this.draw=function(k,z,u){var o,v,h,p,n;if(k.pointAlongPathFrom){if(jsPlumbUtil.isString(d.loc)||d.loc>1||d.loc<0){var i=parseInt(d.loc);o=k.pointAlongPathFrom(i,f*d.length/2,true),v=k.pointOnPath(i,true),h=jsPlumbUtil.pointOnLine(o,v,d.length)}else{if(d.loc==1){o=k.pointOnPath(d.loc);v=k.pointAlongPathFrom(d.loc,-1);h=jsPlumbUtil.pointOnLine(o,v,d.length)}else{if(d.loc==0){h=k.pointOnPath(d.loc);v=k.pointAlongPathFrom(d.loc,1);o=jsPlumbUtil.pointOnLine(h,v,d.length)}else{o=k.pointAlongPathFrom(d.loc,f*d.length/2),v=k.pointOnPath(d.loc),h=jsPlumbUtil.pointOnLine(o,v,d.length)}}}p=jsPlumbUtil.perpendicularLineTo(o,h,d.width);n=jsPlumbUtil.pointOnLine(o,h,c*d.length);var y=Math.min(o.x,p[0].x,p[1].x),s=Math.max(o.x,p[0].x,p[1].x),x=Math.min(o.y,p[0].y,p[1].y),r=Math.max(o.y,p[0].y,p[1].y);var q={hxy:o,tail:p,cxy:n},t=e.strokeStyle||z.strokeStyle,w=e.fillStyle||z.strokeStyle,m=e.lineWidth||z.lineWidth;d.paint(k,q,m,t,w,u);return[y,s,x,r]}else{return[0,0,0,0]}}};jsPlumb.Overlays.PlainArrow=function(d){d=d||{};var c=jsPlumb.extend(d,{foldback:1});jsPlumb.Overlays.Arrow.call(this,c);this.type="PlainArrow"};jsPlumb.Overlays.Diamond=function(e){e=e||{};var c=e.length||40,d=jsPlumb.extend(e,{length:c/2,foldback:2});jsPlumb.Overlays.Arrow.call(this,d);this.type="Diamond"};jsPlumb.Overlays.Label=function(i){this.type="Label";jsPlumb.DOMElementComponent.apply(this,arguments);b.apply(this,arguments);this.labelStyle=i.labelStyle||jsPlumb.Defaults.LabelStyle;this.id=i.id;this.cachedDimensions=null;var e=i.label||"",c=this,f=false,k=document.createElement("div"),g=null;k.style.position="absolute";var d=i._jsPlumb.overlayClass+" "+(c.labelStyle.cssClass?c.labelStyle.cssClass:i.cssClass?i.cssClass:"");k.className=d;jsPlumb.appendElement(k,i.component.parent);jsPlumb.getId(k);c.attachListeners(k,c);c.canvas=k;var h=c.setVisible;c.setVisible=function(l){h(l);k.style.display=l?"block":"none"};this.getElement=function(){return k};this.cleanup=function(){if(k!=null){jsPlumb.CurrentLibrary.removeElement(k)}};this.setLabel=function(m){e=m;g=null;c.component.repaint()};this.getLabel=function(){return e};this.paint=function(l,n,m){if(!f){l.appendDisplayElement(k);c.attachListeners(k,l);f=true}k.style.left=(m[0]+n.minx)+"px";k.style.top=(m[1]+n.miny)+"px"};this.getTextDimensions=function(){if(typeof e=="function"){var l=e(c);k.innerHTML=l.replace(/\r\n/g,"<br/>")}else{if(g==null){g=e;k.innerHTML=g.replace(/\r\n/g,"<br/>")}}var n=jsPlumb.CurrentLibrary.getElementObject(k),m=jsPlumb.CurrentLibrary.getSize(n);return{width:m[0],height:m[1]}};this.computeMaxSize=function(l){var m=c.getTextDimensions(l);return m.width?Math.max(m.width,m.height)*1.5:0};this.draw=function(m,n,o){var s=c.getTextDimensions(m);if(s.width!=null){var p={x:0,y:0};if(m.pointOnPath){var q=c.loc,r=false;if(jsPlumbUtil.isString(c.loc)||c.loc<0||c.loc>1){q=parseInt(c.loc);r=true}p=m.pointOnPath(q,r)}else{var l=c.loc.constructor==Array?c.loc:c.endpointLoc;p={x:l[0]*o[2],y:l[1]*o[3]}}minx=p.x-(s.width/2),miny=p.y-(s.height/2);c.paint(m,{minx:minx,miny:miny,td:s,cxy:p},o);return[minx,minx+s.width,miny,miny+s.height]}else{return[0,0,0,0]}};this.reattachListeners=function(l){if(k){c.reattachListenersForElement(k,c,l)}}};jsPlumb.Overlays.GuideLines=function(){var c=this;c.length=50;c.lineWidth=5;this.type="GuideLines";b.apply(this,arguments);jsPlumb.jsPlumbUIComponent.apply(this,arguments);this.draw=function(e,l,k){var i=e.pointAlongPathFrom(c.loc,c.length/2),h=e.pointOnPath(c.loc),g=jsPlumbUtil.pointOnLine(i,h,c.length),f=jsPlumbUtil.perpendicularLineTo(i,g,40),d=jsPlumbUtil.perpendicularLineTo(g,i,20);c.paint(e,[i,g,f,d],c.lineWidth,"red",null,k);return[Math.min(i.x,g.x),Math.min(i.y,g.y),Math.max(i.x,g.x),Math.max(i.y,g.y)]};this.computeMaxSize=function(){return 50};this.cleanup=function(){}}})();(function(){var c=function(e,g,d,f){this.m=(f-g)/(d-e);this.b=-1*((this.m*e)-g);this.rectIntersect=function(q,p,s,o){var n=[];var k=(p-this.b)/this.m;if(k>=q&&k<=(q+s)){n.push([k,(this.m*k)+this.b])}var t=(this.m*(q+s))+this.b;if(t>=p&&t<=(p+o)){n.push([(t-this.b)/this.m,t])}var k=((p+o)-this.b)/this.m;if(k>=q&&k<=(q+s)){n.push([k,(this.m*k)+this.b])}var t=(this.m*q)+this.b;if(t>=p&&t<=(p+o)){n.push([(t-this.b)/this.m,t])}if(n.length==2){var m=(n[0][0]+n[1][0])/2,l=(n[0][1]+n[1][1])/2;n.push([m,l]);var i=m<=q+(s/2)?-1:1,r=l<=p+(o/2)?-1:1;n.push([i,r]);return n}return null}},a=function(e,g,d,f){if(e<=d&&f<=g){return 1}else{if(e<=d&&g<=f){return 2}else{if(d<=e&&f>=g){return 3}}}return 4},b=function(g,f,i,e,h,m,l,d,k){if(d<=k){return[g,f]}if(i==1){if(e[3]<=0&&h[3]>=1){return[g+(e[2]<0.5?-1*m:m),f]}else{if(e[2]>=1&&h[2]<=0){return[g,f+(e[3]<0.5?-1*l:l)]}else{return[g+(-1*m),f+(-1*l)]}}}else{if(i==2){if(e[3]>=1&&h[3]<=0){return[g+(e[2]<0.5?-1*m:m),f]}else{if(e[2]>=1&&h[2]<=0){return[g,f+(e[3]<0.5?-1*l:l)]}else{return[g+(1*m),f+(-1*l)]}}}else{if(i==3){if(e[3]>=1&&h[3]<=0){return[g+(e[2]<0.5?-1*m:m),f]}else{if(e[2]<=0&&h[2]>=1){return[g,f+(e[3]<0.5?-1*l:l)]}else{return[g+(-1*m),f+(-1*l)]}}}else{if(i==4){if(e[3]<=0&&h[3]>=1){return[g+(e[2]<0.5?-1*m:m),f]}else{if(e[2]<=0&&h[2]>=1){return[g,f+(e[3]<0.5?-1*l:l)]}else{return[g+(1*m),f+(-1*l)]}}}}}}};jsPlumb.Connectors.StateMachine=function(l){var u=this,n=null,o,m,g,e,p=[],d=l.curviness||10,k=l.margin||5,q=l.proximityLimit||80,f=l.orientation&&l.orientation=="clockwise",i=l.loopbackRadius||25,h=false,t=l.showLoopback!==false;this.type="StateMachine";l=l||{};this.compute=function(ad,H,W,I,ac,z,v,U){var Q=Math.abs(ad[0]-H[0]),Y=Math.abs(ad[1]-H[1]),S=0.45*Q,ab=0.45*Y;Q*=1.9;Y*=1.9;v=v||1;var O=Math.min(ad[0],H[0])-S,M=Math.min(ad[1],H[1])-ab;if(!t||(W.elementId!=I.elementId)){h=false;o=ad[0]<H[0]?S:Q-S;m=ad[1]<H[1]?ab:Y-ab;g=ad[0]<H[0]?Q-S:S;e=ad[1]<H[1]?Y-ab:ab;if(ad[2]==0){o-=k}if(ad[2]==1){o+=k}if(ad[3]==0){m-=k}if(ad[3]==1){m+=k}if(H[2]==0){g-=k}if(H[2]==1){g+=k}if(H[3]==0){e-=k}if(H[3]==1){e+=k}var N=(o+g)/2,L=(m+e)/2,A=(-1*N)/L,V=Math.atan(A),P=(A==Infinity||A==-Infinity)?0:Math.abs(d/2*Math.sin(V)),R=(A==Infinity||A==-Infinity)?0:Math.abs(d/2*Math.cos(V)),B=a(o,m,g,e),J=Math.sqrt(Math.pow(g-o,2)+Math.pow(e-m,2));p=b(N,L,B,ad,H,d,d,J,q);var G=Math.max(Math.abs(p[0]-o)*3,Math.abs(p[0]-g)*3,Math.abs(g-o),2*v,U),K=Math.max(Math.abs(p[1]-m)*3,Math.abs(p[1]-e)*3,Math.abs(e-m),2*v,U);if(Q<G){var T=G-Q;O-=(T/2);o+=(T/2);g+=(T/2);Q=G;p[0]+=(T/2)}if(Y<K){var aa=K-Y;M-=(aa/2);m+=(aa/2);e+=(aa/2);Y=K;p[1]+=(aa/2)}n=[O,M,Q,Y,o,m,g,e,p[0],p[1]]}else{h=true;var Z=ad[0],X=ad[0],F=ad[1]-k,D=ad[1]-k,E=Z,C=F-i;Q=((2*v)+(4*i)),Y=((2*v)+(4*i));O=E-i-v-i,M=C-i-v-i;n=[O,M,Q,Y,E-O,C-M,i,f,Z-O,F-M,X-O,D-M]}return n};var r=function(){return[{x:g,y:e},{x:p[0],y:p[1]},{x:p[0]+1,y:p[1]+1},{x:o,y:m}]};var s=function(w,v,x){if(x){v=jsBezier.locationAlongCurveFrom(w,v>0?0:1,v)}return v};this.pointOnPath=function(x,B){if(h){if(B){var y=Math.PI*2*i;x=x/y}if(x>0&&x<1){x=1-x}var z=(x*2*Math.PI)+(Math.PI/2),w=n[4]+(n[6]*Math.cos(z)),v=n[5]+(n[6]*Math.sin(z));return{x:w,y:v}}else{var A=r();x=s(A,x,B);return jsBezier.pointOnCurve(A,x)}};this.gradientAtPoint=function(v,y){if(h){if(y){var w=Math.PI*2*i;v=v/w}return Math.atan(v*2*Math.PI)}else{var x=r();v=s(x,v,y);return jsBezier.gradientAtPoint(x,v)}};this.pointAlongPathFrom=function(D,v,C){if(h){if(C){var B=Math.PI*2*i;D=D/B}if(D>0&&D<1){D=1-D}var B=2*Math.PI*n[6],w=v/B*2*Math.PI,z=(D*2*Math.PI)-w+(Math.PI/2),y=n[4]+(n[6]*Math.cos(z)),x=n[5]+(n[6]*Math.sin(z));return{x:y,y:x}}else{var A=r();D=s(A,D,C);return jsBezier.pointAlongCurveFrom(A,D,v)}}};jsPlumb.Connectors.canvas.StateMachine=function(f){f=f||{};var d=this,g=f.drawGuideline||true,e=f.avoidSelector;jsPlumb.Connectors.StateMachine.apply(this,arguments);jsPlumb.CanvasConnector.apply(this,arguments);this._paint=function(l){if(l.length==10){d.ctx.beginPath();d.ctx.moveTo(l[4],l[5]);d.ctx.bezierCurveTo(l[8],l[9],l[8],l[9],l[6],l[7]);d.ctx.stroke()}else{d.ctx.save();d.ctx.beginPath();var k=0,i=2*Math.PI,h=l[7];d.ctx.arc(l[4],l[5],l[6],0,i,h);d.ctx.stroke();d.ctx.closePath();d.ctx.restore()}};this.createGradient=function(i,h){return h.createLinearGradient(i[4],i[5],i[6],i[7])}};jsPlumb.Connectors.svg.StateMachine=function(){var d=this;jsPlumb.Connectors.StateMachine.apply(this,arguments);jsPlumb.SvgConnector.apply(this,arguments);this.getPath=function(e){if(e.length==10){return"M "+e[4]+" "+e[5]+" C "+e[8]+" "+e[9]+" "+e[8]+" "+e[9]+" "+e[6]+" "+e[7]}else{return"M"+(e[8]+4)+" "+e[9]+" A "+e[6]+" "+e[6]+" 0 1,0 "+(e[8]-4)+" "+e[9]}}};jsPlumb.Connectors.vml.StateMachine=function(){jsPlumb.Connectors.StateMachine.apply(this,arguments);jsPlumb.VmlConnector.apply(this,arguments);var d=jsPlumb.vml.convertValue;this.getPath=function(k){if(k.length==10){return"m"+d(k[4])+","+d(k[5])+" c"+d(k[8])+","+d(k[9])+","+d(k[8])+","+d(k[9])+","+d(k[6])+","+d(k[7])+" e"}else{var h=d(k[8]-k[6]),g=d(k[9]-(2*k[6])),f=h+d(2*k[6]),e=g+d(2*k[6]),l=h+","+g+","+f+","+e;var i="ar "+l+","+d(k[8])+","+d(k[9])+","+d(k[8])+","+d(k[9])+" e";return i}}}})();(function(){var h={"stroke-linejoin":"joinstyle",joinstyle:"joinstyle",endcap:"endcap",miterlimit:"miterlimit"},c=null;if(document.createStyleSheet&&document.namespaces){var m=[".jsplumb_vml","jsplumb\\:textbox","jsplumb\\:oval","jsplumb\\:rect","jsplumb\\:stroke","jsplumb\\:shape","jsplumb\\:group"],g="behavior:url(#default#VML);position:absolute;";c=document.createStyleSheet();for(var r=0;r<m.length;r++){c.addRule(m[r],g)}document.namespaces.add("jsplumb","urn:schemas-microsoft-com:vml")}jsPlumb.vml={};var t=1000,s={},a=function(u,i){var w=jsPlumb.getId(u),v=s[w];if(!v){v=f("group",[0,0,t,t],{"class":i});v.style.backgroundColor="red";s[w]=v;jsPlumb.appendElement(v,u)}return v},e=function(v,w){for(var u in w){v[u]=w[u]}},f=function(u,x,y,v,i){y=y||{};var w=document.createElement("jsplumb:"+u);i.appendElement(w,v);w.className=(y["class"]?y["class"]+" ":"")+"jsplumb_vml";k(w,x);e(w,y);return w},k=function(u,i){u.style.left=i[0]+"px";u.style.top=i[1]+"px";u.style.width=i[2]+"px";u.style.height=i[3]+"px";u.style.position="absolute"},p=jsPlumb.vml.convertValue=function(i){return Math.floor(i*t)},b=function(w,u,v,i){if("transparent"===u){i.setOpacity(v,"0.0")}else{i.setOpacity(v,"1.0")}},q=function(y,u,B,C){var x={};if(u.strokeStyle){x.stroked="true";var D=jsPlumbUtil.convertStyle(u.strokeStyle,true);x.strokecolor=D;b(x,D,"stroke",B);x.strokeweight=u.lineWidth+"px"}else{x.stroked="false"}if(u.fillStyle){x.filled="true";var v=jsPlumbUtil.convertStyle(u.fillStyle,true);x.fillcolor=v;b(x,v,"fill",B)}else{x.filled="false"}if(u.dashstyle){if(B.strokeNode==null){B.strokeNode=f("stroke",[0,0,0,0],{dashstyle:u.dashstyle},y,C)}else{B.strokeNode.dashstyle=u.dashstyle}}else{if(u["stroke-dasharray"]&&u.lineWidth){var E=u["stroke-dasharray"].indexOf(",")==-1?" ":",",z=u["stroke-dasharray"].split(E),w="";for(var A=0;A<z.length;A++){w+=(Math.floor(z[A]/u.lineWidth)+E)}if(B.strokeNode==null){B.strokeNode=f("stroke",[0,0,0,0],{dashstyle:w},y,C)}else{B.strokeNode.dashstyle=w}}}e(y,x)},n=function(){var i=this;jsPlumb.jsPlumbUIComponent.apply(this,arguments);this.opacityNodes={stroke:null,fill:null};this.initOpacityNodes=function(v){i.opacityNodes.stroke=f("stroke",[0,0,1,1],{opacity:"0.0"},v,i._jsPlumb);i.opacityNodes.fill=f("fill",[0,0,1,1],{opacity:"0.0"},v,i._jsPlumb)};this.setOpacity=function(v,x){var w=i.opacityNodes[v];if(w){w.opacity=""+x}};var u=[];this.getDisplayElements=function(){return u};this.appendDisplayElement=function(w,v){if(!v){i.canvas.parentNode.appendChild(w)}u.push(w)}},d=jsPlumb.VmlConnector=function(v){var i=this;i.strokeNode=null;i.canvas=null;n.apply(this,arguments);var u=i._jsPlumb.connectorClass+(v.cssClass?(" "+v.cssClass):"");this.paint=function(A,x,z){if(x!=null){var E=i.getPath(A),y={path:E};if(x.outlineColor){var C=x.outlineWidth||1,D=x.lineWidth+(2*C),B={strokeStyle:jsPlumbUtil.convertStyle(x.outlineColor),lineWidth:D};for(var w in h){B[w]=x[w]}if(i.bgCanvas==null){y["class"]=u;y.coordsize=(A[2]*t)+","+(A[3]*t);i.bgCanvas=f("shape",A,y,v.parent,i._jsPlumb);k(i.bgCanvas,A);i.appendDisplayElement(i.bgCanvas,true);i.attachListeners(i.bgCanvas,i);i.initOpacityNodes(i.bgCanvas,["stroke"])}else{y.coordsize=(A[2]*t)+","+(A[3]*t);k(i.bgCanvas,A);e(i.bgCanvas,y)}q(i.bgCanvas,B,i)}if(i.canvas==null){y["class"]=u;y.coordsize=(A[2]*t)+","+(A[3]*t);if(i.tooltip){y.label=i.tooltip}i.canvas=f("shape",A,y,v.parent,i._jsPlumb);i.appendDisplayElement(i.canvas,true);i.attachListeners(i.canvas,i);i.initOpacityNodes(i.canvas,["stroke"])}else{y.coordsize=(A[2]*t)+","+(A[3]*t);k(i.canvas,A);e(i.canvas,y)}q(i.canvas,x,i,i._jsPlumb)}};this.reattachListeners=function(){if(i.canvas){i.reattachListenersForElement(i.canvas,i)}}},l=window.VmlEndpoint=function(y){n.apply(this,arguments);var i=null,v=this,u=null,x=null;v.canvas=document.createElement("div");v.canvas.style.position="absolute";var w=v._jsPlumb.endpointClass+(y.cssClass?(" "+y.cssClass):"");y._jsPlumb.appendElement(v.canvas,y.parent);if(v.tooltip){v.canvas.setAttribute("label",v.tooltip)}this.paint=function(C,A,z){var B={};jsPlumb.sizeCanvas(v.canvas,C[0],C[1],C[2],C[3]);if(i==null){B["class"]=w;i=v.getVml([0,0,C[2],C[3]],B,z,v.canvas,v._jsPlumb);v.attachListeners(i,v);v.appendDisplayElement(i,true);v.appendDisplayElement(v.canvas,true);v.initOpacityNodes(i,["fill"])}else{k(i,[0,0,C[2],C[3]]);e(i,B)}q(i,A,v)};this.reattachListeners=function(){if(i){v.reattachListenersForElement(i,v)}}};jsPlumb.Connectors.vml.Bezier=function(){jsPlumb.Connectors.Bezier.apply(this,arguments);d.apply(this,arguments);this.getPath=function(i){return"m"+p(i[4])+","+p(i[5])+" c"+p(i[8])+","+p(i[9])+","+p(i[10])+","+p(i[11])+","+p(i[6])+","+p(i[7])+" e"}};jsPlumb.Connectors.vml.Straight=function(){jsPlumb.Connectors.Straight.apply(this,arguments);d.apply(this,arguments);this.getPath=function(i){return"m"+p(i[4])+","+p(i[5])+" l"+p(i[6])+","+p(i[7])+" e"}};jsPlumb.Connectors.vml.Flowchart=function(){jsPlumb.Connectors.Flowchart.apply(this,arguments);d.apply(this,arguments);this.getPath=function(v){var w="m "+p(v[4])+","+p(v[5])+" l";for(var u=0;u<v[8];u++){w=w+" "+p(v[9+(u*2)])+","+p(v[10+(u*2)])}w=w+" "+p(v[6])+","+p(v[7])+" e";return w}};jsPlumb.Endpoints.vml.Dot=function(){jsPlumb.Endpoints.Dot.apply(this,arguments);l.apply(this,arguments);this.getVml=function(w,x,u,v,i){return f("oval",w,x,v,i)}};jsPlumb.Endpoints.vml.Rectangle=function(){jsPlumb.Endpoints.Rectangle.apply(this,arguments);l.apply(this,arguments);this.getVml=function(w,x,u,v,i){return f("rect",w,x,v,i)}};jsPlumb.Endpoints.vml.Image=jsPlumb.Endpoints.Image;jsPlumb.Endpoints.vml.Blank=jsPlumb.Endpoints.Blank;jsPlumb.Overlays.vml.Label=jsPlumb.Overlays.Label;var o=function(x,v){x.apply(this,v);n.apply(this,v);var u=this,w=null;u.canvas=null;u.isAppendedAtTopLevel=true;var i=function(z,y){return"m "+p(z.hxy.x)+","+p(z.hxy.y)+" l "+p(z.tail[0].x)+","+p(z.tail[0].y)+" "+p(z.cxy.x)+","+p(z.cxy.y)+" "+p(z.tail[1].x)+","+p(z.tail[1].y)+" x e"};this.paint=function(B,G,F,H,L,K){var z={};if(H){z.stroked="true";z.strokecolor=jsPlumbUtil.convertStyle(H,true)}if(F){z.strokeweight=F+"px"}if(L){z.filled="true";z.fillcolor=L}var y=Math.min(G.hxy.x,G.tail[0].x,G.tail[1].x,G.cxy.x),J=Math.min(G.hxy.y,G.tail[0].y,G.tail[1].y,G.cxy.y),C=Math.max(G.hxy.x,G.tail[0].x,G.tail[1].x,G.cxy.x),A=Math.max(G.hxy.y,G.tail[0].y,G.tail[1].y,G.cxy.y),I=Math.abs(C-y),E=Math.abs(A-J),D=[y,J,I,E];z.path=i(G,K);z.coordsize=(K[2]*t)+","+(K[3]*t);D[0]=K[0];D[1]=K[1];D[2]=K[2];D[3]=K[3];if(u.canvas==null){u.canvas=f("shape",D,z,B.canvas.parentNode,B._jsPlumb);B.appendDisplayElement(u.canvas,true);u.attachListeners(u.canvas,B);u.attachListeners(u.canvas,u)}else{k(u.canvas,D);e(u.canvas,z)}};this.reattachListeners=function(){if(u.canvas){u.reattachListenersForElement(u.canvas,u)}};this.cleanup=function(){if(u.canvas!=null){jsPlumb.CurrentLibrary.removeElement(u.canvas)}}};jsPlumb.Overlays.vml.Arrow=function(){o.apply(this,[jsPlumb.Overlays.Arrow,arguments])};jsPlumb.Overlays.vml.PlainArrow=function(){o.apply(this,[jsPlumb.Overlays.PlainArrow,arguments])};jsPlumb.Overlays.vml.Diamond=function(){o.apply(this,[jsPlumb.Overlays.Diamond,arguments])}})();(function(){var l={joinstyle:"stroke-linejoin","stroke-linejoin":"stroke-linejoin","stroke-dashoffset":"stroke-dashoffset","stroke-linecap":"stroke-linecap"},w="stroke-dasharray",A="dashstyle",e="linearGradient",b="radialGradient",c="fill",a="stop",z="stroke",q="stroke-width",h="style",m="none",t="jsplumb_gradient_",o="lineWidth",C={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml"},g=function(F,D){for(var E in D){F.setAttribute(E,""+D[E])}},f=function(E,D){var F=document.createElementNS(C.svg,E);D=D||{};D.version="1.1";D.xmlns=C.xhtml;g(F,D);return F},n=function(D){return"position:absolute;left:"+D[0]+"px;top:"+D[1]+"px"},i=function(E){for(var D=0;D<E.childNodes.length;D++){if(E.childNodes[D].tagName==e||E.childNodes[D].tagName==b){E.removeChild(E.childNodes[D])}}},v=function(N,I,F,D,J){var G=t+J._jsPlumb.idstamp();i(N);if(!F.gradient.offset){var L=f(e,{id:G,gradientUnits:"userSpaceOnUse"});N.appendChild(L)}else{var L=f(b,{id:G});N.appendChild(L)}for(var K=0;K<F.gradient.stops.length;K++){var H=K;if(D.length==8){H=D[4]<D[6]?K:F.gradient.stops.length-1-K}else{H=D[4]<D[6]?F.gradient.stops.length-1-K:K}var M=jsPlumbUtil.convertStyle(F.gradient.stops[H][1],true);var O=f(a,{offset:Math.floor(F.gradient.stops[K][0]*100)+"%","stop-color":M});L.appendChild(O)}var E=F.strokeStyle?z:c;I.setAttribute(h,E+":url(#"+G+")")},x=function(K,G,E,D,H){if(E.gradient){v(K,G,E,D,H)}else{i(K);G.setAttribute(h,"")}G.setAttribute(c,E.fillStyle?jsPlumbUtil.convertStyle(E.fillStyle,true):m);G.setAttribute(z,E.strokeStyle?jsPlumbUtil.convertStyle(E.strokeStyle,true):m);if(E.lineWidth){G.setAttribute(q,E.lineWidth)}if(E[A]&&E[o]&&!E[w]){var L=E[A].indexOf(",")==-1?" ":",",I=E[A].split(L),F="";I.forEach(function(M){F+=(Math.floor(M*E.lineWidth)+L)});G.setAttribute(w,F)}else{if(E[w]){G.setAttribute(w,E[w])}}for(var J in l){if(E[J]){G.setAttribute(l[J],E[J])}}},B=function(F){var D=/([0-9].)(p[xt])\s(.*)/;var E=F.match(D);return{size:E[1]+E[2],font:E[3]}},r=function(I,J,E){var K=E.split(" "),H=I.className,G=H.baseVal.split(" ");for(var F=0;F<K.length;F++){if(J){if(G.indexOf(K[F])==-1){G.push(K[F])}}else{var D=G.indexOf(K[F]);if(D!=-1){G.splice(D,1)}}}I.className.baseVal=G.join(" ")},u=function(E,D){r(E,true,D)},k=function(E,D){r(E,false,D)};jsPlumbUtil.svg={addClass:u,removeClass:k,node:f,attr:g,pos:n};var s=function(H){var D=this,G=H.pointerEventsSpec||"all";jsPlumb.jsPlumbUIComponent.apply(this,H.originalArgs);D.canvas=null,D.path=null,D.svg=null;var F=H.cssClass+" "+(H.originalArgs[0].cssClass||""),I={style:"",width:0,height:0,"pointer-events":G,position:"absolute"};if(D.tooltip){I.title=D.tooltip}D.svg=f("svg",I);if(H.useDivWrapper){D.canvas=document.createElement("div");D.canvas.style.position="absolute";jsPlumb.sizeCanvas(D.canvas,0,0,1,1);D.canvas.className=F;if(D.tooltip){D.canvas.setAttribute("title",D.tooltip)}}else{g(D.svg,{"class":F});D.canvas=D.svg}H._jsPlumb.appendElement(D.canvas,H.originalArgs[0]["parent"]);if(H.useDivWrapper){D.canvas.appendChild(D.svg)}var E=[D.canvas];this.getDisplayElements=function(){return E};this.appendDisplayElement=function(J){E.push(J)};this.paint=function(M,L,K){if(L!=null){var J=M[0],N=M[1];if(H.useDivWrapper){jsPlumb.sizeCanvas(D.canvas,M[0],M[1],M[2],M[3]);J=0,N=0}g(D.svg,{style:n([J,N,M[2],M[3]]),width:M[2],height:M[3]});D._paint.apply(this,arguments)}}};var d=jsPlumb.SvgConnector=function(E){var D=this;s.apply(this,[{cssClass:E._jsPlumb.connectorClass,originalArgs:arguments,pointerEventsSpec:"none",tooltip:E.tooltip,_jsPlumb:E._jsPlumb}]);this._paint=function(L,H){var K=D.getPath(L),F={d:K},J=null;F["pointer-events"]="all";if(H.outlineColor){var I=H.outlineWidth||1,G=H.lineWidth+(2*I),J=jsPlumb.CurrentLibrary.extend({},H);J.strokeStyle=jsPlumbUtil.convertStyle(H.outlineColor);J.lineWidth=G;if(D.bgPath==null){D.bgPath=f("path",F);D.svg.appendChild(D.bgPath);D.attachListeners(D.bgPath,D)}else{g(D.bgPath,F)}x(D.svg,D.bgPath,J,L,D)}if(D.path==null){D.path=f("path",F);D.svg.appendChild(D.path);D.attachListeners(D.path,D)}else{g(D.path,F)}x(D.svg,D.path,H,L,D)};this.reattachListeners=function(){if(D.bgPath){D.reattachListenersForElement(D.bgPath,D)}if(D.path){D.reattachListenersForElement(D.path,D)}}};jsPlumb.Connectors.svg.Bezier=function(D){jsPlumb.Connectors.Bezier.apply(this,arguments);d.apply(this,arguments);this.getPath=function(F){var E="M "+F[4]+" "+F[5];E+=(" C "+F[8]+" "+F[9]+" "+F[10]+" "+F[11]+" "+F[6]+" "+F[7]);return E}};jsPlumb.Connectors.svg.Straight=function(D){jsPlumb.Connectors.Straight.apply(this,arguments);d.apply(this,arguments);this.getPath=function(E){return"M "+E[4]+" "+E[5]+" L "+E[6]+" "+E[7]}};jsPlumb.Connectors.svg.Flowchart=function(){var D=this;jsPlumb.Connectors.Flowchart.apply(this,arguments);d.apply(this,arguments);this.getPath=function(F){var G="M "+F[4]+","+F[5];for(var E=0;E<F[8];E++){G=G+" L "+F[9+(E*2)]+" "+F[10+(E*2)]}G=G+" "+F[6]+","+F[7];return G}};var y=window.SvgEndpoint=function(E){var D=this;s.apply(this,[{cssClass:E._jsPlumb.endpointClass,originalArgs:arguments,pointerEventsSpec:"all",useDivWrapper:true,_jsPlumb:E._jsPlumb}]);this._paint=function(H,G){var F=jsPlumb.extend({},G);if(F.outlineColor){F.strokeWidth=F.outlineWidth;F.strokeStyle=jsPlumbUtil.convertStyle(F.outlineColor,true)}if(D.node==null){D.node=D.makeNode(H,F);D.svg.appendChild(D.node);D.attachListeners(D.node,D)}x(D.svg,D.node,F,H,D);n(D.node,H)};this.reattachListeners=function(){if(D.node){D.reattachListenersForElement(D.node,D)}}};jsPlumb.Endpoints.svg.Dot=function(){jsPlumb.Endpoints.Dot.apply(this,arguments);y.apply(this,arguments);this.makeNode=function(E,D){return f("circle",{cx:E[2]/2,cy:E[3]/2,r:E[2]/2})}};jsPlumb.Endpoints.svg.Rectangle=function(){jsPlumb.Endpoints.Rectangle.apply(this,arguments);y.apply(this,arguments);this.makeNode=function(E,D){return f("rect",{width:E[2],height:E[3]})}};jsPlumb.Endpoints.svg.Image=jsPlumb.Endpoints.Image;jsPlumb.Endpoints.svg.Blank=jsPlumb.Endpoints.Blank;jsPlumb.Overlays.svg.Label=jsPlumb.Overlays.Label;var p=function(H,F){H.apply(this,F);jsPlumb.jsPlumbUIComponent.apply(this,F);this.isAppendedAtTopLevel=false;var D=this,G=null;this.paint=function(J,M,I,N,K){if(G==null){G=f("path",{"pointer-events":"all"});J.svg.appendChild(G);D.attachListeners(G,J);D.attachListeners(G,D)}var L=F&&(F.length==1)?(F[0].cssClass||""):"";g(G,{d:E(M),"class":L,stroke:N?N:null,fill:K?K:null})};var E=function(I){return"M"+I.hxy.x+","+I.hxy.y+" L"+I.tail[0].x+","+I.tail[0].y+" L"+I.cxy.x+","+I.cxy.y+" L"+I.tail[1].x+","+I.tail[1].y+" L"+I.hxy.x+","+I.hxy.y};this.reattachListeners=function(){if(G){D.reattachListenersForElement(G,D)}};this.cleanup=function(){if(G!=null){jsPlumb.CurrentLibrary.removeElement(G)}}};jsPlumb.Overlays.svg.Arrow=function(){p.apply(this,[jsPlumb.Overlays.Arrow,arguments])};jsPlumb.Overlays.svg.PlainArrow=function(){p.apply(this,[jsPlumb.Overlays.PlainArrow,arguments])};jsPlumb.Overlays.svg.Diamond=function(){p.apply(this,[jsPlumb.Overlays.Diamond,arguments])};jsPlumb.Overlays.svg.GuideLines=function(){var I=null,D=this,H=null,G,F;jsPlumb.Overlays.GuideLines.apply(this,arguments);this.paint=function(K,M,J,N,L){if(I==null){I=f("path");K.svg.appendChild(I);D.attachListeners(I,K);D.attachListeners(I,D);G=f("path");K.svg.appendChild(G);D.attachListeners(G,K);D.attachListeners(G,D);F=f("path");K.svg.appendChild(F);D.attachListeners(F,K);D.attachListeners(F,D)}g(I,{d:E(M[0],M[1]),stroke:"red",fill:null});g(G,{d:E(M[2][0],M[2][1]),stroke:"blue",fill:null});g(F,{d:E(M[3][0],M[3][1]),stroke:"green",fill:null})};var E=function(K,J){return"M "+K.x+","+K.y+" L"+J.x+","+J.y}}})();(function(){var d=null,i=function(p,o){return jsPlumb.CurrentLibrary.hasClass(a(p),o)},a=function(o){return jsPlumb.CurrentLibrary.getElementObject(o)},m=function(o){return jsPlumb.CurrentLibrary.getOffset(a(o))},n=function(o){return jsPlumb.CurrentLibrary.getPageXY(o)},f=function(o){return jsPlumb.CurrentLibrary.getClientXY(o)};var k=function(){var q=this;q.overlayPlacements=[];jsPlumb.jsPlumbUIComponent.apply(this,arguments);jsPlumbUtil.EventGenerator.apply(this,arguments);this._over=function(z){var B=m(a(q.canvas)),D=n(z),u=D[0]-B.left,C=D[1]-B.top;if(u>0&&C>0&&u<q.canvas.width&&C<q.canvas.height){for(var v=0;v<q.overlayPlacements.length;v++){var w=q.overlayPlacements[v];if(w&&(w[0]<=u&&w[1]>=u&&w[2]<=C&&w[3]>=C)){return true}}var A=q.canvas.getContext("2d").getImageData(parseInt(u),parseInt(C),1,1);return A.data[0]!=0||A.data[1]!=0||A.data[2]!=0||A.data[3]!=0}return false};var p=false,o=false,t=null,s=false,r=function(v,u){return v!=null&&i(v,u)};this.mousemove=function(x){var z=n(x),w=f(x),v=document.elementFromPoint(w[0],w[1]),y=r(v,"_jsPlumb_overlay");var u=d==null&&(r(v,"_jsPlumb_endpoint")||r(v,"_jsPlumb_connector"));if(!p&&u&&q._over(x)){p=true;q.fire("mouseenter",q,x);return true}else{if(p&&(!q._over(x)||!u)&&!y){p=false;q.fire("mouseexit",q,x)}}q.fire("mousemove",q,x)};this.click=function(u){if(p&&q._over(u)&&!s){q.fire("click",q,u)}s=false};this.dblclick=function(u){if(p&&q._over(u)&&!s){q.fire("dblclick",q,u)}s=false};this.mousedown=function(u){if(q._over(u)&&!o){o=true;t=m(a(q.canvas));q.fire("mousedown",q,u)}};this.mouseup=function(u){o=false;q.fire("mouseup",q,u)};this.contextmenu=function(u){if(p&&q._over(u)&&!s){q.fire("contextmenu",q,u)}s=false}};var c=function(p){var o=document.createElement("canvas");p._jsPlumb.appendElement(o,p.parent);o.style.position="absolute";if(p["class"]){o.className=p["class"]}p._jsPlumb.getId(o,p.uuid);if(p.tooltip){o.setAttribute("title",p.tooltip)}return o};var l=function(p){k.apply(this,arguments);var o=[];this.getDisplayElements=function(){return o};this.appendDisplayElement=function(q){o.push(q)}};var h=jsPlumb.CanvasConnector=function(r){l.apply(this,arguments);var o=function(v,t){p.ctx.save();jsPlumb.extend(p.ctx,t);if(t.gradient){var u=p.createGradient(v,p.ctx);for(var s=0;s<t.gradient.stops.length;s++){u.addColorStop(t.gradient.stops[s][0],t.gradient.stops[s][1])}p.ctx.strokeStyle=u}p._paint(v,t);p.ctx.restore()};var p=this,q=p._jsPlumb.connectorClass+" "+(r.cssClass||"");p.canvas=c({"class":q,_jsPlumb:p._jsPlumb,parent:r.parent,tooltip:r.tooltip});p.ctx=p.canvas.getContext("2d");p.appendDisplayElement(p.canvas);p.paint=function(w,t){if(t!=null){jsPlumb.sizeCanvas(p.canvas,w[0],w[1],w[2],w[3]);if(t.outlineColor!=null){var v=t.outlineWidth||1,s=t.lineWidth+(2*v),u={strokeStyle:t.outlineColor,lineWidth:s};o(w,u)}o(w,t)}}};var b=function(r){var p=this;l.apply(this,arguments);var q=p._jsPlumb.endpointClass+" "+(r.cssClass||""),o={"class":q,_jsPlumb:p._jsPlumb,parent:r.parent,tooltip:p.tooltip};p.canvas=c(o);p.ctx=p.canvas.getContext("2d");p.appendDisplayElement(p.canvas);this.paint=function(x,u,s){jsPlumb.sizeCanvas(p.canvas,x[0],x[1],x[2],x[3]);if(u.outlineColor!=null){var w=u.outlineWidth||1,t=u.lineWidth+(2*w);var v={strokeStyle:u.outlineColor,lineWidth:t}}p._paint.apply(this,arguments)}};jsPlumb.Endpoints.canvas.Dot=function(r){jsPlumb.Endpoints.Dot.apply(this,arguments);b.apply(this,arguments);var q=this,p=function(s){try{return parseInt(s)}catch(t){if(s.substring(s.length-1)=="%"){return parseInt(s.substring(0,s-1))}}},o=function(u){var s=q.defaultOffset,t=q.defaultInnerRadius;u.offset&&(s=p(u.offset));u.innerRadius&&(t=p(u.innerRadius));return[s,t]};this._paint=function(A,t,x){if(t!=null){var B=q.canvas.getContext("2d"),u=x.getOrientation(q);jsPlumb.extend(B,t);if(t.gradient){var v=o(t.gradient),y=u[1]==1?v[0]*-1:v[0],s=u[0]==1?v[0]*-1:v[0],z=B.createRadialGradient(A[4],A[4],A[4],A[4]+s,A[4]+y,v[1]);for(var w=0;w<t.gradient.stops.length;w++){z.addColorStop(t.gradient.stops[w][0],t.gradient.stops[w][1])}B.fillStyle=z}B.beginPath();B.arc(A[4],A[4],A[4],0,Math.PI*2,true);B.closePath();if(t.fillStyle||t.gradient){B.fill()}if(t.strokeStyle){B.stroke()}}}};jsPlumb.Endpoints.canvas.Rectangle=function(p){var o=this;jsPlumb.Endpoints.Rectangle.apply(this,arguments);b.apply(this,arguments);this._paint=function(x,r,v){var A=o.canvas.getContext("2d"),t=v.getOrientation(o);jsPlumb.extend(A,r);if(r.gradient){var z=t[1]==1?x[3]:t[1]==0?x[3]/2:0;var y=t[1]==-1?x[3]:t[1]==0?x[3]/2:0;var s=t[0]==1?x[2]:t[0]==0?x[2]/2:0;var q=t[0]==-1?x[2]:t[0]==0?x[2]/2:0;var w=A.createLinearGradient(s,z,q,y);for(var u=0;u<r.gradient.stops.length;u++){w.addColorStop(r.gradient.stops[u][0],r.gradient.stops[u][1])}A.fillStyle=w}A.beginPath();A.rect(0,0,x[2],x[3]);A.closePath();if(r.fillStyle||r.gradient){A.fill()}if(r.strokeStyle){A.stroke()}}};jsPlumb.Endpoints.canvas.Triangle=function(p){var o=this;jsPlumb.Endpoints.Triangle.apply(this,arguments);b.apply(this,arguments);this._paint=function(z,q,v){var s=z[2],C=z[3],B=z[0],A=z[1],D=o.canvas.getContext("2d"),w=0,u=0,t=0,r=v.getOrientation(o);if(r[0]==1){w=s;u=C;t=180}if(r[1]==-1){w=s;t=90}if(r[1]==1){u=C;t=-90}D.fillStyle=q.fillStyle;D.translate(w,u);D.rotate(t*Math.PI/180);D.beginPath();D.moveTo(0,0);D.lineTo(s/2,C/2);D.lineTo(0,C);D.closePath();if(q.fillStyle||q.gradient){D.fill()}if(q.strokeStyle){D.stroke()}}};jsPlumb.Endpoints.canvas.Image=jsPlumb.Endpoints.Image;jsPlumb.Endpoints.canvas.Blank=jsPlumb.Endpoints.Blank;jsPlumb.Connectors.canvas.Bezier=function(){var o=this;jsPlumb.Connectors.Bezier.apply(this,arguments);h.apply(this,arguments);this._paint=function(q,p){o.ctx.beginPath();o.ctx.moveTo(q[4],q[5]);o.ctx.bezierCurveTo(q[8],q[9],q[10],q[11],q[6],q[7]);o.ctx.stroke()};this.createGradient=function(r,p,q){return o.ctx.createLinearGradient(r[6],r[7],r[4],r[5])}};jsPlumb.Connectors.canvas.Straight=function(){var p=this,o=[null,[1,-1],[1,1],[-1,1],[-1,-1]];jsPlumb.Connectors.Straight.apply(this,arguments);h.apply(this,arguments);this._paint=function(r,t){p.ctx.beginPath();if(t.dashstyle&&t.dashstyle.split(" ").length==2){var v=t.dashstyle.split(" ");if(v.length!=2){v=[2,2]}var C=[v[0]*t.lineWidth,v[1]*t.lineWidth],y=(r[6]-r[4])/(r[7]-r[5]),G=jsPlumbUtil.segment([r[4],r[5]],[r[6],r[7]]),x=o[G],u=Math.atan(y),z=Math.sqrt(Math.pow(r[6]-r[4],2)+Math.pow(r[7]-r[5],2)),B=Math.floor(z/(C[0]+C[1])),w=[r[4],r[5]];for(var A=0;A<B;A++){p.ctx.moveTo(w[0],w[1]);var q=w[0]+(Math.abs(Math.sin(u)*C[0])*x[0]),F=w[1]+(Math.abs(Math.cos(u)*C[0])*x[1]),E=w[0]+(Math.abs(Math.sin(u)*(C[0]+C[1]))*x[0]),D=w[1]+(Math.abs(Math.cos(u)*(C[0]+C[1]))*x[1]);p.ctx.lineTo(q,F);w=[E,D]}p.ctx.moveTo(w[0],w[1]);p.ctx.lineTo(r[6],r[7])}else{p.ctx.moveTo(r[4],r[5]);p.ctx.lineTo(r[6],r[7])}p.ctx.stroke()};this.createGradient=function(r,q){return q.createLinearGradient(r[4],r[5],r[6],r[7])}};jsPlumb.Connectors.canvas.Flowchart=function(){var o=this;jsPlumb.Connectors.Flowchart.apply(this,arguments);h.apply(this,arguments);this._paint=function(r,q){o.ctx.beginPath();o.ctx.moveTo(r[4],r[5]);for(var p=0;p<r[8];p++){o.ctx.lineTo(r[9+(p*2)],r[10+(p*2)])}o.ctx.lineTo(r[6],r[7]);o.ctx.stroke()};this.createGradient=function(q,p){return p.createLinearGradient(q[4],q[5],q[6],q[7])}};jsPlumb.Overlays.canvas.Label=jsPlumb.Overlays.Label;var g=function(){jsPlumb.jsPlumbUIComponent.apply(this,arguments)};var e=function(p,o){p.apply(this,o);g.apply(this,o);this.paint=function(s,u,q,v,t){var r=s.ctx;r.lineWidth=q;r.beginPath();r.moveTo(u.hxy.x,u.hxy.y);r.lineTo(u.tail[0].x,u.tail[0].y);r.lineTo(u.cxy.x,u.cxy.y);r.lineTo(u.tail[1].x,u.tail[1].y);r.lineTo(u.hxy.x,u.hxy.y);r.closePath();if(v){r.strokeStyle=v;r.stroke()}if(t){r.fillStyle=t;r.fill()}}};jsPlumb.Overlays.canvas.Arrow=function(){e.apply(this,[jsPlumb.Overlays.Arrow,arguments])};jsPlumb.Overlays.canvas.PlainArrow=function(){e.apply(this,[jsPlumb.Overlays.PlainArrow,arguments])};jsPlumb.Overlays.canvas.Diamond=function(){e.apply(this,[jsPlumb.Overlays.Diamond,arguments])}})();(function(a){jsPlumb.CurrentLibrary={addClass:function(c,b){c=jsPlumb.CurrentLibrary.getElementObject(c);try{if(c[0].className.constructor==SVGAnimatedString){jsPlumb.util.svg.addClass(c[0],b)}}catch(d){}c.addClass(b)},animate:function(d,c,b){d.animate(c,b)},appendElement:function(c,b){jsPlumb.CurrentLibrary.getElementObject(b).append(c)},ajax:function(b){b=b||{};b.type=b.type||"get";a.ajax(b)},bind:function(b,c,d){b=jsPlumb.CurrentLibrary.getElementObject(b);b.bind(c,d)},dragEvents:{start:"start",stop:"stop",drag:"drag",step:"step",over:"over",out:"out",drop:"drop",complete:"complete"},extend:function(c,b){return a.extend(c,b)},getAttribute:function(b,c){return b.attr(c)},getClientXY:function(b){return[b.clientX,b.clientY]},getDocumentElement:function(){return document},getDragObject:function(b){return b[1].draggable},getDragScope:function(b){return b.draggable("option","scope")},getDropEvent:function(b){return b[0]},getDropScope:function(b){return b.droppable("option","scope")},getDOMElement:function(b){if(typeof(b)=="string"){return document.getElementById(b)}else{if(b.context){return b[0]}else{return b}}},getElementObject:function(b){return typeof(b)=="string"?a("#"+b):a(b)},getOffset:function(b){return b.offset()},getOriginalEvent:function(b){return b.originalEvent},getPageXY:function(b){return[b.pageX,b.pageY]},getParent:function(b){return jsPlumb.CurrentLibrary.getElementObject(b).parent()},getScrollLeft:function(b){return b.scrollLeft()},getScrollTop:function(b){return b.scrollTop()},getSelector:function(b){return a(b)},getSize:function(b){return[b.outerWidth(),b.outerHeight()]},getTagName:function(b){var c=jsPlumb.CurrentLibrary.getElementObject(b);return c.length>0?c[0].tagName:null},getUIPosition:function(c){if(c.length==1){ret={left:c[0].pageX,top:c[0].pageY}}else{var d=c[1],b=d.offset;ret=b||d.absolutePosition}return ret},hasClass:function(c,b){return c.hasClass(b)},initDraggable:function(c,b,d){b=b||{};b.helper=null;if(d){b.scope=b.scope||jsPlumb.Defaults.Scope}c.draggable(b)},initDroppable:function(c,b){b.scope=b.scope||jsPlumb.Defaults.Scope;c.droppable(b)},isAlreadyDraggable:function(b){b=jsPlumb.CurrentLibrary.getElementObject(b);return b.hasClass("ui-draggable")},isDragSupported:function(c,b){return c.draggable},isDropSupported:function(c,b){return c.droppable},removeClass:function(c,b){c=jsPlumb.CurrentLibrary.getElementObject(c);try{if(c[0].className.constructor==SVGAnimatedString){jsPlumb.util.svg.removeClass(c[0],b)}}catch(d){}c.removeClass(b)},removeElement:function(b,c){jsPlumb.CurrentLibrary.getElementObject(b).remove()},setAttribute:function(c,d,b){c.attr(d,b)},setDraggable:function(c,b){c.draggable("option","disabled",!b)},setDragScope:function(c,b){c.draggable("option","scope",b)},setOffset:function(b,c){jsPlumb.CurrentLibrary.getElementObject(b).offset(c)},trigger:function(d,e,b){var c=jQuery._data(jsPlumb.CurrentLibrary.getElementObject(d)[0],"handle");c(b)},unbind:function(b,c,d){b=jsPlumb.CurrentLibrary.getElementObject(b);b.unbind(c,d)}};a(document).ready(jsPlumb.init)})(jQuery);(function(){"undefined"==typeof Math.sgn&&(Math.sgn=function(l){return 0==l?0:0<l?1:-1});var d={subtract:function(m,l){return{x:m.x-l.x,y:m.y-l.y}},dotProduct:function(m,l){return m.x*l.x+m.y*l.y},square:function(l){return Math.sqrt(l.x*l.x+l.y*l.y)},scale:function(m,l){return{x:m.x*l,y:m.y*l}}},f=Math.pow(2,-65),h=function(y,x){for(var t=[],v=x.length-1,r=2*v-1,s=[],w=[],p=[],q=[],o=[[1,0.6,0.3,0.1],[0.4,0.6,0.6,0.4],[0.1,0.3,0.6,1]],u=0;u<=v;u++){s[u]=d.subtract(x[u],y)}for(u=0;u<=v-1;u++){w[u]=d.subtract(x[u+1],x[u]);w[u]=d.scale(w[u],3)}for(u=0;u<=v-1;u++){for(var m=0;m<=v;m++){p[u]||(p[u]=[]);p[u][m]=d.dotProduct(w[u],s[m])}}for(u=0;u<=r;u++){q[u]||(q[u]=[]);q[u].y=0;q[u].x=parseFloat(u)/r}r=v-1;for(s=0;s<=v+r;s++){u=Math.max(0,s-r);for(w=Math.min(s,v);u<=w;u++){j=s-u;q[u+j].y=q[u+j].y+p[j][u]*o[j][u]}}v=x.length-1;q=a(q,2*v-1,t,0);r=d.subtract(y,x[0]);p=d.square(r);for(u=o=0;u<q;u++){r=d.subtract(y,k(x,v,t[u],null,null));r=d.square(r);if(r<p){p=r;o=t[u]}}r=d.subtract(y,x[v]);r=d.square(r);if(r<p){p=r;o=1}return{location:o,distance:p}},a=function(C,B,x,z){var v=[],w=[],A=[],t=[],u=0,s,y;y=Math.sgn(C[0].y);for(var q=1;q<=B;q++){s=Math.sgn(C[q].y);s!=y&&u++;y=s}switch(u){case 0:return 0;case 1:if(z>=64){x[0]=(C[0].x+C[B].x)/2;return 1}var p,u=C[0].y-C[B].y;y=C[B].x-C[0].x;q=C[0].x*C[B].y-C[B].x*C[0].y;s=max_distance_below=0;for(p=1;p<B;p++){var r=u*C[p].x+y*C[p].y+q;r>s?s=r:r<max_distance_below&&(max_distance_below=r)}p=y;s=(1*(q-s)-p*0)*(1/(0*p-u*1));p=y;y=q-max_distance_below;u=(1*y-p*0)*(1/(0*p-u*1));y=Math.min(s,u);if(Math.max(s,u)-y<f){A=C[B].x-C[0].x;t=C[B].y-C[0].y;x[0]=0+1*(A*(C[0].y-0)-t*(C[0].x-0))*(1/(A*0-t*1));return 1}}k(C,B,0.5,v,w);C=a(v,B,A,z+1);B=a(w,B,t,z+1);for(z=0;z<C;z++){x[z]=A[z]}for(z=0;z<B;z++){x[z+C]=t[z]}return C+B},k=function(m,l,p,q,n){for(var o=[[]],r=0;r<=l;r++){o[0][r]=m[r]}for(m=1;m<=l;m++){for(r=0;r<=l-m;r++){o[m]||(o[m]=[]);o[m][r]||(o[m][r]={});o[m][r].x=(1-p)*o[m-1][r].x+p*o[m-1][r+1].x;o[m][r].y=(1-p)*o[m-1][r].y+p*o[m-1][r+1].y}}if(q!=null){for(r=0;r<=l;r++){q[r]=o[r][0]}}if(n!=null){for(r=0;r<=l;r++){n[r]=o[l-r][r]}}return o[l][0]},g={},e=function(t){var s=g[t];if(!s){var s=[],p=function(u){return function(){return u}},q=function(){return function(u){return u}},n=function(){return function(u){return 1-u}},o=function(u){return function(v){for(var x=1,w=0;w<u.length;w++){x=x*u[w](v)}return x}};s.push(new function(){return function(u){return Math.pow(u,t)}});for(var r=1;r<t;r++){for(var l=[new p(t)],m=0;m<t-r;m++){l.push(new q)}for(m=0;m<r;m++){l.push(new n)}s.push(new o(l))}s.push(new function(){return function(u){return Math.pow(1-u,t)}});g[t]=s}return s},c=function(m,l){for(var p=e(m.length-1),q=0,n=0,o=0;o<m.length;o++){q=q+m[o].x*p[o](l);n=n+m[o].y*p[o](l)}return{x:q,y:n}},b=function(m,l,p){for(var q=c(m,l),n=0,o=p>0?1:-1,r=null;n<Math.abs(p);){l=l+0.005*o;r=c(m,l);n=n+Math.sqrt(Math.pow(r.x-q.x,2)+Math.pow(r.y-q.y,2));q=r}return{point:r,location:l}},i=function(m,l){var o=c(m,l),p=c(m.slice(0,m.length-1),l),n=p.y-o.y,o=p.x-o.x;return n==0?Infinity:Math.atan(n/o)};window.jsBezier={distanceFromCurve:h,gradientAtPoint:i,gradientAtPointAlongCurveFrom:function(m,l,n){l=b(m,l,n);if(l.location>1){l.location=1}if(l.location<0){l.location=0}return i(m,l.location)},nearestPointOnCurve:function(m,l){var n=h(m,l);return{point:k(l,l.length-1,n.location,null,null),location:n.location}},pointOnCurve:c,pointAlongCurveFrom:function(m,l,n){return b(m,l,n).point},perpendicularToCurveAt:function(m,l,n,o){l=b(m,l,o==null?0:o);m=i(m,l.location);o=Math.atan(-1/m);m=n/2*Math.sin(o);n=n/2*Math.cos(o);return[{x:l.point.x+n,y:l.point.y+m},{x:l.point.x-n,y:l.point.y-m}]},locationAlongCurveFrom:function(m,l,n){return b(m,l,n).location}}})();