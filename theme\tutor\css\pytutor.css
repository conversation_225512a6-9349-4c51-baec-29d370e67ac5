/*

Online Python Tutor
https://github.com/pgbovine/OnlinePythonTutor/

Copyright (C) 2010-2012 <PERSON> (<EMAIL>)

Permission is hereby granted, free of charge, to any person obtaining a
copy of this software and associated documentation files (the
"Software"), to deal in the Software without restriction, including
without limitation the rights to use, copy, modify, merge, publish,
distribute, sublicense, and/or sell copies of the Software, and to
permit persons to whom the Software is furnished to do so, subject to
the following conditions:

The above copyright notice and this permission notice shall be included
in all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.
IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY
CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,
TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

*/

/* Most recent color scheme redesign on 2012-08-19 */

/* To prevent CSS namespace clashes, prefix all rules with:
     div.ExecutionVisualizer
*/


/* reset some styles to nullify effects of existing stylesheets
   e.g., http://meyerweb.com/eric/tools/css/reset/
*/
div.ExecutionVisualizer {
  /* none for now */
}

div.ExecutionVisualizer table.visualizer {
  font-family: verdana, arial, helvetica, sans-serif;
  font-size: 10pt;
  margin-bottom: 10px;
}

div.ExecutionVisualizer table.visualizer td.vizLayoutTd {
  vertical-align: top;
}

div.ExecutionVisualizer td#stack_td,
div.ExecutionVisualizer td#heap_td {
  vertical-align:top;
  font-size: 10pt; /* don't make fonts in the heap so big! */
}

div.ExecutionVisualizer #dataViz {
  margin-left: 25px;
}

div.ExecutionVisualizer div#codeDisplayDiv {
  /* set this as default unless user specifies a custom size */
  /* width: 750px; */
  margin-left: 0px;
}

div.ExecutionVisualizer div#pyCodeOutputDiv {
  /*max-width: 550px;*/
  max-height: 450px;
  /*max-height: 620px;*/
  overflow: auto;
  /*margin-bottom: 4px;*/

  margin-left: auto;
  margin-right: auto;
}

div.ExecutionVisualizer table#pyCodeOutput {
  font-family: Andale mono, monospace;
  font-size:11pt;
  line-height:1.1em;

  border-collapse: separate; /* some crazy CSS voodoo that needs to be
                                there so that SVG arrows to the left
                                of the code line up vertically in Chrome */
  border-spacing: 0px;
  border-top: 1px solid #bbb;
  padding-top: 3px;
  border-bottom: 1px solid #bbb;
  margin: 6px auto;
  /* padding-left: 20px; */
  white-space: nowrap;
}

/* don't wrap lines within code output ... FORCE scrollbars to appear */
div.ExecutionVisualizer table#pyCodeOutput td {
  white-space: nowrap;
  vertical-align: middle; /* explicitly force, to override external CSS conflicts */
}

div.ExecutionVisualizer #leftCodeGutterSVG {
  width: 18px;
  height: 0px; /* programmatically set this later ... IE needs this to
                  be 0 or it defaults to something arbitrary and gross */
}

div.ExecutionVisualizer #prevLegendArrowSVG,
div.ExecutionVisualizer #curLegendArrowSVG {
  width: 18px;
  height: 10px;
}

div.ExecutionVisualizer .arrow {
  font-size: 16pt;
}

div.ExecutionVisualizer table#pyCodeOutput .lineNo {
  color: #aaa;
  padding: 0.2em;
  padding-left: 0.3em;
  padding-right: 0.5em;
  text-align: right;
}

div.ExecutionVisualizer table#pyCodeOutput .cod {
  /*font-weight: bold;*/
  margin-left: 3px;
  padding-left: 7px;
  text-align: left; /* necessary or else doesn't work properly in IE */
}

div.ExecutionVisualizer div#progOutputs {
  margin-top: 8px;
}

div.ExecutionVisualizer div#legendDiv {
  margin-top: 10px;
  padding: 0px;
  text-align: left;
  color: #666;
  font-size: 9pt;
}

div.ExecutionVisualizer div#editCodeLinkDiv {
  text-align: center;
  /*
  margin-top: 12px;
  margin-bottom: 4px;
  */
  margin: 8px auto;
}

div.ExecutionVisualizer div#annotateLinkDiv {
  /*text-align: left;*/
  margin-top: 0px;
  margin-bottom: 12px;
  /*
  margin-left: auto;
  margin-right: auto;
  */
}

div.ExecutionVisualizer div#stepAnnotationDiv {
  margin-bottom: 12px;
}

div.ExecutionVisualizer textarea#stepAnnotationEditor,
div.ExecutionVisualizer textarea#vizTitleEditor,
div.ExecutionVisualizer textarea#vizDescriptionEditor {
  border: 1px solid #999999;
  padding: 4px;

  overflow: auto; /* to look pretty on IE */
  /* make sure textarea doesn't grow and stretch */
  resize: none;
}


div.ExecutionVisualizer #errorOutput {
  color: #e93f34; /* should match brightRed JavaScript variable */
  font-size: 12pt;
  padding: 2px;
  line-height: 1.5em;
  margin-bottom: 4px;
}

/* VCR control buttons for stepping through execution */

div.ExecutionVisualizer #vcrControls {
  margin: 15px auto;
  /*width: 100%;*/
  text-align: center;
}

div.ExecutionVisualizer #vcrControls button {
  margin-left: 2px;
  margin-right: 2px;
}

div.ExecutionVisualizer #vcrControls #curInstr {
  margin-left: 4px;
  margin-right: 4px;
}

div.ExecutionVisualizer #pyStdout {
  border: 1px solid #999999;
  font-size: 12pt;
  padding: 4px;
  font-family: Andale mono, monospace;

  overflow: auto; /* to look pretty on IE */
  /* make sure textarea doesn't grow and stretch */
  resize: none;
}


div.ExecutionVisualizer .vizFrame {
  margin-bottom: 20px;
  padding-left: 8px;
  border-left: 2px solid #cccccc;
}


/* Rendering of primitive types */

div.ExecutionVisualizer .nullObj {
//  font-size: 8pt;
}

div.ExecutionVisualizer .stringObj,
div.ExecutionVisualizer .customObj,
div.ExecutionVisualizer .funcObj {
  font-family: Andale mono, monospace;
  white-space: nowrap;
}

div.ExecutionVisualizer .retval {
  font-size: 9pt;
}

div.ExecutionVisualizer .stackFrame .retval {
  color: #e93f34; /* highlight non-zombie stack frame return values -
                     should match brightRed JavaScript variable */
}

/* Rendering of basic compound types */

div.ExecutionVisualizer table.listTbl,
div.ExecutionVisualizer table.tupleTbl,
div.ExecutionVisualizer table.setTbl {
  background-color: #ffffc6;
}


div.ExecutionVisualizer table.listTbl {
  border: 0px solid black;
  border-spacing: 0px;
}

div.ExecutionVisualizer table.listTbl td.listHeader,
div.ExecutionVisualizer table.tupleTbl td.tupleHeader {
  padding-left: 4px;
  padding-top: 2px;
  padding-bottom: 3px;
  font-size: 8pt;
  color: #777;
  text-align: left;
  border-left: 1px solid #555555;
}

div.ExecutionVisualizer table.tupleTbl {
  border-spacing: 0px;
  color: black;

  border-bottom: 1px solid #555555; /* must match td.tupleHeader border */
  border-top: 1px solid #555555; /* must match td.tupleHeader border */
  border-right: 1px solid #555555; /* must match td.tupleHeader border */
}


div.ExecutionVisualizer table.listTbl td.listElt {
  border-bottom: 1px solid #555555; /* must match td.listHeader border */
  border-left: 1px solid #555555; /* must match td.listHeader border */
}

div.ExecutionVisualizer table.tupleTbl td.tupleElt {
  border-left: 1px solid #555555; /* must match td.tupleHeader border */
}

div.ExecutionVisualizer table.customObjTbl {
  background-color: white;
  color: black;
  border: 1px solid black;
}

div.ExecutionVisualizer table.customObjTbl td.customObjElt {
  padding: 5px;
}

div.ExecutionVisualizer table.listTbl td.listElt,
div.ExecutionVisualizer table.tupleTbl td.tupleElt {
  padding-top: 0px;
  padding-bottom: 8px;
  padding-left: 10px;
  padding-right: 10px;
  vertical-align: bottom;
}

div.ExecutionVisualizer table.setTbl {
  border: 1px solid #555555;
  border-spacing: 0px;
  text-align: center;
}

div.ExecutionVisualizer table.setTbl td.setElt {
  padding: 8px;
}


div.ExecutionVisualizer table.dictTbl,
div.ExecutionVisualizer table.instTbl,
div.ExecutionVisualizer table.classTbl {
  border-spacing: 1px;
}

div.ExecutionVisualizer table.dictTbl td.dictKey,
div.ExecutionVisualizer table.instTbl td.instKey,
div.ExecutionVisualizer table.classTbl td.classKey {
  background-color: #faebbf;
}

div.ExecutionVisualizer table.dictTbl td.dictVal,
div.ExecutionVisualizer table.instTbl td.instVal,
div.ExecutionVisualizer table.classTbl td.classVal {
  background-color: #ffffc6;
}


div.ExecutionVisualizer table.dictTbl td.dictKey,
div.ExecutionVisualizer table.instTbl td.instKey,
div.ExecutionVisualizer table.classTbl td.classKey {
  padding-top: 12px /*15px*/;
  padding-bottom: 5px;
  padding-left: 10px;
  padding-right: 4px;

  text-align: right;
}

div.ExecutionVisualizer table.dictTbl td.dictVal,
div.ExecutionVisualizer table.instTbl td.instVal,
div.ExecutionVisualizer table.classTbl td.classVal {
  padding-top: 12px /*15px*/;
  padding-bottom: 5px;
  padding-right: 10px;
  padding-left: 4px;
}


div.ExecutionVisualizer table.classTbl td,
div.ExecutionVisualizer table.instTbl td {
  border-bottom: 1px #888 solid;
}

div.ExecutionVisualizer table.classTbl td.classVal,
div.ExecutionVisualizer table.instTbl td.instVal {
  border-left: 1px #888 solid;
}

div.ExecutionVisualizer table.classTbl {
  border-collapse: collapse;
  border: 1px #888 solid;
}

/* only add a border to dicts if they're embedded within another object */
div.ExecutionVisualizer td.listElt table.dictTbl,
div.ExecutionVisualizer td.tupleElt table.dictTbl,
div.ExecutionVisualizer td.dictVal table.dictTbl,
div.ExecutionVisualizer td.instVal table.dictTbl,
div.ExecutionVisualizer td.classVal table.dictTbl {
  border: 1px #888 solid;
}

div.ExecutionVisualizer .objectIdLabel {
  font-size: 8pt;
  color: #444;
  margin-bottom: 2px;
}

div.ExecutionVisualizer .typeLabel {
  font-size: 8pt;
  color: #555;
  margin-bottom: 2px;
}

div.ExecutionVisualizer div#stack,
div.ExecutionVisualizer div#globals_area {
  padding-left: 10px;
  padding-right: 30px;

  /* no longer necessary ... */
  /*float: left;*/
  /* border-right: 1px dashed #bbbbbb; */
}

div.ExecutionVisualizer div.stackFrame,
div.ExecutionVisualizer div.zombieStackFrame {
  background-color: #ffffff;
  margin-bottom: 15px;
  padding: 2px;
  padding-left: 6px;
  padding-right: 6px;
  padding-bottom: 4px;
  font-size: 10pt;
  white-space: nowrap;
}

div.ExecutionVisualizer div.zombieStackFrame {
  border-left: 1px dotted #aaa;
  /*color: #c0c0c0;*/
  color: #808080;
}

div.ExecutionVisualizer div.highlightedStackFrame {
  background-color: #d7e7fb;

  /*background-color: #c0daf8;*/
  /*background-color: #9eeaff #c5dfea;*/
}

div.ExecutionVisualizer div.stackFrame,
div.ExecutionVisualizer div.highlightedStackFrame {
  border-left: 1px solid #a6b3b6;
}


div.ExecutionVisualizer div.stackFrameHeader {
  font-family: Andale mono, monospace;
  font-size: 10pt;
  margin-top: 4px;
  margin-bottom: 3px;
  white-space: nowrap;
}

div.ExecutionVisualizer td.stackFrameVar {
  text-align: right;
  padding-right: 8px;
  padding-top: 3px;
  padding-bottom: 3px;
}

div.ExecutionVisualizer td.stackFrameValue {
  text-align: left;
  border-bottom: 1px solid #aaaaaa;
  border-left: 1px solid #aaaaaa;

  vertical-align: middle;

  padding-top: 3px;
  padding-left: 3px;
  padding-bottom: 3px;
}

div.ExecutionVisualizer .stackFrameVarTable tr {

}

div.ExecutionVisualizer .stackFrameVarTable {
  text-align: right;
  padding-top: 3px;

  /* right-align the table */
  margin-left: auto;
  margin-right: 0px;

  /* hack to counteract possible nasty CSS reset styles from parent divs */
  border-collapse: separate;
  border-spacing: 2px;
}

div.ExecutionVisualizer div#heap {
  float: left;
  padding-left: 30px;
}

div.ExecutionVisualizer td.toplevelHeapObject {
  /* needed for d3 to do transitions */
  padding-left: 8px;
  padding-right: 8px;
  padding-top: 4px;
  padding-bottom: 4px;
  /*
  border: 2px dotted white;
  border-color: white;
  */
}

div.ExecutionVisualizer table.heapRow {
  margin-bottom: 10px;
}

div.ExecutionVisualizer div.heapObject {
  padding-left: 2px; /* leave a TINY amount of room for connector endpoints */
}

div.ExecutionVisualizer div.heapPrimitive {
  padding-left: 4px; /* leave some more room for connector endpoints */
}

div.ExecutionVisualizer div#stackHeader {
  margin-bottom: 15px;
  text-align: right;
}

div.ExecutionVisualizer div#heapHeader {
  /*margin-top: 2px;
  margin-bottom: 13px;*/
  margin-bottom: 15px;
}

div.ExecutionVisualizer div#stackHeader,
div.ExecutionVisualizer div#heapHeader {
  color: #333333;
  font-size: 10pt;
}

div.ExecutionVisualizer #executionSlider {
  /* if you set 'width', then it looks ugly when you dynamically resize */
  margin-top: 15px;
  margin-bottom: 5px;

  margin-left: auto;
  margin-right: auto;

  width: 95%;
}

div.ExecutionVisualizer #executionSliderCaption {
  font-size: 8pt;
  color: #666666;
  margin-top: 15px;
}

div.ExecutionVisualizer #executionSliderFooter {
  margin-top: -7px; /* make it butt up against #executionSlider */
}


/* darken slider handle a bit */
div.ExecutionVisualizer .ui-slider .ui-slider-handle {
  border: 1px solid #999;
}


/* for annotation bubbles */

/* For styling tricks, see: http://css-tricks.com/textarea-tricks/ */
textarea.bubbleInputText {
  border: 1px solid #ccc;
  outline: none;
  overflow: auto; /* to look pretty on IE */

  /* make sure textarea doesn't grow and stretch the enclosing bubble */
  resize: none;
  width: 225px;
  max-width: 225px;
  height: 35px;
  max-height: 35px;
}


.ui-tooltip-pgbootstrap,
textarea.bubbleInputText {
  font-family: verdana, arial, helvetica, sans-serif;
	font-size: 9pt;
	line-height: 1.3em;
}


/* modified version of Twitter bootstrap style by Philip Guo */
.ui-tooltip-pgbootstrap{
	color: #333;
	background-color: #ffffff;

	max-width: 250px;
	min-width: 10px;

	border: 2px solid #4284D3;

  cursor: pointer;

	*border-right-width: 2px;
	*border-bottom-width: 2px;

	-webkit-border-radius: 5px;
	-moz-border-radius: 5px;
	border-radius: 5px;

  /* way too poofy ...
	-webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
	box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  */

	-webkit-box-shadow: 2px 2px 3px 0px rgba(0, 0, 0, 0.2);
	-moz-box-shadow: 2px 2px 3px 0px rgba(0, 0, 0, 0.2);
	box-shadow: 2px 2px 3px 0px rgba(0, 0, 0, 0.2);

	-webkit-background-clip: padding-box;
	-moz-background-clip: padding;
	background-clip: padding-box;
}

	.ui-tooltip-pgbootstrap .ui-tooltip-titlebar{
		font-size: 18px;
		line-height: 22px;

		border-bottom: 1px solid #ccc;
		background-color: transparent;
	}

	.ui-tooltip-pgbootstrap .ui-tooltip-content{
		padding: 5px /* 5px is minimum or else it might look ugly */ 8px;
	}


		.ui-tooltip-pgbootstrap .ui-tooltip-titlebar .ui-state-default{
			right: 9px; top: 49%;
			border-style: none;
		}

	.ui-tooltip-pgbootstrap .ui-tooltip-icon{
		background: white;
	}

		.ui-tooltip-pgbootstrap .ui-tooltip-icon .ui-icon{
			width: auto;
			height: auto;
			float: right;
			font-size: 20px;
			font-weight: bold;
			line-height: 18px;
			color: #000000;
			text-shadow: 0 1px 0 #ffffff;
			opacity: 0.2;
			filter: alpha(opacity=20);
		}

		.ui-tooltip-pgbootstrap .ui-tooltip-icon .ui-icon:hover{
			color: #000000;
			text-decoration: none;
			cursor: pointer;
			opacity: 0.4;
			filter: alpha(opacity=40);
		}


/* Add rounded corners to your tooltips in: FF3+, Chrome 2+, Opera 10.6+, IE9+, Safari 2+ */
.ui-tooltip-pgbootstrap{
	-moz-border-radius: 5px;
	-webkit-border-radius: 5px;
	border-radius: 5px;
}


.ui-tooltip-pgbootstrap-stub{
	border: 1px solid #999;

  /*
	-webkit-box-shadow: none;
	-moz-box-shadow: none;
	box-shadow: none;
  */
}

	.ui-tooltip-pgbootstrap-stub .ui-tooltip-content{
		padding: 6px 9px;
	}


div.ExecutionVisualizer .annotationText,
div.ExecutionVisualizer .vizDescriptionText {
  font-family: verdana, arial, helvetica, sans-serif;
  font-size: 11pt;
  line-height: 1.5em;
}

div.ExecutionVisualizer .vizTitleText {
  font-family: verdana, arial, helvetica, sans-serif;
  font-size: 16pt;
  margin-bottom: 12pt;
}

div.ExecutionVisualizer div#vizHeader {
  margin-bottom: 10px;
  width: 700px;
  max-width: 700px;
}

/* prev then curr, so curr gets precedence when both apply */
div.ExecutionVisualizer .highlight-prev {
  background-color: #F0F0EA;
}

div.ExecutionVisualizer .highlight-curr {
  background-color: #FFFF66;
}

div.ExecutionVisualizer .highlight-legend {
  padding: 2px;
}

/* resizing sliders from David Pritchard */
.ui-resizable-e {
  background-color: #dddddd;
  width: 1px;
  border: 3px solid white;
}

.ui-resizable-e:hover {
  border-color: #dddddd;
}


/* for pyCrazyMode */

/* prev then curr, so curr gets precedence when both apply */
div.ExecutionVisualizer .pycrazy-highlight-prev {
  background-color: #eeeeee; /*#F0F0EA;*/
  /*
  text-decoration: none;
  border-bottom: 1px solid #dddddd;
  */
}

div.ExecutionVisualizer .pycrazy-highlight-cur {
  background-color: #FFFF66;
  /* aligned slightly higher than border-bottom */
  /*
  text-decoration: none;
  border-bottom: 1px solid #e93f34;
  */
}

div.ExecutionVisualizer .pycrazy-highlight-prev-and-cur {
  background-color: #FFFF66;

  text-decoration: none;
  border-bottom: 1px solid #999999;
}
