<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from www.composingprograms.com/versions/v1/pages/24-mutable-data.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:46:02 GMT -->
<head>
  <title>2.4 Mutable Data</title>
  <meta charset="utf-8" />

  <link rel="stylesheet" type="text/css" href="../theme/css/cp.css" />

  <!-- Stylesheets -->
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/pytutor.css"/>
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/ui-lightness/jquery-ui-1.8.21.custom.css" />
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/codemirror.css"  />

  <!-- jQuery -->
  <script type="text/javascript" src="../theme/tutor/js/jquery-1.8.2.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery-ui-1.8.24.custom.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery.ba-bbq.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery.jsPlumb-1.3.10-all-min.js"></script>

  <!-- codemirror.net online code editor -->
  <script type="text/javascript" src="../theme/tutor/js/codemirror/codemirror.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/codemirror/python.js"></script>

  <!-- d3 -->
  <script type="text/javascript" src="../theme/tutor/js/d3.v2.min.js"></script>

  <!-- Online Python Tutor -->
  <script type="text/javascript" src="../theme/tutor/js/pytutor.js"></script>
  <script type="text/javascript" src="../theme/js/tutorize.js"></script>


    <script src="http://cdn.mathjax.org/mathjax/latest/MathJax.js?config=TeX-AMS-MML_HTMLorMML" type= "text/javascript">
       MathJax.Hub.Config({
           config: ["MMLorHTML.js"],
           jax: ["input/TeX","input/MathML","output/HTML-CSS","output/NativeMML"],
           TeX: { extensions: ["AMSmath.js","AMSsymbols.html","noErrors.html","noUndefined.js"], equationNumbers: { autoNumber: "AMS" } },
           extensions: ["tex2jax.js","mml2jax.html","MathMenu.html","MathZoom.html","jsMath2jax.js"],
           tex2jax: {
               inlineMath: [ ['$','$'] ],
               displayMath: [ ['$$','$$'] ],
               processEscapes: true },
           "HTML-CSS": {
               styles: { ".MathJax .mo, .MathJax .mi": {color: "black ! important"}}
           }
       });
    </script>

</head>

<body id="index" class="home">
  <div class="container">

    <div class="nav-main">
      <div class="wrap">
        <a class="nav-home" href="../index.html">
          <span class="nav-logo">c<span class="nav-logo-compose">⚬</span>mp<span class="nav-logo-compose">⚬</span>sing pr<span class="nav-logo-compose">⚬</span>grams</span>
        </a>
VERSION 1, Replaced July 2014
      </div>
    </div>

    <section class="content wrap documentationContent">
      <div class="nav-docs">
	<h3>Chapter 2<a id="hide_contents">Hide contents</a> </h3>
		<div class="nav-docs-section">
			<h3><a href="21-introduction.html">2.1 Introduction</a></h3>
				<li><a href="21-introduction.html#the-object-metaphor">2.1.1 The Object Metaphor</a>
				<li><a href="21-introduction.html#native-data-types">2.1.2 Native Data Types</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="22-data-abstraction.html">2.2 Data Abstraction</a></h3>
				<li><a href="22-data-abstraction.html#example-arithmetic-on-rational-numbers">2.2.1 Example: Arithmetic on Rational Numbers</a>
				<li><a href="22-data-abstraction.html#pairs">2.2.2 Pairs</a>
				<li><a href="22-data-abstraction.html#abstraction-barriers">2.2.3 Abstraction Barriers</a>
				<li><a href="22-data-abstraction.html#the-properties-of-data">2.2.4 The Properties of Data</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="23-sequences.html">2.3 Sequences</a></h3>
				<li><a href="23-sequences.html#tuples">2.3.1 Tuples</a>
				<li><a href="23-sequences.html#sequence-iteration">2.3.2 Sequence Iteration</a>
				<li><a href="23-sequences.html#sequence-abstraction">2.3.3 Sequence Abstraction</a>
				<li><a href="23-sequences.html#nested-pairs">2.3.4 Nested Pairs</a>
				<li><a href="23-sequences.html#recursive-lists">2.3.5 Recursive Lists</a>
				<li><a href="23-sequences.html#strings">2.3.6 Strings</a>
				<li><a href="23-sequences.html#sequence-processing">2.3.7 Sequence Processing</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="24-mutable-data.html">2.4 Mutable Data</a></h3>
				<li><a href="24-mutable-data.html#lists">2.4.1 Lists</a>
				<li><a href="24-mutable-data.html#dictionaries">2.4.2 Dictionaries</a>
				<li><a href="24-mutable-data.html#local-state">2.4.3 Local State</a>
				<li><a href="24-mutable-data.html#the-benefits-of-non-local-assignment">2.4.4 The Benefits of Non-Local Assignment</a>
				<li><a href="24-mutable-data.html#the-cost-of-non-local-assignment">2.4.5 The Cost of Non-Local Assignment</a>
				<li><a href="24-mutable-data.html#implementing-lists-and-dictionaries">2.4.6 Implementing Lists and Dictionaries</a>
				<li><a href="24-mutable-data.html#dispatch-dictionaries">2.4.7 Dispatch Dictionaries</a>
				<li><a href="24-mutable-data.html#propagating-constraints">2.4.8 Propagating Constraints</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="25-object-oriented-programming.html">2.5 Object-Oriented Programming</a></h3>
				<li><a href="25-object-oriented-programming.html#objects-and-classes">2.5.1 Objects and Classes</a>
				<li><a href="25-object-oriented-programming.html#defining-classes">2.5.2 Defining Classes</a>
				<li><a href="25-object-oriented-programming.html#message-passing-and-dot-expressions">2.5.3 Message Passing and Dot Expressions</a>
				<li><a href="25-object-oriented-programming.html#class-attributes">2.5.4 Class Attributes</a>
				<li><a href="25-object-oriented-programming.html#inheritance">2.5.5 Inheritance</a>
				<li><a href="25-object-oriented-programming.html#using-inheritance">2.5.6 Using Inheritance</a>
				<li><a href="25-object-oriented-programming.html#multiple-inheritance">2.5.7 Multiple Inheritance</a>
				<li><a href="25-object-oriented-programming.html#functions-as-objects">2.5.8 Functions as Objects</a>
				<li><a href="25-object-oriented-programming.html#the-role-of-objects">2.5.9 The Role of Objects</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="26-implementing-classes-and-objects.html">2.6 Implementing Classes and Objects</a></h3>
				<li><a href="26-implementing-classes-and-objects.html#instances">2.6.1 Instances</a>
				<li><a href="26-implementing-classes-and-objects.html#classes">2.6.2 Classes</a>
				<li><a href="26-implementing-classes-and-objects.html#using-implemented-objects">2.6.3 Using Implemented Objects</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="27-recursive-data-structures.html">2.7 Recursive Data Structures</a></h3>
				<li><a href="27-recursive-data-structures.html#a-recursive-list-class">2.7.1 A Recursive List Class</a>
				<li><a href="27-recursive-data-structures.html#hierarchical-structures">2.7.2 Hierarchical Structures</a>
				<li><a href="27-recursive-data-structures.html#memoization">2.7.3 Memoization</a>
				<li><a href="27-recursive-data-structures.html#orders-of-growth">2.7.4 Orders of Growth</a>
				<li><a href="27-recursive-data-structures.html#example-exponentiation">2.7.5 Example: Exponentiation</a>
				<li><a href="27-recursive-data-structures.html#sets">2.7.6 Sets</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="28-generic-operations.html">2.8 Generic Operations</a></h3>
				<li><a href="28-generic-operations.html#string-conversion">2.8.1 String Conversion</a>
				<li><a href="28-generic-operations.html#multiple-representations">2.8.2 Multiple Representations</a>
				<li><a href="28-generic-operations.html#special-methods">2.8.3 Special Methods</a>
				<li><a href="28-generic-operations.html#generic-functions">2.8.4 Generic Functions</a>
		</div>
      </div>

      <div class="inner-content">
  <div class="section" id="mutable-data">
<h2>2.4   Mutable Data</h2>
<p>We have seen how abstraction is vital in helping us to cope with the complexity
of large systems.  Effective program synthesis also requires organizational
principles that can guide us in formulating the overall design of a program. In
particular, we need strategies to help us structure large systems so that they
will be <em>modular</em>, that is, so that they can be divided "naturally" into
coherent parts that can be separately developed and maintained.</p>
<p>One powerful technique for creating modular programs is to introduce new kinds
of data that may change state over time.  In this way, a single data object can
represent something that evolves independently of the rest of the program. The
behavior of a changing object may be influenced by its history, just like an
entity in the world. Adding state to data is an essential ingredient of our
final destination in this chapter: object-oriented programming.</p>
<p>The native data types we have introduced so far — numbers, Booleans, tuples,
ranges, and strings — are all types of <em>immutable</em> objects. While names may
change bindings to different values in the environment during the course of
execution, the values themselves do not change. <em>Mutable objects</em> (also called
mutable values) can change throughout the execution of a program.</p>
<div class="section" id="lists">
<h3>2.4.1   Lists</h3>
<p>The <tt class="docutils literal">list</tt> is Python's most useful and flexible sequence type.  A list is
similar to a tuple, but it is mutable:  Method calls and assignment
statements can change the contents of a list.</p>
<p>We can introduce many list modification operations through an example that
illustrates the history of playing cards (drastically simplified). Comments in
the examples describe the effect of each method invocation.</p>
<p>Playing cards were invented in China, perhaps around the 9th century. An early
deck had three suits, which corresponded to denominations of money.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">chinese_suits</span> <span class="o">=</span> <span class="p">[</span><span class="s">'coin'</span><span class="p">,</span> <span class="s">'string'</span><span class="p">,</span> <span class="s">'myriad'</span><span class="p">]</span>  <span class="c"># A list literal</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span> <span class="o">=</span> <span class="n">chinese_suits</span>                         <span class="c"># Two names refer to the same list</span>
</pre></div>

<p>As cards migrated to Europe (perhaps through Egypt), only the suit of coins
remained in Spanish decks (<em>oro</em>).</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span><span class="o">.</span><span class="n">pop</span><span class="p">()</span>             <span class="c"># Remove and return the final element</span>
<span class="go">'myriad'</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span><span class="o">.</span><span class="n">remove</span><span class="p">(</span><span class="s">'string'</span><span class="p">)</span>  <span class="c"># Remove the first element that equals the argument</span>
</pre></div>

<p>Three more suits were added (they evolved in name and design over time),</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="s">'cup'</span><span class="p">)</span>              <span class="c"># Add an element to the end</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span><span class="o">.</span><span class="n">extend</span><span class="p">([</span><span class="s">'sword'</span><span class="p">,</span> <span class="s">'club'</span><span class="p">])</span>  <span class="c"># Add all elements of a list to the end</span>
</pre></div>

<p>and Italians called swords <em>spades</em>.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span><span class="p">[</span><span class="mi">2</span><span class="p">]</span> <span class="o">=</span> <span class="s">'spade'</span>  <span class="c"># Replace an element</span>
</pre></div>

<p>giving the suits of a traditional Italian deck of cards.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span>
<span class="go">['coin', 'cup', 'spade', 'club']</span>
</pre></div>

<p>The French variant that we use today in the U.S. changes the first two:</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span><span class="p">[</span><span class="mi">0</span><span class="p">:</span><span class="mi">2</span><span class="p">]</span> <span class="o">=</span> <span class="p">[</span><span class="s">'heart'</span><span class="p">,</span> <span class="s">'diamond'</span><span class="p">]</span>  <span class="c"># Replace a slice</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span>
<span class="go">['heart', 'diamond', 'spade', 'club']</span>
</pre></div>

<p>Methods also exist for inserting, sorting, and reversing lists.  All of these
<em>mutation operations</em> change the value of the list; they do not create new list
objects.</p>
<p><strong>Sharing and Identity.</strong> Because we have been changing a single list rather
than creating new lists, the object bound to the name <tt class="docutils literal">chinese_suits</tt> has also
changed, because it is the same list object that was bound to <tt class="docutils literal">suits</tt>!</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">chinese_suits</span>  <span class="c"># This name co-refers with "suits" to the same list</span>
<span class="go">['heart', 'diamond', 'spade', 'club']</span>
</pre></div>

<p>This behavior is new.  Previously, if a name did not appear in a statement,
then its value would not be affected by that statement.  With mutable data,
methods called on one name can affect another name at the same time.</p>
<p>The environment diagram for this example shows how the value bound to
<tt class="docutils literal">chinese</tt> is changed by statements involving only <tt class="docutils literal">suits</tt>. Step through
each line of the following example to observe these changes.</p>
<div class="example" data-output="False" data-step="3" id="example_32" style="">
chinese = ['coin', 'string', 'myriad']
suits = chinese
suits.pop()
suits.remove('string')
suits.append('cup')
suits.extend(['sword', 'club'])
suits[2] = 'spade'
suits[0:2] = ['heart', 'diamond']
</div>
<script type="text/javascript">
var example_32_trace = {"trace": [{"line": 1, "event": "step_line", "globals": {}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": [], "heap": {}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"chinese": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["chinese"], "heap": {"1": ["LIST", "coin", "string", "myriad"]}, "stdout": ""}, {"line": 3, "event": "step_line", "globals": {"chinese": ["REF", 1], "suits": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["chinese", "suits"], "heap": {"1": ["LIST", "coin", "string", "myriad"]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"chinese": ["REF", 1], "suits": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["chinese", "suits"], "heap": {"1": ["LIST", "coin", "string"]}, "stdout": ""}, {"line": 5, "event": "step_line", "globals": {"chinese": ["REF", 1], "suits": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["chinese", "suits"], "heap": {"1": ["LIST", "coin"]}, "stdout": ""}, {"line": 6, "event": "step_line", "globals": {"chinese": ["REF", 1], "suits": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["chinese", "suits"], "heap": {"1": ["LIST", "coin", "cup"]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"chinese": ["REF", 1], "suits": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["chinese", "suits"], "heap": {"1": ["LIST", "coin", "cup", "sword", "club"]}, "stdout": ""}, {"line": 8, "event": "step_line", "globals": {"chinese": ["REF", 1], "suits": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["chinese", "suits"], "heap": {"1": ["LIST", "coin", "cup", "spade", "club"]}, "stdout": ""}, {"line": 8, "event": "return", "globals": {"chinese": ["REF", 1], "suits": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["chinese", "suits"], "heap": {"1": ["LIST", "heart", "diamond", "spade", "club"]}, "stdout": ""}], "code": "chinese = ['coin', 'string', 'myriad']\nsuits = chinese\nsuits.pop()\nsuits.remove('string')\nsuits.append('cup')\nsuits.extend(['sword', 'club'])\nsuits[2] = 'spade'\nsuits[0:2] = ['heart', 'diamond']"}</script><p>Lists can be copied using the <tt class="docutils literal">list</tt> constructor function.  Changes to one
list do not affect another, unless they share structure.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">nest</span> <span class="o">=</span> <span class="nb">list</span><span class="p">(</span><span class="n">suits</span><span class="p">)</span>  <span class="c"># Bind "nest" to a second list with the same elements</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">nest</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">=</span> <span class="n">suits</span>     <span class="c"># Create a nested list</span>
</pre></div>

<p>According to this environment, changing the list referenced by <tt class="docutils literal">suits</tt> will
affect the nested list that is the first element of <tt class="docutils literal">nest</tt>, but not the other
elements.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span><span class="o">.</span><span class="n">insert</span><span class="p">(</span><span class="mi">2</span><span class="p">,</span> <span class="s">'Joker'</span><span class="p">)</span>  <span class="c"># Insert an element at index 2, shifting the rest</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">nest</span>
<span class="go">[['heart', 'diamond', 'Joker', 'spade', 'club'], 'diamond', 'spade', 'club']</span>
</pre></div>

<p>And likewise, undoing this change in the first element of <tt class="docutils literal">nest</tt> will change
<tt class="docutils literal">suit</tt> as well.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">nest</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span><span class="o">.</span><span class="n">pop</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
<span class="go">'Joker'</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span>
<span class="go">['heart', 'diamond', 'spade', 'club']</span>
</pre></div>

<p>Stepping through this example line by line will show the representation of a
nested list.</p>
<div class="example" data-output="False" data-step="0" id="example_33" style="">
suits = ['heart', 'diamond', 'spade', 'club']
nest = list(suits)
nest[0] = suits
suits.insert(2, 'Joker')
j = nest[0].pop(2)
</div>
<script type="text/javascript">
var example_33_trace = {"trace": [{"line": 1, "event": "step_line", "globals": {}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": [], "heap": {}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"suits": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["suits"], "heap": {"1": ["LIST", "heart", "diamond", "spade", "club"]}, "stdout": ""}, {"line": 3, "event": "step_line", "globals": {"nest": ["REF", 2], "suits": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["suits", "nest"], "heap": {"1": ["LIST", "heart", "diamond", "spade", "club"], "2": ["LIST", "heart", "diamond", "spade", "club"]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"nest": ["REF", 2], "suits": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["suits", "nest"], "heap": {"1": ["LIST", "heart", "diamond", "spade", "club"], "2": ["LIST", ["REF", 1], "diamond", "spade", "club"]}, "stdout": ""}, {"line": 5, "event": "step_line", "globals": {"nest": ["REF", 2], "suits": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["suits", "nest"], "heap": {"1": ["LIST", "heart", "diamond", "Joker", "spade", "club"], "2": ["LIST", ["REF", 1], "diamond", "spade", "club"]}, "stdout": ""}, {"line": 5, "event": "return", "globals": {"nest": ["REF", 2], "suits": ["REF", 1], "j": "Joker"}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["suits", "nest", "j"], "heap": {"1": ["LIST", "heart", "diamond", "spade", "club"], "2": ["LIST", ["REF", 1], "diamond", "spade", "club"]}, "stdout": ""}], "code": "suits = ['heart', 'diamond', 'spade', 'club']\nnest = list(suits)\nnest[0] = suits\nsuits.insert(2, 'Joker')\nj = nest[0].pop(2)"}</script><p>Because two lists may have the same contents but in fact be different lists, we
require a means to test whether two objects are the same.  Python includes two
comparison operators, called <tt class="docutils literal">is</tt> and <tt class="docutils literal">is not</tt>, that test whether two
expressions in fact evaluate to the identical object.  Two objects are identical
if they are equal in their current value, and any change to one will always be
reflected in the other. Identity is a stronger condition than equality.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span> <span class="ow">is</span> <span class="n">nest</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span>
<span class="go">True</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span> <span class="ow">is</span> <span class="p">[</span><span class="s">'heart'</span><span class="p">,</span> <span class="s">'diamond'</span><span class="p">,</span> <span class="s">'spade'</span><span class="p">,</span> <span class="s">'club'</span><span class="p">]</span>
<span class="go">False</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">suits</span> <span class="o">==</span> <span class="p">[</span><span class="s">'heart'</span><span class="p">,</span> <span class="s">'diamond'</span><span class="p">,</span> <span class="s">'spade'</span><span class="p">,</span> <span class="s">'club'</span><span class="p">]</span>
<span class="go">True</span>
</pre></div>

<p>The final two comparisons illustrate the difference between <tt class="docutils literal">is</tt> and <tt class="docutils literal">==</tt>.
The former checks for identity, while the latter checks for the equality of
contents.</p>
<p><strong>List comprehensions.</strong> A list comprehension uses an extended syntax for
creating lists, analogous to the syntax of generator expressions.</p>
<p>For example, the <tt class="docutils literal">unicodedata</tt> module tracks the official names of every
character in the Unicode alphabet.  We can look up the characters corresponding
to names, including those for card suits.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">unicodedata</span> <span class="k">import</span> <span class="n">lookup</span>
<span class="gp">&gt;&gt;&gt; </span><span class="p">[</span><span class="n">lookup</span><span class="p">(</span><span class="s">'WHITE '</span> <span class="o">+</span> <span class="n">s</span><span class="o">.</span><span class="n">upper</span><span class="p">()</span> <span class="o">+</span> <span class="s">' SUIT'</span><span class="p">)</span> <span class="k">for</span> <span class="n">s</span> <span class="ow">in</span> <span class="n">suits</span><span class="p">]</span>
<span class="go">['♡', '♢', '♤', '♧']</span>
</pre></div>

<p>List comprehensions reinforce the general approach of sequence processing, as
<tt class="docutils literal">list</tt> is a sequence data type.</p>
</div>
<div class="section" id="dictionaries">
<h3>2.4.2   Dictionaries</h3>
<p>Dictionaries are Python's built-in data type for storing and manipulating
correspondence relationships.  A dictionary contains key-value pairs, where both
the keys and values are objects.  The purpose of a dictionary is to provide an
abstraction for storing and retrieving values that are indexed not by
consecutive integers, but by descriptive keys.</p>
<p>Strings commonly serve as keys, because strings are our conventional
representation for names of things. This dictionary literal gives the values of
various Roman numerals.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">numerals</span> <span class="o">=</span> <span class="p">{</span><span class="s">'I'</span><span class="p">:</span> <span class="mf">1.0</span><span class="p">,</span> <span class="s">'V'</span><span class="p">:</span> <span class="mi">5</span><span class="p">,</span> <span class="s">'X'</span><span class="p">:</span> <span class="mi">10</span><span class="p">}</span>
</pre></div>

<p>Looking up values by their keys uses the element selection operator that we
previously applied to sequences.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">numerals</span><span class="p">[</span><span class="s">'X'</span><span class="p">]</span>
<span class="go">10</span>
</pre></div>

<p>A dictionary can have at most one value for each key.  Adding new key-value
pairs and changing the existing value for a key can both be achieved with
assignment statements.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">numerals</span><span class="p">[</span><span class="s">'I'</span><span class="p">]</span> <span class="o">=</span> <span class="mi">1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">numerals</span><span class="p">[</span><span class="s">'L'</span><span class="p">]</span> <span class="o">=</span> <span class="mi">50</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">numerals</span>
<span class="go">{'I': 1, 'X': 10, 'L': 50, 'V': 5}</span>
</pre></div>

<p>Notice that <tt class="docutils literal">'L'</tt> was not added to the end of the output above.  Dictionaries
are unordered collections of key-value pairs.  When we print a dictionary, the
keys and values are rendered in some order, but as users of the language we
cannot predict what that order will be.</p>
<p>Dictionaries can appear in environment diagrams as well.</p>
<div class="example" data-output="False" data-step="-1" id="example_34" style="">
numerals = {'I': 1, 'V': 5, 'X': 10}
numerals['L'] = 50
</div>
<script type="text/javascript">
var example_34_trace = {"trace": [{"line": 1, "event": "step_line", "globals": {}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": [], "heap": {}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"numerals": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["numerals"], "heap": {"1": ["DICT", ["V", 5], ["I", 1], ["X", 10]]}, "stdout": ""}, {"line": 2, "event": "return", "globals": {"numerals": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["numerals"], "heap": {"1": ["DICT", ["L", 50], ["V", 5], ["I", 1], ["X", 10]]}, "stdout": ""}], "code": "numerals = {'I': 1, 'V': 5, 'X': 10}\nnumerals['L'] = 50"}</script><p>The dictionary abstraction also supports various methods of iterating of the
contents of the dictionary as a whole.  The methods <tt class="docutils literal">keys</tt>, <tt class="docutils literal">values</tt>, and
<tt class="docutils literal">items</tt> all return iterable values.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="nb">sum</span><span class="p">(</span><span class="n">numerals</span><span class="o">.</span><span class="n">values</span><span class="p">())</span>
<span class="go">66</span>
</pre></div>

<p>A list of key-value pairs can be converted into a dictionary by calling the
<tt class="docutils literal">dict</tt> constructor function.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="nb">dict</span><span class="p">([(</span><span class="mi">3</span><span class="p">,</span> <span class="mi">9</span><span class="p">),</span> <span class="p">(</span><span class="mi">4</span><span class="p">,</span> <span class="mi">16</span><span class="p">),</span> <span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">25</span><span class="p">)])</span>
<span class="go">{3: 9, 4: 16, 5: 25}</span>
</pre></div>

<p>Dictionaries do have some restrictions:</p>
<ul class="simple">
<li>A key of a dictionary cannot be an object of a mutable built-in type.</li>
<li>There can be at most one value for a given key.</li>
</ul>
<p>This first restriction is tied to the underlying implementation of dictionaries
in Python. The details of this implementation are not a topic of this text.
Intuitively, consider that the key tells Python where to find that key-value
pair in memory; if the key changes, the location of the pair may be lost.</p>
<p>The second restriction is a consequence of the dictionary abstraction, which is
designed to store and retrieve values for keys.  We can only retrieve <em>the</em>
value for a key if at most one such value exists in the dictionary.</p>
<p>A useful method implemented by dictionaries is <tt class="docutils literal">get</tt>, which returns either
the value for a key, if the key is present, or a default value.  The arguments
to <tt class="docutils literal">get</tt> are the key and the default value.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">numerals</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s">'A'</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
<span class="go">0</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">numerals</span><span class="o">.</span><span class="n">get</span><span class="p">(</span><span class="s">'V'</span><span class="p">,</span> <span class="mi">0</span><span class="p">)</span>
<span class="go">5</span>
</pre></div>

<p>Dictionaries also have a comprehension syntax analogous to those of lists and
generator expressions.  Evaluating a dictionary comprehension yields a new
dictionary object.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="p">{</span><span class="n">x</span><span class="p">:</span> <span class="n">x</span><span class="o">*</span><span class="n">x</span> <span class="k">for</span> <span class="n">x</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">3</span><span class="p">,</span><span class="mi">6</span><span class="p">)}</span>
<span class="go">{3: 9, 4: 16, 5: 25}</span>
</pre></div>

</div>
<div class="section" id="local-state">
<h3>2.4.3   Local State</h3>
<p>Lists and dictionaries have <em>local state</em>: they are changing values that have
some particular contents at any point in the execution of a program. The word
state implies an evolving process in which that state may change.</p>
<p>Functions can also have local state.  For instance, let us define a function
that models the process of withdrawing money from a bank account. We will
create a function called <tt class="docutils literal">withdraw</tt>, which takes as its argument an amount to
be withdrawn. If there is enough money in the account to accommodate the
withdrawal, then <tt class="docutils literal">withdraw</tt> will return the balance remaining after the
withdrawal. Otherwise, <tt class="docutils literal">withdraw</tt> will return the message <tt class="docutils literal">'Insufficient
funds'</tt>.  For example, if we begin with $100 in the account, we would like to
obtain the following sequence of return values by calling withdraw:</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">withdraw</span><span class="p">(</span><span class="mi">25</span><span class="p">)</span>
<span class="go">75</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">withdraw</span><span class="p">(</span><span class="mi">25</span><span class="p">)</span>
<span class="go">50</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">withdraw</span><span class="p">(</span><span class="mi">60</span><span class="p">)</span>
<span class="go">'Insufficient funds'</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">withdraw</span><span class="p">(</span><span class="mi">15</span><span class="p">)</span>
<span class="go">35</span>
</pre></div>

<p>Above, the expression <tt class="docutils literal">withdraw(25)</tt>, evaluated twice, yields different
values. Thus, this user-defined function is non-pure. Calling the function not
only returns a value, but also has the side effect of changing the function in
some way, so that the next call with the same argument will return a different
result.  This side effect is a result of <tt class="docutils literal">withdraw</tt> making a change to a
name-value binding outside of its local environment frame.</p>
<p>For <tt class="docutils literal">withdraw</tt> to make sense, it must be created with an initial account
balance. The function <tt class="docutils literal">make_withdraw</tt> is a higher-order function that takes a
starting balance as an argument.  The function <tt class="docutils literal">withdraw</tt> is its return value.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">withdraw</span> <span class="o">=</span> <span class="n">make_withdraw</span><span class="p">(</span><span class="mi">100</span><span class="p">)</span>
</pre></div>

<p>An implementation of <tt class="docutils literal">make_withdraw</tt> requires a new kind of statement: a
<tt class="docutils literal">nonlocal</tt> statement.  When we call <tt class="docutils literal">make_withdraw</tt>, we bind the name
<tt class="docutils literal">balance</tt> to the initial amount.  We then define and return a local function,
<tt class="docutils literal">withdraw</tt>, which updates and returns the value of <tt class="docutils literal">balance</tt> when called.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">make_withdraw</span><span class="p">(</span><span class="n">balance</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""Return a withdraw function that draws down balance with each call."""</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">withdraw</span><span class="p">(</span><span class="n">amount</span><span class="p">):</span>
<span class="gp">    </span>        <span class="k">nonlocal</span> <span class="n">balance</span>                 <span class="c"># Declare the name "balance" nonlocal</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="n">amount</span> <span class="o">&gt;</span> <span class="n">balance</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="s">'Insufficient funds'</span>
<span class="gp">    </span>        <span class="n">balance</span> <span class="o">=</span> <span class="n">balance</span> <span class="o">-</span> <span class="n">amount</span>       <span class="c"># Re-bind the existing balance name</span>
<span class="gp">    </span>        <span class="k">return</span> <span class="n">balance</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">withdraw</span>
</pre></div>

<p>The novel part of this implementation is the <tt class="docutils literal">nonlocal</tt> statement, which
mandates that whenever we change the binding of the name <tt class="docutils literal">balance</tt>, the
binding is changed in the first frame in which <tt class="docutils literal">balance</tt> is already bound.
Recall that without the <tt class="docutils literal">nonlocal</tt> statement, an assignment statement would
always bind a name in the first frame of the environment.  The <tt class="docutils literal">nonlocal</tt>
statement indicates that the name appears somewhere in the environment other
than the first (local) frame or the last (global) frame.</p>
<p>The following environment diagrams illustrate the effects of multiple calls to
a function created by <tt class="docutils literal">make_withdraw</tt>.</p>
<div class="example" data-output="False" data-step="5" id="example_35" style="">
def make_withdraw(balance):
    def withdraw(amount):
        nonlocal balance
        if amount &gt; balance:
            return 'Insufficient funds'
        balance = balance - amount
        return balance
    return withdraw

wd = make_withdraw(20)
wd(5)
wd(3)
</div>
<script type="text/javascript">
var example_35_trace = {"trace": [{"line": 1, "event": "step_line", "globals": {}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": [], "heap": {}, "stdout": ""}, {"line": 10, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f1", "is_zombie": false, "encoded_locals": {"balance": 20}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f1", "is_zombie": false, "encoded_locals": {"balance": 20}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 8, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p", "is_zombie": false, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance", "withdraw"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 8, "event": "return", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p", "is_zombie": false, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 11, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 2, "event": "call", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 6, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 12, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 2, "event": "call", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 3}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 3}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 6, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 3}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 3}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 3, "__return__": 12}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 12, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 3, "__return__": 12}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}], "code": "def make_withdraw(balance):\n    def withdraw(amount):\n        nonlocal balance\n        if amount > balance:\n            return 'Insufficient funds'\n        balance = balance - amount\n        return balance\n    return withdraw\n\nwd = make_withdraw(20)\nwd(5)\nwd(3)"}</script><p>The first def statement has the usual effect: it creates a new user-defined
function and binds the name <tt class="docutils literal">make_withdraw</tt> to that function in the global
frame. The subsequent call to <tt class="docutils literal">make_withdraw</tt> creates and returns a locally
defined function <tt class="docutils literal">withdraw</tt>.  The name <tt class="docutils literal">balance</tt> is bound in the parent
frame of this function.  Crucially, there will only be this single binding for
the name <tt class="docutils literal">balance</tt> throughout the rest of this example.</p>
<p>Next, we evaluate an expression that calls this function, bound to the name
<tt class="docutils literal">wd</tt>, on an amount 5.  The body of <tt class="docutils literal">withdraw</tt> is executed in a new
environment that extends the environment in which <tt class="docutils literal">withdraw</tt> was defined.
Tracing the effect of evaluating <tt class="docutils literal">withdraw</tt> illustrates the effect of a
<tt class="docutils literal">nonlocal</tt> statement in Python: a name outside of the first local frame can
be changed by an assignment statement.</p>
<div class="example" data-output="False" data-step="10" id="example_36" style="">
def make_withdraw(balance):
    def withdraw(amount):
        nonlocal balance
        if amount &gt; balance:
            return 'Insufficient funds'
        balance = balance - amount
        return balance
    return withdraw

wd = make_withdraw(20)
wd(5)
wd(3)
</div>
<script type="text/javascript">
var example_36_trace = {"trace": [{"line": 1, "event": "step_line", "globals": {}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": [], "heap": {}, "stdout": ""}, {"line": 10, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f1", "is_zombie": false, "encoded_locals": {"balance": 20}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f1", "is_zombie": false, "encoded_locals": {"balance": 20}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 8, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p", "is_zombie": false, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance", "withdraw"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 8, "event": "return", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p", "is_zombie": false, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 11, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 2, "event": "call", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 6, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 12, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 2, "event": "call", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 3}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 3}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 6, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 3}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 3}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 3, "__return__": 12}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 12, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 3, "__return__": 12}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}], "code": "def make_withdraw(balance):\n    def withdraw(amount):\n        nonlocal balance\n        if amount > balance:\n            return 'Insufficient funds'\n        balance = balance - amount\n        return balance\n    return withdraw\n\nwd = make_withdraw(20)\nwd(5)\nwd(3)"}</script><p>The <tt class="docutils literal">nonlocal</tt> statement changes all of the remaining assignment statements
in the definition of <tt class="docutils literal">withdraw</tt>.  After executing <tt class="docutils literal">nonlocal balance</tt>, any
assignment statement with <tt class="docutils literal">balance</tt> on the left-hand side of <tt class="docutils literal">=</tt> will not
bind <tt class="docutils literal">balance</tt> in the first frame of the current environment.  Instead, it
will find the first frame in which <tt class="docutils literal">balance</tt> was already defined and re-bind
the name in that frame.  If <tt class="docutils literal">balance</tt> has not previously been bound to a
value, then the <tt class="docutils literal">nonlocal</tt> statement will give an error.</p>
<p>By virtue of changing the binding for <tt class="docutils literal">balance</tt>, we have changed the
<tt class="docutils literal">withdraw</tt> function as well.  The next time it is called, the name
<tt class="docutils literal">balance</tt> will evaluate to 15 instead of 20.  Hence, when we call
<tt class="docutils literal">withdraw</tt> a second time, we see that its return value is 12 and not
17.  The change to <tt class="docutils literal">balance</tt> from the first call affects the result of
the second call.</p>
<div class="example" data-output="False" data-step="-1" id="example_37" style="">
def make_withdraw(balance):
    def withdraw(amount):
        nonlocal balance
        if amount &gt; balance:
            return 'Insufficient funds'
        balance = balance - amount
        return balance
    return withdraw

wd = make_withdraw(20)
wd(5)
wd(3)
</div>
<script type="text/javascript">
var example_37_trace = {"trace": [{"line": 1, "event": "step_line", "globals": {}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": [], "heap": {}, "stdout": ""}, {"line": 10, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f1", "is_zombie": false, "encoded_locals": {"balance": 20}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f1", "is_zombie": false, "encoded_locals": {"balance": 20}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 8, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p", "is_zombie": false, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance", "withdraw"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 8, "event": "return", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p", "is_zombie": false, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 11, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 2, "event": "call", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 6, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 12, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 2, "event": "call", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 3}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 3}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 6, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 15, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 3}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 3}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 3, "__return__": 12}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 12, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 15}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 3, "__return__": 12}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}], "code": "def make_withdraw(balance):\n    def withdraw(amount):\n        nonlocal balance\n        if amount > balance:\n            return 'Insufficient funds'\n        balance = balance - amount\n        return balance\n    return withdraw\n\nwd = make_withdraw(20)\nwd(5)\nwd(3)"}</script><p>The second call to <tt class="docutils literal">withdraw</tt> does create a second local frame, as usual.
However, both <tt class="docutils literal">withdraw</tt> frames have the same parent.  That is, they both
extend the environment for <tt class="docutils literal">make_withdraw</tt>, which contains the binding for
<tt class="docutils literal">balance</tt>.  Hence, they share that particular name binding.  Calling
<tt class="docutils literal">withdraw</tt> has the side effect of altering the environment that will be
extended by future calls to <tt class="docutils literal">withdraw</tt>.  The <tt class="docutils literal">nonlocal</tt> statement allows
<tt class="docutils literal">withdraw</tt> to change a name binding in the <tt class="docutils literal">make_withdraw</tt> frame.</p>
<p>Ever since we first encountered nested <tt class="docutils literal">def</tt> statements, we have observed
that a locally defined function can look up names outside of its local frames.
No <tt class="docutils literal">nonlocal</tt> statement is required to <em>access</em> a non-local name.  By
contrast, only after a <tt class="docutils literal">nonlocal</tt> statement can a function <em>change</em> the
binding of names in these frames.</p>
<p><strong>Practical Guidance.</strong> By introducing <tt class="docutils literal">nonlocal</tt> statements, we have created
a dual role for assignment statements.  Either they change local bindings, or
they change nonlocal bindings.  In fact, assignment statements already had a
dual role: they either created new bindings or re-bound existing names.
Assignment can also change the contents of lists and dictionaries. The
many roles of Python assignment can obscure the effects of executing an
assignment statement.  It is up to you as a programmer to document your code
clearly so that the effects of assignment can be understood by others.</p>
<p><strong>Python Particulars.</strong> This pattern of non-local assignment is a general
feature of programming languages with higher-order functions and lexical scope.
Most other languages do not require a <tt class="docutils literal">nonlocal</tt> statement at all. Instead,
non-local assignment is often the default behavior of assignment statements.</p>
<p>Python also has an unusual restriction regarding the lookup of names: within
the body of a function, all instances of a name must refer to the same frame.
As a result, Python cannot look up the value of a name in a non-local frame,
then bind that same name in the local frame, because the same name would be
accessed in two different frames in the same function.  This restriction
allows Python to pre-compute which frame contains each name before executing
the body of a function. When this restriction is violated, a confusing error
message results.  To demonstrate, the <tt class="docutils literal">make_withdraw</tt> example is repeated
below with the <tt class="docutils literal">nonlocal</tt> statement removed.</p>
<div class="example" data-output="False" data-step="7" id="example_38" style="">
def make_withdraw(balance):
    def withdraw(amount):
        if amount &gt; balance:
            return 'Insufficient funds'
        balance = balance - amount
        return balance
    return withdraw

wd = make_withdraw(20)
wd(5)
</div>
<script type="text/javascript">
var example_38_trace = {"trace": [{"line": 1, "event": "step_line", "globals": {}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": [], "heap": {}, "stdout": ""}, {"line": 9, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f1", "is_zombie": false, "encoded_locals": {"balance": 20}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f1", "is_zombie": false, "encoded_locals": {"balance": 20}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p", "is_zombie": false, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance", "withdraw"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p", "is_zombie": false, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 10, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 2, "event": "call", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 3, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 3, "event": "exception", "exception_msg": "UnboundLocalError: local variable 'balance' referenced before assignment", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 3, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 5, "__return__": null}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 10, "event": "exception", "exception_msg": "UnboundLocalError: local variable 'balance' referenced before assignment", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}], "code": "def make_withdraw(balance):\n    def withdraw(amount):\n        if amount > balance:\n            return 'Insufficient funds'\n        balance = balance - amount\n        return balance\n    return withdraw\n\nwd = make_withdraw(20)\nwd(5)"}</script><p>This <tt class="docutils literal">UnboundLocalError</tt> appears because <tt class="docutils literal">balance</tt> is assigned locally in
line 5, and so Python assumes that all references to <tt class="docutils literal">balance</tt> must
appear in the local frame as well.  This error occurs <em>before</em> line 5 is
ever executed, implying that Python has considered line 5 in some way
before executing line 3. As we study interpreter design, we will see that
pre-computing facts about a function body before executing it is quite common.
In this case, Python's pre-processing restricted the frame in which <tt class="docutils literal">balance</tt>
could appear, and thus prevented the name from being found. Adding a
<tt class="docutils literal">nonlocal</tt> statement corrects this error. The <tt class="docutils literal">nonlocal</tt> statement did not
exist in Python 2.</p>
</div>
<div class="section" id="the-benefits-of-non-local-assignment">
<h3>2.4.4   The Benefits of Non-Local Assignment</h3>
<p>Non-local assignment is an important step on our path to viewing a program as a
collection of independent and autonomous <em>objects</em>, which interact with each
other but each manage their own internal state.</p>
<p>In particular, non-local assignment has given us the ability to maintain some
state that is local to a function, but evolves over successive calls to that
function.  The <tt class="docutils literal">balance</tt> associated with a particular withdraw function is
shared among all calls to that function. However, the binding for balance
associated with an instance of withdraw is inaccessible to the rest of the
program.  Only <tt class="docutils literal">wd</tt> is associated with the frame for <tt class="docutils literal">make_withdraw</tt> in
which it was defined.  If <tt class="docutils literal">make_withdraw</tt> is called again, then it will
create a separate frame with a separate binding for <tt class="docutils literal">balance</tt>.</p>
<p>We can extend our example to illustrate this point.  A second call to
<tt class="docutils literal">make_withdraw</tt> returns a second <tt class="docutils literal">withdraw</tt> function that has a different
parent. We bind this second function to the name <tt class="docutils literal">wd2</tt> in the global frame.</p>
<div class="example" data-output="False" data-step="9" id="example_39" style="">
def make_withdraw(balance):
    def withdraw(amount):
        nonlocal balance
        if amount &gt; balance:
            return 'Insufficient funds'
        balance = balance - amount
        return balance
    return withdraw

wd = make_withdraw(20)
wd2 = make_withdraw(7)
wd2(6)
wd(8)
</div>
<script type="text/javascript">
var example_39_trace = {"trace": [{"line": 1, "event": "step_line", "globals": {}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": [], "heap": {}, "stdout": ""}, {"line": 10, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f1", "is_zombie": false, "encoded_locals": {"balance": 20}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f1", "is_zombie": false, "encoded_locals": {"balance": 20}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 8, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p", "is_zombie": false, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance", "withdraw"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 8, "event": "return", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p", "is_zombie": false, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 11, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f2", "is_zombie": false, "encoded_locals": {"balance": 7}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f2", "is_zombie": false, "encoded_locals": {"balance": 7}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 8, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p", "is_zombie": false, "encoded_locals": {"balance": 7, "withdraw": ["REF", 3]}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["balance", "withdraw"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 8, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p", "is_zombie": false, "encoded_locals": {"balance": 7, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 12, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 7, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 2, "event": "call", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 7, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 6}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 7, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 6}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 6, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 7, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 6}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 6}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 6, "__return__": 1}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 13, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 6, "__return__": 1}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 2, "event": "call", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 6, "__return__": 1}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"amount": 8}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 6, "__return__": 1}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"amount": 8}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 6, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 6, "__return__": 1}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"amount": 8}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 6, "__return__": 1}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"amount": 8}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 6, "__return__": 1}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"amount": 8, "__return__": 12}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["amount", "__return__"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 13, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 6, "__return__": 1}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4_z", "is_zombie": true, "encoded_locals": {"amount": 8, "__return__": 12}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["amount", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}], "code": "def make_withdraw(balance):\n    def withdraw(amount):\n        nonlocal balance\n        if amount > balance:\n            return 'Insufficient funds'\n        balance = balance - amount\n        return balance\n    return withdraw\n\nwd = make_withdraw(20)\nwd2 = make_withdraw(7)\nwd2(6)\nwd(8)"}</script><p>Now, we see that there are in fact two bindings for the name <tt class="docutils literal">balance</tt> in two
different frames, and each <tt class="docutils literal">withdraw</tt> function has a different parent. The
name <tt class="docutils literal">wd</tt> is bound to a function with a balance of 20, while
<tt class="docutils literal">wd2</tt> is bound to a different function with a balance of 7.</p>
<p>Calling <tt class="docutils literal">wd2</tt> changes the binding of its non-local <tt class="docutils literal">balance</tt> name, but
does not affect the function bound to the name <tt class="docutils literal">withdraw</tt>. A future call to
<tt class="docutils literal">wd</tt> is unaffected by the changing balance of <tt class="docutils literal">wd2</tt>; its balance is still
20.</p>
<div class="example" data-output="False" data-step="14" id="example_40" style="">
def make_withdraw(balance):
    def withdraw(amount):
        nonlocal balance
        if amount &gt; balance:
            return 'Insufficient funds'
        balance = balance - amount
        return balance
    return withdraw

wd = make_withdraw(20)
wd2 = make_withdraw(7)
wd2(6)
wd(8)
</div>
<script type="text/javascript">
var example_40_trace = {"trace": [{"line": 1, "event": "step_line", "globals": {}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": [], "heap": {}, "stdout": ""}, {"line": 10, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f1", "is_zombie": false, "encoded_locals": {"balance": 20}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f1", "is_zombie": false, "encoded_locals": {"balance": 20}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 8, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p", "is_zombie": false, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance", "withdraw"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 8, "event": "return", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p", "is_zombie": false, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 11, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f2", "is_zombie": false, "encoded_locals": {"balance": 7}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f2", "is_zombie": false, "encoded_locals": {"balance": 7}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 8, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p", "is_zombie": false, "encoded_locals": {"balance": 7, "withdraw": ["REF", 3]}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["balance", "withdraw"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 8, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p", "is_zombie": false, "encoded_locals": {"balance": 7, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 12, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 7, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 2, "event": "call", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 7, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 6}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 7, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 6}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 6, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 7, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 6}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 6}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 6, "__return__": 1}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 13, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 6, "__return__": 1}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 2, "event": "call", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 6, "__return__": 1}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"amount": 8}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 6, "__return__": 1}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"amount": 8}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 6, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 20, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 6, "__return__": 1}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"amount": 8}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 6, "__return__": 1}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"amount": 8}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 6, "__return__": 1}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"amount": 8, "__return__": 12}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["amount", "__return__"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}, {"line": 13, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f2_p_z", "is_zombie": true, "encoded_locals": {"balance": 1, "withdraw": ["REF", 3], "__return__": ["REF", 3]}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [2], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 6, "__return__": 1}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4_z", "is_zombie": true, "encoded_locals": {"amount": 8, "__return__": 12}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["amount", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1], "3": ["FUNCTION", "withdraw(amount)", 2]}, "stdout": ""}], "code": "def make_withdraw(balance):\n    def withdraw(amount):\n        nonlocal balance\n        if amount > balance:\n            return 'Insufficient funds'\n        balance = balance - amount\n        return balance\n    return withdraw\n\nwd = make_withdraw(20)\nwd2 = make_withdraw(7)\nwd2(6)\nwd(8)"}</script><p>In this way, each instance of <tt class="docutils literal">withdraw</tt> maintains its own balance state,
but that state is inaccessible to any other function in the program.  Viewing
this situation at a higher level, we have created an abstraction of a bank
account that manages its own internals but behaves in a way that models accounts
in the world: it changes over time based on its own history of withdrawal
requests.</p>
</div>
<div class="section" id="the-cost-of-non-local-assignment">
<h3>2.4.5   The Cost of Non-Local Assignment</h3>
<p>Our environment model of computation cleanly extends to explain the effects of
non-local assignment.  However, non-local assignment introduces some important
nuances in the way we think about names and values.</p>
<p>Previously, our values did not change; only our names and bindings changed.
When two names <tt class="docutils literal">a</tt> and <tt class="docutils literal">b</tt> were both bound to the value 4, it did not
matter whether they were bound to the same 4 or different 4's.  As far
as we could tell, there was only one 4 object that never changed.</p>
<p>However, functions with state do not behave this way.  When two names <tt class="docutils literal">wd</tt>
and <tt class="docutils literal">wd2</tt> are both bound to a <tt class="docutils literal">withdraw</tt> function, it <em>does</em> matter whether
they are bound to the same function or different instances of that function.
Consider the following example, which contrasts the one we just analyzed.
In this case, calling the function named by <tt class="docutils literal">wd2</tt> did change the value of the
function named by <tt class="docutils literal">wd</tt>, because both names refer to the same function.</p>
<div class="example" data-output="False" data-step="-1" id="example_41" style="">
def make_withdraw(balance):
    def withdraw(amount):
        nonlocal balance
        if amount &gt; balance:
            return 'Insufficient funds'
        balance = balance - amount
        return balance
    return withdraw

wd = make_withdraw(12)
wd2 = wd
wd2(1)
wd(1)
</div>
<script type="text/javascript">
var example_41_trace = {"trace": [{"line": 1, "event": "step_line", "globals": {}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": [], "heap": {}, "stdout": ""}, {"line": 10, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f1", "is_zombie": false, "encoded_locals": {"balance": 12}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": false, "unique_hash": "make_withdraw_f1", "is_zombie": false, "encoded_locals": {"balance": 12}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null]}, "stdout": ""}, {"line": 8, "event": "step_line", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p", "is_zombie": false, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance", "withdraw"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 8, "event": "return", "globals": {"make_withdraw": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p", "is_zombie": false, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "make_withdraw", "ordered_globals": ["make_withdraw"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 11, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 12, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 2, "event": "call", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 1}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 1}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 6, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 12, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 1}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 11, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 1}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 11, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2", "is_zombie": false, "encoded_locals": {"amount": 1, "__return__": 11}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 13, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 11, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 1, "__return__": 11}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 2, "event": "call", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 11, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 1, "__return__": 11}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 1}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 11, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 1, "__return__": 11}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 1}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 6, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 11, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 1, "__return__": 11}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 1}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 10, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 1, "__return__": 11}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 1}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 10, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 1, "__return__": 11}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3", "is_zombie": false, "encoded_locals": {"amount": 1, "__return__": 10}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}], "func_name": "withdraw", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 13, "event": "return", "globals": {"make_withdraw": ["REF", 1], "wd": ["REF", 2], "wd2": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "make_withdraw", "is_parent": true, "unique_hash": "make_withdraw_f1_p_z", "is_zombie": true, "encoded_locals": {"balance": 10, "withdraw": ["REF", 2], "__return__": ["REF", 2]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["balance", "withdraw", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f2_z", "is_zombie": true, "encoded_locals": {"amount": 1, "__return__": 11}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f3_z", "is_zombie": true, "encoded_locals": {"amount": 1, "__return__": 10}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}], "func_name": "<module>", "ordered_globals": ["make_withdraw", "wd", "wd2"], "heap": {"1": ["FUNCTION", "make_withdraw(balance)", null], "2": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}], "code": "def make_withdraw(balance):\n    def withdraw(amount):\n        nonlocal balance\n        if amount > balance:\n            return 'Insufficient funds'\n        balance = balance - amount\n        return balance\n    return withdraw\n\nwd = make_withdraw(12)\nwd2 = wd\nwd2(1)\nwd(1)"}</script><p>It is not unusual for two names to co-refer to the same value in the world, and
so it is in our programs.  But, as values change over time, we must be very
careful to understand the effect of a change on other names that might refer to
those values.</p>
<p>The key to correctly analyzing code with non-local assignment is to remember
that only function calls can introduce new frames.  Assignment statements always
change bindings in existing frames.  In this case, unless <tt class="docutils literal">make_withdraw</tt> is
called twice, there can be only one binding for <tt class="docutils literal">balance</tt>.</p>
<p><strong>Sameness and change.</strong> These subtleties arise because, by introducing non-pure
functions that change the non-local environment, we have changed the nature of
expressions.  An expression that contains only pure function calls is
<em>referentially transparent</em>; its value does not change if we substitute one of
its subexpression with the value of that subexpression.</p>
<p>Re-binding operations violate the conditions of referential transparency because
they do more than return a value; they change the environment.  When we
introduce arbitrary re-binding, we encounter a thorny epistemological issue:
what it means for two values to be the same. In our environment model of
computation, two separately defined functions are not the same, because changes
to one may not be reflected in the other.</p>
<p>In general, so long as we never modify data objects, we can regard a compound
data object to be precisely the totality of its pieces. For example, a rational
number is determined by giving its numerator and its denominator. But this view
is no longer valid in the presence of change, where a compound data object has
an "identity" that is something different from the pieces of which it is
composed. A bank account is still "the same" bank account even if we change the
balance by making a withdrawal; conversely, we could have two bank accounts that
happen to have the same balance, but are different objects.</p>
<p>Despite the complications it introduces, non-local assignment is a powerful tool
for creating modular programs. Different parts of a program, which correspond to
different environment frames, can evolve separately throughout program
execution.  Moreover, using functions with local state, we are able to implement
mutable data types.  In fact, we can implement abstract data types that are
equivalent to the built-in <tt class="docutils literal">list</tt> and <tt class="docutils literal">dict</tt> types introduced above.</p>
</div>
<div class="section" id="implementing-lists-and-dictionaries">
<h3>2.4.6   Implementing Lists and Dictionaries</h3>
<p>Lists are sequences, like tuples.  The Python language does not give us access
to the implementation of lists, only to the sequence abstraction and the
mutation methods we have introduced in this section.  To overcome this
language-enforced abstraction barrier, we can develop a functional
implementation of lists, again using a recursive representation.  This section
also has a second purpose: to further our understanding of
dispatch functions.</p>
<p>We will implement a list as a function that has a recursive list as its local
state.  Lists need to have an identity, like any mutable value.  In particular,
we cannot use <tt class="docutils literal">None</tt> to represent an empty mutable list, because two empty
lists are not identical values (e.g., appending to one does not append to the
other), but <tt class="docutils literal">None is None</tt>.  On the other hand, two different functions that
each have <tt class="docutils literal">empty_rlist</tt> as their local state will suffice to distinguish two
empty lists.</p>
<p>Our mutable list is a dispatch function, just as our functional implementation
of a pair was a dispatch function.  It checks the input "message" against known
messages and takes an appropriate action for each different input.  Our mutable
list responds to five different messages.  The first two implement the behaviors
of the sequence abstraction.  The next two add or remove the first element of
the list.  The final message returns a string representation of the whole list
contents.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">mutable_rlist</span><span class="p">():</span>
<span class="gp">    </span>    <span class="sd">"""Return a functional implementation of a mutable recursive list."""</span>
<span class="gp">    </span>    <span class="n">contents</span> <span class="o">=</span> <span class="n">empty_rlist</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">dispatch</span><span class="p">(</span><span class="n">message</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="k">None</span><span class="p">):</span>
<span class="gp">    </span>        <span class="k">nonlocal</span> <span class="n">contents</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="n">message</span> <span class="o">==</span> <span class="s">'len'</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="n">len_rlist</span><span class="p">(</span><span class="n">contents</span><span class="p">)</span>
<span class="gp">    </span>        <span class="k">elif</span> <span class="n">message</span> <span class="o">==</span> <span class="s">'getitem'</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="n">getitem_rlist</span><span class="p">(</span><span class="n">contents</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
<span class="gp">    </span>        <span class="k">elif</span> <span class="n">message</span> <span class="o">==</span> <span class="s">'push_first'</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">contents</span> <span class="o">=</span> <span class="n">rlist</span><span class="p">(</span><span class="n">value</span><span class="p">,</span> <span class="n">contents</span><span class="p">)</span>
<span class="gp">    </span>        <span class="k">elif</span> <span class="n">message</span> <span class="o">==</span> <span class="s">'pop_first'</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">f</span> <span class="o">=</span> <span class="n">first</span><span class="p">(</span><span class="n">contents</span><span class="p">)</span>
<span class="gp">    </span>            <span class="n">contents</span> <span class="o">=</span> <span class="n">rest</span><span class="p">(</span><span class="n">contents</span><span class="p">)</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="n">f</span>
<span class="gp">    </span>        <span class="k">elif</span> <span class="n">message</span> <span class="o">==</span> <span class="s">'str'</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="nb">str</span><span class="p">(</span><span class="n">contents</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">dispatch</span>
</pre></div>

<p>We can also add a convenience function to construct a functionally implemented
recursive list from any built-in sequence, simply by adding each element in
reverse order.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">to_mutable_rlist</span><span class="p">(</span><span class="n">source</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""Return a functional list with the same contents as source."""</span>
<span class="gp">    </span>    <span class="n">s</span> <span class="o">=</span> <span class="n">mutable_rlist</span><span class="p">()</span>
<span class="gp">    </span>    <span class="k">for</span> <span class="n">element</span> <span class="ow">in</span> <span class="nb">reversed</span><span class="p">(</span><span class="n">source</span><span class="p">):</span>
<span class="gp">    </span>        <span class="n">s</span><span class="p">(</span><span class="s">'push_first'</span><span class="p">,</span> <span class="n">element</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">s</span>
</pre></div>

<p>In the definition above, the function <tt class="docutils literal">reversed</tt> takes and returns an iterable
value; it is another example of a function that processes sequences.</p>
<p>At this point, we can construct a functionally implemented lists.  Note that
the list itself is a function.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">s</span> <span class="o">=</span> <span class="n">to_mutable_rlist</span><span class="p">(</span><span class="n">suits</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="n">s</span><span class="p">)</span>
<span class="go">&lt;class 'function'&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span><span class="p">(</span><span class="s">'str'</span><span class="p">)</span>
<span class="go">"('heart', ('diamond', ('spade', ('club', None))))"</span>
</pre></div>

<p>In addition, we can pass messages to the list <tt class="docutils literal">s</tt> that change its contents,
for instance removing the first element.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">s</span><span class="p">(</span><span class="s">'pop_first'</span><span class="p">)</span>
<span class="go">'heart'</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">s</span><span class="p">(</span><span class="s">'str'</span><span class="p">)</span>
<span class="go">"('diamond', ('spade', ('club', None)))"</span>
</pre></div>

<p>In principle, the operations <tt class="docutils literal">push_first</tt> and <tt class="docutils literal">pop_first</tt> suffice to make
arbitrary changes to a list.  We can always empty out the list entirely and then
replace its old contents with the desired result.</p>
<p><strong>Message passing.</strong> Given some time, we could implement the many useful
mutation operations of Python lists, such as <tt class="docutils literal">extend</tt> and <tt class="docutils literal">insert</tt>.  We
would have a choice: we could implement them all as functions, which use the
existing messages <tt class="docutils literal">pop_first</tt> and <tt class="docutils literal">push_first</tt> to make all changes.
Alternatively, we could add additional <tt class="docutils literal">elif</tt> clauses to the body of
<tt class="docutils literal">dispatch</tt>, each checking for a message (e.g.,  <tt class="docutils literal">'extend'</tt>) and applying the
appropriate change to <tt class="docutils literal">contents</tt> directly.</p>
<p>This second approach, which encapsulates the logic for all operations on a data
value within one function that responds to different messages, is called message
passing.  A program that uses message passing defines dispatch functions, each
of which may have local state, and organizes computation by passing "messages"
as the first argument to those functions.  The messages are strings that
correspond to particular behaviors.</p>
<p>One could imagine that enumerating all of these messages by name in the body of
<tt class="docutils literal">dispatch</tt> would become tedious and prone to error.  Python dictionaries,
introduced in the next section, provide a data type that will help us manage the
mapping between messages and operations.</p>
<p><strong>Implementing Dictionaries.</strong> We can implement an abstract data type that
conforms to the dictionary abstraction as a list of records, each of which is a
two-element list consisting of a key and the associated value.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">dictionary</span><span class="p">():</span>
<span class="gp">    </span>    <span class="sd">"""Return a functional implementation of a dictionary."""</span>
<span class="gp">    </span>    <span class="n">records</span> <span class="o">=</span> <span class="p">[]</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">getitem</span><span class="p">(</span><span class="n">key</span><span class="p">):</span>
<span class="gp">    </span>        <span class="k">for</span> <span class="n">k</span><span class="p">,</span> <span class="n">v</span> <span class="ow">in</span> <span class="n">records</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">if</span> <span class="n">k</span> <span class="o">==</span> <span class="n">key</span><span class="p">:</span>
<span class="gp">    </span>                <span class="k">return</span> <span class="n">v</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">setitem</span><span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
<span class="gp">    </span>        <span class="k">for</span> <span class="n">item</span> <span class="ow">in</span> <span class="n">records</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">if</span> <span class="n">item</span><span class="p">[</span><span class="mi">0</span><span class="p">]</span> <span class="o">==</span> <span class="n">key</span><span class="p">:</span>
<span class="gp">    </span>                <span class="n">item</span><span class="p">[</span><span class="mi">1</span><span class="p">]</span> <span class="o">=</span> <span class="n">value</span>
<span class="gp">    </span>                <span class="k">return</span>
<span class="gp">    </span>        <span class="n">records</span><span class="o">.</span><span class="n">append</span><span class="p">([</span><span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">])</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">dispatch</span><span class="p">(</span><span class="n">message</span><span class="p">,</span> <span class="n">key</span><span class="o">=</span><span class="k">None</span><span class="p">,</span> <span class="n">value</span><span class="o">=</span><span class="k">None</span><span class="p">):</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="n">message</span> <span class="o">==</span> <span class="s">'getitem'</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="n">getitem</span><span class="p">(</span><span class="n">key</span><span class="p">)</span>
<span class="gp">    </span>        <span class="k">elif</span> <span class="n">message</span> <span class="o">==</span> <span class="s">'setitem'</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">setitem</span><span class="p">(</span><span class="n">key</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
<span class="gp">    </span>        <span class="k">elif</span> <span class="n">message</span> <span class="o">==</span> <span class="s">'keys'</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">k</span> <span class="k">for</span> <span class="n">k</span><span class="p">,</span> <span class="n">_</span> <span class="ow">in</span> <span class="n">records</span><span class="p">)</span>
<span class="gp">    </span>        <span class="k">elif</span> <span class="n">message</span> <span class="o">==</span> <span class="s">'values'</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="nb">tuple</span><span class="p">(</span><span class="n">v</span> <span class="k">for</span> <span class="n">_</span><span class="p">,</span> <span class="n">v</span> <span class="ow">in</span> <span class="n">records</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">dispatch</span>
</pre></div>

<p>Again, we use the message passing method to organize our implementation.  We
have supported four messages: <tt class="docutils literal">getitem</tt>, <tt class="docutils literal">setitem</tt>, <tt class="docutils literal">keys</tt>, and
<tt class="docutils literal">values</tt>.  To look up a value for a key, we iterate through the records to
find a matching key.  To insert a value for a key, we iterate through the
records to see if there is already a record with that key. If not, we form a new
record. If there already is a record with this key, we set the value of the
record to the designated new value.</p>
<p>We can now use our implementation to store and retrieve values.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">d</span> <span class="o">=</span> <span class="n">dictionary</span><span class="p">()</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">d</span><span class="p">(</span><span class="s">'setitem'</span><span class="p">,</span> <span class="mi">3</span><span class="p">,</span> <span class="mi">9</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">d</span><span class="p">(</span><span class="s">'setitem'</span><span class="p">,</span> <span class="mi">4</span><span class="p">,</span> <span class="mi">16</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">d</span><span class="p">(</span><span class="s">'getitem'</span><span class="p">,</span> <span class="mi">3</span><span class="p">)</span>
<span class="go">9</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">d</span><span class="p">(</span><span class="s">'getitem'</span><span class="p">,</span> <span class="mi">4</span><span class="p">)</span>
<span class="go">16</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">d</span><span class="p">(</span><span class="s">'keys'</span><span class="p">)</span>
<span class="go">(3, 4)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">d</span><span class="p">(</span><span class="s">'values'</span><span class="p">)</span>
<span class="go">(9, 16)</span>
</pre></div>

<p>This implementation of a dictionary is <em>not</em> optimized for fast record lookup,
because each response to the message <tt class="docutils literal">'getitem'</tt> must iterate through the
entire list of <tt class="docutils literal">records</tt>. The built-in dictionary type is considerably more
efficient.</p>
</div>
<div class="section" id="dispatch-dictionaries">
<h3>2.4.7   Dispatch Dictionaries</h3>
<p>The dispatch function is a general method for implementing a message passing
interface for an abstract data type. To implement message dispatch, we have
thus far used a large conditional statement to look up function values using
message strings.</p>
<p>The built-in dictionary data type provides a general method for looking up a
value for a key. Instead of using dispatch functions to implement abstract data
types, we can use dictionaries with string keys.</p>
<p>The mutable <tt class="docutils literal">account</tt> data type below is implemented as a dictionary.  It
has a constructor <tt class="docutils literal">account</tt> and selector <tt class="docutils literal">check_balance</tt>, as well as
functions to <tt class="docutils literal">deposit</tt> or <tt class="docutils literal">withdraw</tt> funds. Moreover, the local state of
the account is stored in the dictionary alongside the functions that implement
its behavior.</p>
<div class="example" data-output="False" data-step="12" id="example_42" style="">
def account(initial_balance):
    def deposit(amount):
        dispatch['balance'] += amount
        return dispatch['balance']
    def withdraw(amount):
        if amount &gt; dispatch['balance']:
            return 'Insufficient funds'
        dispatch['balance'] -= amount
        return dispatch['balance']
    dispatch = {'deposit':   deposit,
                'withdraw':  withdraw,
                'balance':   initial_balance}
    return dispatch

def withdraw(account, amount):
    return account['withdraw'](amount)
def deposit(account, amount):
    return account['deposit'](amount)
def check_balance(account):
    return account['balance']

a = account(20)
deposit(a, 5)
withdraw(a, 17)
check_balance(a)
</div>
<script type="text/javascript">
var example_42_trace = {"trace": [{"line": 1, "event": "step_line", "globals": {}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": [], "heap": {}, "stdout": ""}, {"line": 15, "event": "step_line", "globals": {"account": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["account"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null]}, "stdout": ""}, {"line": 17, "event": "step_line", "globals": {"account": ["REF", 1], "withdraw": ["REF", 2]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["account", "withdraw"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null]}, "stdout": ""}, {"line": 19, "event": "step_line", "globals": {"account": ["REF", 1], "withdraw": ["REF", 2], "deposit": ["REF", 3]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["account", "withdraw", "deposit"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null]}, "stdout": ""}, {"line": 22, "event": "step_line", "globals": {"withdraw": ["REF", 2], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"withdraw": ["REF", 2], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": false, "unique_hash": "account_f1", "is_zombie": false, "encoded_locals": {"initial_balance": 20}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["initial_balance"]}], "func_name": "account", "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"withdraw": ["REF", 2], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": false, "unique_hash": "account_f1", "is_zombie": false, "encoded_locals": {"initial_balance": 20}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["initial_balance"]}], "func_name": "account", "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null]}, "stdout": ""}, {"line": 5, "event": "step_line", "globals": {"withdraw": ["REF", 2], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p", "is_zombie": false, "encoded_locals": {"initial_balance": 20, "deposit": ["REF", 5]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit"]}], "func_name": "account", "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1]}, "stdout": ""}, {"line": 10, "event": "step_line", "globals": {"withdraw": ["REF", 2], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p", "is_zombie": false, "encoded_locals": {"initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw"]}], "func_name": "account", "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 11, "event": "step_line", "globals": {"withdraw": ["REF", 2], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p", "is_zombie": false, "encoded_locals": {"initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw"]}], "func_name": "account", "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 12, "event": "step_line", "globals": {"withdraw": ["REF", 2], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p", "is_zombie": false, "encoded_locals": {"initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw"]}], "func_name": "account", "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1]}, "stdout": ""}, {"line": 13, "event": "step_line", "globals": {"withdraw": ["REF", 2], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p", "is_zombie": false, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch"]}], "func_name": "account", "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 20], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 13, "event": "return", "globals": {"withdraw": ["REF", 2], "account": ["REF", 1], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p", "is_zombie": false, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}], "func_name": "account", "ordered_globals": ["account", "withdraw", "deposit", "check_balance"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 20], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 23, "event": "step_line", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}], "func_name": "<module>", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 20], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 17, "event": "call", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2", "is_zombie": false, "encoded_locals": {"account": ["REF", 7], "amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["account", "amount"]}], "func_name": "deposit", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 20], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 18, "event": "step_line", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2", "is_zombie": false, "encoded_locals": {"account": ["REF", 7], "amount": 5}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["account", "amount"]}], "func_name": "deposit", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 20], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 2, "event": "call", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2", "is_zombie": false, "encoded_locals": {"account": ["REF", 7], "amount": 5}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "deposit", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 20], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 3, "event": "step_line", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2", "is_zombie": false, "encoded_locals": {"account": ["REF", 7], "amount": 5}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "deposit", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 20], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2", "is_zombie": false, "encoded_locals": {"account": ["REF", 7], "amount": 5}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3", "is_zombie": false, "encoded_locals": {"amount": 5}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount"]}], "func_name": "deposit", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 4, "event": "return", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2", "is_zombie": false, "encoded_locals": {"account": ["REF", 7], "amount": 5}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3", "is_zombie": false, "encoded_locals": {"amount": 5, "__return__": 25}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}], "func_name": "deposit", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 18, "event": "return", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2", "is_zombie": false, "encoded_locals": {"account": ["REF", 7], "amount": 5, "__return__": 25}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}], "func_name": "deposit", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 24, "event": "step_line", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}], "func_name": "<module>", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 15, "event": "call", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"account": ["REF", 7], "amount": 17}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["account", "amount"]}], "func_name": "withdraw", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 16, "event": "step_line", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"account": ["REF", 7], "amount": 17}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["account", "amount"]}], "func_name": "withdraw", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 5, "event": "call", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"account": ["REF", 7], "amount": 17}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["account", "amount"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f5", "is_zombie": false, "encoded_locals": {"amount": 17}, "is_highlighted": true, "frame_id": 5, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 6, "event": "step_line", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"account": ["REF", 7], "amount": 17}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["account", "amount"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f5", "is_zombie": false, "encoded_locals": {"amount": 17}, "is_highlighted": true, "frame_id": 5, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 8, "event": "step_line", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"account": ["REF", 7], "amount": 17}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["account", "amount"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f5", "is_zombie": false, "encoded_locals": {"amount": 17}, "is_highlighted": true, "frame_id": 5, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 25], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 9, "event": "step_line", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"account": ["REF", 7], "amount": 17}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["account", "amount"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f5", "is_zombie": false, "encoded_locals": {"amount": 17}, "is_highlighted": true, "frame_id": 5, "ordered_varnames": ["amount"]}], "func_name": "withdraw", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 8], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 9, "event": "return", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"account": ["REF", 7], "amount": 17}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["account", "amount"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f5", "is_zombie": false, "encoded_locals": {"amount": 17, "__return__": 8}, "is_highlighted": true, "frame_id": 5, "ordered_varnames": ["amount", "__return__"]}], "func_name": "withdraw", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 8], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 16, "event": "return", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4", "is_zombie": false, "encoded_locals": {"account": ["REF", 7], "amount": 17, "__return__": 8}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f5_z", "is_zombie": true, "encoded_locals": {"amount": 17, "__return__": 8}, "is_highlighted": false, "frame_id": 5, "ordered_varnames": ["amount", "__return__"]}], "func_name": "withdraw", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 8], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 25, "event": "step_line", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 17, "__return__": 8}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f5_z", "is_zombie": true, "encoded_locals": {"amount": 17, "__return__": 8}, "is_highlighted": false, "frame_id": 5, "ordered_varnames": ["amount", "__return__"]}], "func_name": "<module>", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 8], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 19, "event": "call", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 17, "__return__": 8}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f5_z", "is_zombie": true, "encoded_locals": {"amount": 17, "__return__": 8}, "is_highlighted": false, "frame_id": 5, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "check_balance", "is_parent": false, "unique_hash": "check_balance_f6", "is_zombie": false, "encoded_locals": {"account": ["REF", 7]}, "is_highlighted": true, "frame_id": 6, "ordered_varnames": ["account"]}], "func_name": "check_balance", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 8], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 20, "event": "step_line", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 17, "__return__": 8}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f5_z", "is_zombie": true, "encoded_locals": {"amount": 17, "__return__": 8}, "is_highlighted": false, "frame_id": 5, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "check_balance", "is_parent": false, "unique_hash": "check_balance_f6", "is_zombie": false, "encoded_locals": {"account": ["REF", 7]}, "is_highlighted": true, "frame_id": 6, "ordered_varnames": ["account"]}], "func_name": "check_balance", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 8], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 20, "event": "return", "globals": {"a": ["REF", 7], "account": ["REF", 1], "withdraw": ["REF", 2], "check_balance": ["REF", 4], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 17, "__return__": 8}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f5_z", "is_zombie": true, "encoded_locals": {"amount": 17, "__return__": 8}, "is_highlighted": false, "frame_id": 5, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "check_balance", "is_parent": false, "unique_hash": "check_balance_f6", "is_zombie": false, "encoded_locals": {"account": ["REF", 7], "__return__": 8}, "is_highlighted": true, "frame_id": 6, "ordered_varnames": ["account", "__return__"]}], "func_name": "check_balance", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 8], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}, {"line": 25, "event": "return", "globals": {"check_balance": ["REF", 4], "withdraw": ["REF", 2], "account": ["REF", 1], "a": ["REF", 7], "deposit": ["REF", 3]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "account", "is_parent": true, "unique_hash": "account_f1_p_z", "is_zombie": true, "encoded_locals": {"dispatch": ["REF", 7], "initial_balance": 20, "withdraw": ["REF", 6], "deposit": ["REF", 5], "__return__": ["REF", 7]}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["initial_balance", "deposit", "withdraw", "dispatch", "__return__"]}, {"parent_frame_id_list": [], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f2_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "deposit", "is_parent": false, "unique_hash": "deposit_f3_z", "is_zombie": true, "encoded_locals": {"amount": 5, "__return__": 25}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f4_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "amount": 17, "__return__": 8}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["account", "amount", "__return__"]}, {"parent_frame_id_list": [1], "func_name": "withdraw", "is_parent": false, "unique_hash": "withdraw_f5_z", "is_zombie": true, "encoded_locals": {"amount": 17, "__return__": 8}, "is_highlighted": false, "frame_id": 5, "ordered_varnames": ["amount", "__return__"]}, {"parent_frame_id_list": [], "func_name": "check_balance", "is_parent": false, "unique_hash": "check_balance_f6_z", "is_zombie": true, "encoded_locals": {"account": ["REF", 7], "__return__": 8}, "is_highlighted": false, "frame_id": 6, "ordered_varnames": ["account", "__return__"]}], "func_name": "<module>", "ordered_globals": ["account", "withdraw", "deposit", "check_balance", "a"], "heap": {"1": ["FUNCTION", "account(initial_balance)", null], "2": ["FUNCTION", "withdraw(account, amount)", null], "3": ["FUNCTION", "deposit(account, amount)", null], "4": ["FUNCTION", "check_balance(account)", null], "5": ["FUNCTION", "deposit(amount)", 1], "6": ["FUNCTION", "withdraw(amount)", 1], "7": ["DICT", ["balance", 8], ["withdraw", ["REF", 6]], ["deposit", ["REF", 5]]]}, "stdout": ""}], "code": "def account(initial_balance):\n    def deposit(amount):\n        dispatch['balance'] += amount\n        return dispatch['balance']\n    def withdraw(amount):\n        if amount > dispatch['balance']:\n            return 'Insufficient funds'\n        dispatch['balance'] -= amount\n        return dispatch['balance']\n    dispatch = {'deposit':   deposit,\n                'withdraw':  withdraw,\n                'balance':   initial_balance}\n    return dispatch\n\ndef withdraw(account, amount):\n    return account['withdraw'](amount)\ndef deposit(account, amount):\n    return account['deposit'](amount)\ndef check_balance(account):\n    return account['balance']\n\na = account(20)\ndeposit(a, 5)\nwithdraw(a, 17)\ncheck_balance(a)"}</script><p>The name <tt class="docutils literal">dispatch</tt> within the body of the <tt class="docutils literal">account</tt> constructor is bound
to a dictionary that contains the messages accepted by an account as keys. The
<em>balance</em> is a number, while the messages <em>deposit</em> and <em>withdraw</em> are bound to
functions. These functions have access to the <tt class="docutils literal">dispatch</tt> dictionary, and so
they can read and change the balance. By storing the balance in the dispatch
dictionary rather than in the <tt class="docutils literal">account</tt> frame directly, we avoid the need for
<tt class="docutils literal">nonlocal</tt> statements in <tt class="docutils literal">deposit</tt> and <tt class="docutils literal">withdraw</tt>.</p>
<p>The operators <tt class="docutils literal">+=</tt> and <tt class="docutils literal"><span class="pre">-=</span></tt> are shorthand in Python (and many other
languages) for combined lookup and re-assignment.  The last two lines below are
equivalent.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="mi">2</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">=</span> <span class="n">a</span> <span class="o">+</span> <span class="mi">1</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">a</span> <span class="o">+=</span> <span class="mi">1</span>
</pre></div>

</div>
<div class="section" id="propagating-constraints">
<h3>2.4.8   Propagating Constraints</h3>
<p>Mutable data allows us to simulate systems with change, but also allows us to
build new kinds of abstractions.  In this extended example, we combine nonlocal
assignment, lists, and dictionaries to build a <em>constraint-based system</em> that
supports computation in multiple directions. Expressing programs as constraints
is a type of <em>declarative programming</em>, in which a programmer declares the
structure of a problem to be solved, but abstracts away the details of exactly
how the solution to the problem is computed.</p>
<p>Computer programs are traditionally organized as one-directional computations,
which perform operations on pre-specified arguments to produce desired outputs.
On the other hand, we often want to model systems in terms of relations among
quantities. For example, we previously considered the ideal gas law, which
relates the pressure (<tt class="docutils literal">p</tt>), volume (<tt class="docutils literal">v</tt>), quantity (<tt class="docutils literal">n</tt>), and temperature
(<tt class="docutils literal">t</tt>) of an ideal gas via Boltzmann's constant (<tt class="docutils literal">k</tt>):</p>
<pre class="literal-block">
p * v = n * k * t
</pre>
<p>Such an equation is not one-directional. Given any four of the quantities, we
can use this equation to compute the fifth. Yet translating the equation into a
traditional computer language would force us to choose one of the quantities to
be computed in terms of the other four. Thus, a function for computing the
pressure could not be used to compute the temperature, even though the
computations of both quantities arise from the same equation.</p>
<p>In this section, we sketch the design of a general model of linear
relationships.  We define primitive constraints that hold between quantities,
such as an <tt class="docutils literal">adder(a, b, c)</tt> constraint that enforces the mathematical
relationship <tt class="docutils literal">a + b = c</tt>.</p>
<p>We also define a means of combination, so that primitive constraints can be
combined to express more complex relations.  In this way, our program resembles
a programming language.  We combine constraints by constructing a network in
which constraints are joined by connectors. A connector is an object that
"holds" a value and may participate in one or more constraints.</p>
<p>For example, we know that the relationship between Fahrenheit and Celsius
temperatures is:</p>
<pre class="literal-block">
9 * c = 5 * (f - 32)
</pre>
<p>This equation is a complex constraint between <tt class="docutils literal">c</tt> and <tt class="docutils literal">f</tt>. Such a constraint
can be thought of as a network consisting of primitive <tt class="docutils literal">adder</tt>,
<tt class="docutils literal">multiplier</tt>, and <tt class="docutils literal">constant</tt> constraints.</p>
<div class="figure">
<img alt="" src="../img/constraints.png"/>
</div>
<p>In this figure, we see on the left a multiplier box with three terminals,
labeled <tt class="docutils literal">a</tt>, <tt class="docutils literal">b</tt>, and <tt class="docutils literal">c</tt>. These connect the multiplier to the rest of the
network as follows: The <tt class="docutils literal">a</tt> terminal is linked to a connector <tt class="docutils literal">celsius</tt>, which
will hold the Celsius temperature. The <tt class="docutils literal">b</tt> terminal is linked to a connector
<tt class="docutils literal">w</tt>, which is also linked to a constant box that holds 9. The <tt class="docutils literal">c</tt>
terminal, which the multiplier box constrains to be the product of <tt class="docutils literal">a</tt> and
<tt class="docutils literal">b</tt>, is linked to the <tt class="docutils literal">c</tt> terminal of another multiplier box, whose <tt class="docutils literal">b</tt> is
connected to a constant 5 and whose <tt class="docutils literal">a</tt> is connected to one of the terms
in the sum constraint.</p>
<p>Computation by such a network proceeds as follows: When a connector is given a
value (by the user or by a constraint box to which it is linked), it awakens all
of its associated constraints (except for the constraint that just awakened it)
to inform them that it has a value. Each awakened constraint box then polls its
connectors to see if there is enough information to determine a value for a
connector. If so, the box sets that connector, which then awakens all of its
associated constraints, and so on. For instance, in conversion between Celsius
and Fahrenheit, <tt class="docutils literal">w</tt>, <tt class="docutils literal">x</tt>, and <tt class="docutils literal">y</tt> are immediately set by the constant
boxes to 9, 5, and 32, respectively. The connectors awaken the
multipliers and the adder, which determine that there is not enough information
to proceed. If the user (or some other part of the network) sets the <tt class="docutils literal">celsius</tt>
connector to a value (say 25), the leftmost multiplier will be awakened, and
it will set <tt class="docutils literal">u</tt> to <tt class="docutils literal">25 * 9 = 225</tt>.  Then <tt class="docutils literal">u</tt> awakens the second
multiplier, which sets <tt class="docutils literal">v</tt> to 45, and <tt class="docutils literal">v</tt> awakens the adder, which sets
the <tt class="docutils literal">fahrenheit</tt> connector to 77.</p>
<p><strong>Using the Constraint System.</strong> To use the constraint system to carry out the
temperature computation outlined above, we first create two named connectors,
<tt class="docutils literal">celsius</tt> and <tt class="docutils literal">fahrenheit</tt>, by calling the <tt class="docutils literal">connector</tt> constructor.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">celsius</span> <span class="o">=</span> <span class="n">connector</span><span class="p">(</span><span class="s">'Celsius'</span><span class="p">)</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">fahrenheit</span> <span class="o">=</span> <span class="n">connector</span><span class="p">(</span><span class="s">'Fahrenheit'</span><span class="p">)</span>
</pre></div>

<p>Then, we link these connectors into a network that mirrors the figure above.
The function <tt class="docutils literal">converter</tt> assembles the various connectors and constraints
in the network.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">converter</span><span class="p">(</span><span class="n">c</span><span class="p">,</span> <span class="n">f</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""Connect c to f with constraints to convert from Celsius to Fahrenheit."""</span>
<span class="gp">    </span>    <span class="n">u</span><span class="p">,</span> <span class="n">v</span><span class="p">,</span> <span class="n">w</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">y</span> <span class="o">=</span> <span class="p">[</span><span class="n">connector</span><span class="p">()</span> <span class="k">for</span> <span class="n">_</span> <span class="ow">in</span> <span class="nb">range</span><span class="p">(</span><span class="mi">5</span><span class="p">)]</span>
<span class="gp">    </span>    <span class="n">multiplier</span><span class="p">(</span><span class="n">c</span><span class="p">,</span> <span class="n">w</span><span class="p">,</span> <span class="n">u</span><span class="p">)</span>
<span class="gp">    </span>    <span class="n">multiplier</span><span class="p">(</span><span class="n">v</span><span class="p">,</span> <span class="n">x</span><span class="p">,</span> <span class="n">u</span><span class="p">)</span>
<span class="gp">    </span>    <span class="n">adder</span><span class="p">(</span><span class="n">v</span><span class="p">,</span> <span class="n">y</span><span class="p">,</span> <span class="n">f</span><span class="p">)</span>
<span class="gp">    </span>    <span class="n">constant</span><span class="p">(</span><span class="n">w</span><span class="p">,</span> <span class="mi">9</span><span class="p">)</span>
<span class="gp">    </span>    <span class="n">constant</span><span class="p">(</span><span class="n">x</span><span class="p">,</span> <span class="mi">5</span><span class="p">)</span>
<span class="gp">    </span>    <span class="n">constant</span><span class="p">(</span><span class="n">y</span><span class="p">,</span> <span class="mi">32</span><span class="p">)</span>
</pre></div>

<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">converter</span><span class="p">(</span><span class="n">celsius</span><span class="p">,</span> <span class="n">fahrenheit</span><span class="p">)</span>
</pre></div>

<p>We will use a message passing system to coordinate constraints and connectors.
Constraints are dictionaries that do not hold local states themselves. Their
responses to messages are non-pure functions that change the connectors that
they constrain.</p>
<p>Connectors are dictionaries that hold a current value and respond to messages
that manipulate that value.  Constraints will not change the value of connectors
directly, but instead will do so by sending messages, so that the connector can
notify other constraints in response to the change.  In this way, a connector
represents a number, but also encapsulates connector behavior.</p>
<p>One message we can send to a connector is to set its value.  Here, we (the
<tt class="docutils literal">'user'</tt>) set the value of <tt class="docutils literal">celsius</tt> to 25.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">celsius</span><span class="p">[</span><span class="s">'set_val'</span><span class="p">](</span><span class="s">'user'</span><span class="p">,</span> <span class="mi">25</span><span class="p">)</span>
<span class="go">Celsius = 25</span>
<span class="go">Fahrenheit = 77.0</span>
</pre></div>

<p>Not only does the value of <tt class="docutils literal">celsius</tt> change to 25, but its value
propagates through the network, and so the value of <tt class="docutils literal">fahrenheit</tt> is changed as
well.  These changes are printed because we named these two connectors when we
constructed them.</p>
<p>Now we can try to set <tt class="docutils literal">fahrenheit</tt> to a new value, say 212.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">fahrenheit</span><span class="p">[</span><span class="s">'set_val'</span><span class="p">](</span><span class="s">'user'</span><span class="p">,</span> <span class="mi">212</span><span class="p">)</span>
<span class="go">Contradiction detected: 77.0 vs 212</span>
</pre></div>

<p>The connector complains that it has sensed a contradiction: Its value is
77.0, and someone is trying to set it to 212. If we really want to reuse
the network with new values, we can tell <tt class="docutils literal">celsius</tt> to forget its old value:</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">celsius</span><span class="p">[</span><span class="s">'forget'</span><span class="p">](</span><span class="s">'user'</span><span class="p">)</span>
<span class="go">Celsius is forgotten</span>
<span class="go">Fahrenheit is forgotten</span>
</pre></div>

<p>The connector <tt class="docutils literal">celsius</tt> finds that the <tt class="docutils literal">user</tt>, who set its value originally,
is now retracting that value, so <tt class="docutils literal">celsius</tt> agrees to lose its value, and it
informs the rest of the network of this fact. This information eventually
propagates to <tt class="docutils literal">fahrenheit</tt>, which now finds that it has no reason for
continuing to believe that its own value is 77. Thus, it also gives up its
value.</p>
<p>Now that <tt class="docutils literal">fahrenheit</tt> has no value, we are free to set it to 212:</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">fahrenheit</span><span class="p">[</span><span class="s">'set_val'</span><span class="p">](</span><span class="s">'user'</span><span class="p">,</span> <span class="mi">212</span><span class="p">)</span>
<span class="go">Fahrenheit = 212</span>
<span class="go">Celsius = 100.0</span>
</pre></div>

<p>This new value, when propagated through the network, forces <tt class="docutils literal">celsius</tt> to have
a value of 100. We have used the very same network to compute <tt class="docutils literal">celsius</tt>
given <tt class="docutils literal">fahrenheit</tt> and to compute <tt class="docutils literal">fahrenheit</tt> given <tt class="docutils literal">celsius</tt>. This
non-directionality of computation is the distinguishing feature of
constraint-based systems.</p>
<p><strong>Implementing the Constraint System.</strong> As we have seen, connectors are
dictionaries that map message names to function and data values.  We will
implement connectors that respond to the following messages:</p>
<ul class="simple">
<li><tt class="docutils literal"><span class="pre">connector['set_val'](source,</span> value)</tt> indicates that the <tt class="docutils literal">source</tt> is
requesting the connector to set its current value to <tt class="docutils literal">value</tt>.</li>
<li><tt class="docutils literal"><span class="pre">connector['has_val']()</span></tt> returns whether the connector already has a value.</li>
<li><tt class="docutils literal"><span class="pre">connector['val']</span></tt> is the current value of the connector.</li>
<li><tt class="docutils literal"><span class="pre">connector['forget'](source)</span></tt> tells the connector that the <tt class="docutils literal">source</tt> is
requesting it to forget its value.</li>
<li><tt class="docutils literal"><span class="pre">connector['connect'](source)</span></tt> tells the connector to participate in a new
constraint, the <tt class="docutils literal">source</tt>.</li>
</ul>
<p>Constraints are also dictionaries, which receive information from connectors by
means of two messages:</p>
<ul class="simple">
<li><tt class="docutils literal"><span class="pre">constraint['new_val']()</span></tt> indicates that some connector that is connected to
the constraint has a new value.</li>
<li><tt class="docutils literal"><span class="pre">constraint['forget']()</span></tt> indicates that some connector that is connected to
the constraint has forgotten its value.</li>
</ul>
<p>When constraints receive these messages, they propagate them appropriately to
other connectors.</p>
<p>The <tt class="docutils literal">adder</tt> function constructs an adder constraint over three connectors,
where the first two must add to the third: <tt class="docutils literal">a + b = c</tt>.  To support
multidirectional constraint propagation, the adder must also specify that it
subtracts <tt class="docutils literal">a</tt> from <tt class="docutils literal">c</tt> to get <tt class="docutils literal">b</tt> and likewise subtracts <tt class="docutils literal">b</tt> from <tt class="docutils literal">c</tt>
to get <tt class="docutils literal">a</tt>.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">operator</span> <span class="k">import</span> <span class="n">add</span><span class="p">,</span> <span class="n">sub</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">adder</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">c</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""The constraint that a + b = c."""</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">make_ternary_constraint</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">c</span><span class="p">,</span> <span class="n">add</span><span class="p">,</span> <span class="n">sub</span><span class="p">,</span> <span class="n">sub</span><span class="p">)</span>
</pre></div>

<p>We would like to implement a generic ternary (three-way) constraint, which
uses the three connectors and three functions from <tt class="docutils literal">adder</tt> to create a
constraint that accepts <tt class="docutils literal">new_val</tt> and <tt class="docutils literal">forget</tt> messages.  The response to
messages are local functions, which are placed in a dictionary called
<tt class="docutils literal">constraint</tt>.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">make_ternary_constraint</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">c</span><span class="p">,</span> <span class="n">ab</span><span class="p">,</span> <span class="n">ca</span><span class="p">,</span> <span class="n">cb</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""The constraint that ab(a,b)=c and ca(c,a)=b and cb(c,b) = a."""</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">new_value</span><span class="p">():</span>
<span class="gp">    </span>        <span class="n">av</span><span class="p">,</span> <span class="n">bv</span><span class="p">,</span> <span class="n">cv</span> <span class="o">=</span> <span class="p">[</span><span class="n">connector</span><span class="p">[</span><span class="s">'has_val'</span><span class="p">]()</span> <span class="k">for</span> <span class="n">connector</span> <span class="ow">in</span> <span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">c</span><span class="p">)]</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="n">av</span> <span class="ow">and</span> <span class="n">bv</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">c</span><span class="p">[</span><span class="s">'set_val'</span><span class="p">](</span><span class="n">constraint</span><span class="p">,</span> <span class="n">ab</span><span class="p">(</span><span class="n">a</span><span class="p">[</span><span class="s">'val'</span><span class="p">],</span> <span class="n">b</span><span class="p">[</span><span class="s">'val'</span><span class="p">]))</span>
<span class="gp">    </span>        <span class="k">elif</span> <span class="n">av</span> <span class="ow">and</span> <span class="n">cv</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">b</span><span class="p">[</span><span class="s">'set_val'</span><span class="p">](</span><span class="n">constraint</span><span class="p">,</span> <span class="n">ca</span><span class="p">(</span><span class="n">c</span><span class="p">[</span><span class="s">'val'</span><span class="p">],</span> <span class="n">a</span><span class="p">[</span><span class="s">'val'</span><span class="p">]))</span>
<span class="gp">    </span>        <span class="k">elif</span> <span class="n">bv</span> <span class="ow">and</span> <span class="n">cv</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">a</span><span class="p">[</span><span class="s">'set_val'</span><span class="p">](</span><span class="n">constraint</span><span class="p">,</span> <span class="n">cb</span><span class="p">(</span><span class="n">c</span><span class="p">[</span><span class="s">'val'</span><span class="p">],</span> <span class="n">b</span><span class="p">[</span><span class="s">'val'</span><span class="p">]))</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">forget_value</span><span class="p">():</span>
<span class="gp">    </span>        <span class="k">for</span> <span class="n">connector</span> <span class="ow">in</span> <span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">c</span><span class="p">):</span>
<span class="gp">    </span>            <span class="n">connector</span><span class="p">[</span><span class="s">'forget'</span><span class="p">](</span><span class="n">constraint</span><span class="p">)</span>
<span class="gp">    </span>    <span class="n">constraint</span> <span class="o">=</span> <span class="p">{</span><span class="s">'new_val'</span><span class="p">:</span> <span class="n">new_value</span><span class="p">,</span> <span class="s">'forget'</span><span class="p">:</span> <span class="n">forget_value</span><span class="p">}</span>
<span class="gp">    </span>    <span class="k">for</span> <span class="n">connector</span> <span class="ow">in</span> <span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">c</span><span class="p">):</span>
<span class="gp">    </span>        <span class="n">connector</span><span class="p">[</span><span class="s">'connect'</span><span class="p">](</span><span class="n">constraint</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">constraint</span>
</pre></div>

<p>The dictionary called <tt class="docutils literal">constraint</tt> is a dispatch dictionary, but also the
constraint object itself. It responds to the two messages that constraints
receive, but is also passed as the <tt class="docutils literal">source</tt> argument in calls to its
connectors.</p>
<p>The constraint's local function <tt class="docutils literal">new_value</tt> is called whenever the constraint
is informed that one of its connectors has a value. This function first checks
to see if both <tt class="docutils literal">a</tt> and <tt class="docutils literal">b</tt> have values. If so, it tells <tt class="docutils literal">c</tt> to set its
value to the return value of function <tt class="docutils literal">ab</tt>, which is <tt class="docutils literal">add</tt> in the case of an
<tt class="docutils literal">adder</tt>. The constraint passes <em>itself</em> (<tt class="docutils literal">constraint</tt>) as the <tt class="docutils literal">source</tt>
argument of the connector, which is the adder object. If <tt class="docutils literal">a</tt> and <tt class="docutils literal">b</tt> do not
both have values, then the constraint checks <tt class="docutils literal">a</tt> and <tt class="docutils literal">c</tt>, and so on.</p>
<p>If the constraint is informed that one of its connectors has forgotten its
value, it requests that all of its connectors now forget their values. (Only
those values that were set by this constraint are actually lost.)</p>
<p>A <tt class="docutils literal">multiplier</tt> is very similar to an <tt class="docutils literal">adder</tt>.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">operator</span> <span class="k">import</span> <span class="n">mul</span><span class="p">,</span> <span class="n">truediv</span>
<span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">multiplier</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">c</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""The constraint that a * b = c."""</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">make_ternary_constraint</span><span class="p">(</span><span class="n">a</span><span class="p">,</span> <span class="n">b</span><span class="p">,</span> <span class="n">c</span><span class="p">,</span> <span class="n">mul</span><span class="p">,</span> <span class="n">truediv</span><span class="p">,</span> <span class="n">truediv</span><span class="p">)</span>
</pre></div>

<p>A constant is a constraint as well, but one that is never sent any messages,
because it involves only a single connector that it sets on construction.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">constant</span><span class="p">(</span><span class="n">connector</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""The constraint that connector = value."""</span>
<span class="gp">    </span>    <span class="n">constraint</span> <span class="o">=</span> <span class="p">{}</span>
<span class="gp">    </span>    <span class="n">connector</span><span class="p">[</span><span class="s">'set_val'</span><span class="p">](</span><span class="n">constraint</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">constraint</span>
</pre></div>

<p>These three constraints are sufficient to implement our temperature conversion
network.</p>
<p><strong>Representing connectors.</strong> A connector is represented as a dictionary that
contains a value, but also has response functions with local state.  The
connector must track the <tt class="docutils literal">informant</tt> that gave it its current value, and a
list of <tt class="docutils literal">constraints</tt> in which it participates.</p>
<p>The constructor <tt class="docutils literal">connector</tt> has local functions for setting and
forgetting values, which are the responses to messages from constraints.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">connector</span><span class="p">(</span><span class="n">name</span><span class="o">=</span><span class="k">None</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""A connector between constraints."""</span>
<span class="gp">    </span>    <span class="n">informant</span> <span class="o">=</span> <span class="k">None</span>
<span class="gp">    </span>    <span class="n">constraints</span> <span class="o">=</span> <span class="p">[]</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">set_value</span><span class="p">(</span><span class="n">source</span><span class="p">,</span> <span class="n">value</span><span class="p">):</span>
<span class="gp">    </span>        <span class="k">nonlocal</span> <span class="n">informant</span>
<span class="gp">    </span>        <span class="n">val</span> <span class="o">=</span> <span class="n">connector</span><span class="p">[</span><span class="s">'val'</span><span class="p">]</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="n">val</span> <span class="ow">is</span> <span class="k">None</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">informant</span><span class="p">,</span> <span class="n">connector</span><span class="p">[</span><span class="s">'val'</span><span class="p">]</span> <span class="o">=</span> <span class="n">source</span><span class="p">,</span> <span class="n">value</span>
<span class="gp">    </span>            <span class="k">if</span> <span class="n">name</span> <span class="ow">is</span> <span class="ow">not</span> <span class="k">None</span><span class="p">:</span>
<span class="gp">    </span>                <span class="nb">print</span><span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="s">'='</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
<span class="gp">    </span>            <span class="n">inform_all_except</span><span class="p">(</span><span class="n">source</span><span class="p">,</span> <span class="s">'new_val'</span><span class="p">,</span> <span class="n">constraints</span><span class="p">)</span>
<span class="gp">    </span>        <span class="k">else</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">if</span> <span class="n">val</span> <span class="o">!=</span> <span class="n">value</span><span class="p">:</span>
<span class="gp">    </span>                <span class="nb">print</span><span class="p">(</span><span class="s">'Contradiction detected:'</span><span class="p">,</span> <span class="n">val</span><span class="p">,</span> <span class="s">'vs'</span><span class="p">,</span> <span class="n">value</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">def</span> <span class="nf">forget_value</span><span class="p">(</span><span class="n">source</span><span class="p">):</span>
<span class="gp">    </span>        <span class="k">nonlocal</span> <span class="n">informant</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="n">informant</span> <span class="o">==</span> <span class="n">source</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">informant</span><span class="p">,</span> <span class="n">connector</span><span class="p">[</span><span class="s">'val'</span><span class="p">]</span> <span class="o">=</span> <span class="k">None</span><span class="p">,</span> <span class="k">None</span>
<span class="gp">    </span>            <span class="k">if</span> <span class="n">name</span> <span class="ow">is</span> <span class="ow">not</span> <span class="k">None</span><span class="p">:</span>
<span class="gp">    </span>                <span class="nb">print</span><span class="p">(</span><span class="n">name</span><span class="p">,</span> <span class="s">'is forgotten'</span><span class="p">)</span>
<span class="gp">    </span>            <span class="n">inform_all_except</span><span class="p">(</span><span class="n">source</span><span class="p">,</span> <span class="s">'forget'</span><span class="p">,</span> <span class="n">constraints</span><span class="p">)</span>
<span class="gp">    </span>    <span class="n">connector</span> <span class="o">=</span> <span class="p">{</span><span class="s">'val'</span><span class="p">:</span> <span class="k">None</span><span class="p">,</span>
<span class="gp">    </span>                 <span class="s">'set_val'</span><span class="p">:</span> <span class="n">set_value</span><span class="p">,</span>
<span class="gp">    </span>                 <span class="s">'forget'</span><span class="p">:</span> <span class="n">forget_value</span><span class="p">,</span>
<span class="gp">    </span>                 <span class="s">'has_val'</span><span class="p">:</span> <span class="k">lambda</span><span class="p">:</span> <span class="n">connector</span><span class="p">[</span><span class="s">'val'</span><span class="p">]</span> <span class="ow">is</span> <span class="ow">not</span> <span class="k">None</span><span class="p">,</span>
<span class="gp">    </span>                 <span class="s">'connect'</span><span class="p">:</span> <span class="k">lambda</span> <span class="n">source</span><span class="p">:</span> <span class="n">constraints</span><span class="o">.</span><span class="n">append</span><span class="p">(</span><span class="n">source</span><span class="p">)}</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">connector</span>
</pre></div>

<p>A connector is again a dispatch dictionary for the five messages used by
constraints to communicate with connectors. Four responses are functions, and
the final response is the value itself.</p>
<p>The local function <tt class="docutils literal">set_value</tt> is called when there is a request to set the
connector's value. If the connector does not currently have a value, it will set
its value and remember as <tt class="docutils literal">informant</tt> the source constraint that requested the
value to be set. Then the connector will notify all of its participating
constraints except the constraint that requested the value to be set. This is
accomplished using the following iterative function.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">inform_all_except</span><span class="p">(</span><span class="n">source</span><span class="p">,</span> <span class="n">message</span><span class="p">,</span> <span class="n">constraints</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""Inform all constraints of the message, except source."""</span>
<span class="gp">    </span>    <span class="k">for</span> <span class="n">c</span> <span class="ow">in</span> <span class="n">constraints</span><span class="p">:</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="n">c</span> <span class="o">!=</span> <span class="n">source</span><span class="p">:</span>
<span class="gp">    </span>            <span class="n">c</span><span class="p">[</span><span class="n">message</span><span class="p">]()</span>
</pre></div>

<p>If a connector is asked to forget its value, it calls the local function
<tt class="docutils literal"><span class="pre">forget-value</span></tt>, which first checks to make sure that the request is coming
from the same constraint that set the value originally. If so, the connector
informs its associated constraints about the loss of the value.</p>
<p>The response to the message <tt class="docutils literal">has_val</tt> indicates whether the connector has a
value.  The response to the message <tt class="docutils literal">connect</tt> adds the source constraint to
the list of constraints.</p>
<p>The constraint program we have designed introduces many ideas that will appear
again in object-oriented programming.  Constraints and connectors are both
abstractions that are manipulated through messages.  When the value of a
connector is changed, it is changed via a message that not only changes the
value, but validates it (checking the source) and propagates its effects
(informing other constraints).  In fact, we will use a similar architecture of
dictionaries with string-valued keys and functional values to implement an
object-oriented system later in this chapter.</p>
</div>
</div>
  <p><i>Continue</i>:
  	<a href="25-object-oriented-programming.html">
  		2.5 Object-Oriented Programming
  	</a>
      </div>
    </section>

    <div class="wrap">
      <footer id="contentinfo" class="body">
          Composing Programs by <a href="http://www.denero.org/">John
          DeNero</a>, based on the textbook <a
          href="http://mitpress.mit.edu/sicp/">Structure and
          Interpretation of Computer Programs</a> by Harold Abelson and
          Gerald Jay Sussman, is licensed under a <a rel="license"
          href="http://creativecommons.org/licenses/by-sa/3.0/">Creative
          Commons Attribution-ShareAlike 3.0 Unported License</a>.
      </footer><!-- /#contentinfo -->
    </div>
  </div>
</body>

<!-- Mirrored from www.composingprograms.com/versions/v1/pages/24-mutable-data.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:46:03 GMT -->
</html>
