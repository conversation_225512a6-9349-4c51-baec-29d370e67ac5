<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from www.composingprograms.com/versions/v1/pages/11-getting-started.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:45:24 GMT -->
<head>
  <title>1.1 Getting Started</title>
  <meta charset="utf-8" />

  <link rel="stylesheet" type="text/css" href="../theme/css/cp.css" />

  <!-- Stylesheets -->
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/pytutor.css"/>
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/ui-lightness/jquery-ui-1.8.21.custom.css" />
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/codemirror.css"  />

  <!-- jQuery -->
  <script type="text/javascript" src="../theme/tutor/js/jquery-1.8.2.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery-ui-1.8.24.custom.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery.ba-bbq.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery.jsPlumb-1.3.10-all-min.js"></script>

  <!-- codemirror.net online code editor -->
  <script type="text/javascript" src="../theme/tutor/js/codemirror/codemirror.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/codemirror/python.js"></script>

  <!-- d3 -->
  <script type="text/javascript" src="../theme/tutor/js/d3.v2.min.js"></script>

  <!-- Online Python Tutor -->
  <script type="text/javascript" src="../theme/tutor/js/pytutor.js"></script>
  <script type="text/javascript" src="../theme/js/tutorize.js"></script>


    <script src="http://cdn.mathjax.org/mathjax/latest/MathJax.js?config=TeX-AMS-MML_HTMLorMML" type= "text/javascript">
       MathJax.Hub.Config({
           config: ["MMLorHTML.js"],
           jax: ["input/TeX","input/MathML","output/HTML-CSS","output/NativeMML"],
           TeX: { extensions: ["AMSmath.js","AMSsymbols.html","noErrors.html","noUndefined.js"], equationNumbers: { autoNumber: "AMS" } },
           extensions: ["tex2jax.js","mml2jax.html","MathMenu.html","MathZoom.html","jsMath2jax.js"],
           tex2jax: {
               inlineMath: [ ['$','$'] ],
               displayMath: [ ['$$','$$'] ],
               processEscapes: true },
           "HTML-CSS": {
               styles: { ".MathJax .mo, .MathJax .mi": {color: "black ! important"}}
           }
       });
    </script>

</head>

<body id="index" class="home">
  <div class="container">

    <div class="nav-main">
      <div class="wrap">
        <a class="nav-home" href="../index.html">
          <span class="nav-logo">c<span class="nav-logo-compose">⚬</span>mp<span class="nav-logo-compose">⚬</span>sing pr<span class="nav-logo-compose">⚬</span>grams</span>
        </a>
VERSION 1, Replaced July 2014
      </div>
    </div>

    <section class="content wrap documentationContent">
      <div class="nav-docs">
	<h3>Chapter 1<a id="hide_contents">Hide contents</a> </h3>
		<div class="nav-docs-section">
			<h3><a href="11-getting-started.html">1.1 Getting Started</a></h3>
				<li><a href="11-getting-started.html#programming-in-python">1.1.1 Programming in Python</a>
				<li><a href="11-getting-started.html#installing-python-3">1.1.2 Installing Python 3</a>
				<li><a href="11-getting-started.html#interactive-sessions">1.1.3 Interactive Sessions</a>
				<li><a href="11-getting-started.html#first-example">1.1.4 First Example</a>
				<li><a href="11-getting-started.html#errors">1.1.5 Errors</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="12-elements-of-programming.html">1.2 Elements of Programming</a></h3>
				<li><a href="12-elements-of-programming.html#expressions">1.2.1 Expressions</a>
				<li><a href="12-elements-of-programming.html#call-expressions">1.2.2 Call Expressions</a>
				<li><a href="12-elements-of-programming.html#importing-library-functions">1.2.3 Importing Library Functions</a>
				<li><a href="12-elements-of-programming.html#names-and-the-environment">1.2.4 Names and the Environment</a>
				<li><a href="12-elements-of-programming.html#evaluating-nested-expressions">1.2.5 Evaluating Nested Expressions</a>
				<li><a href="12-elements-of-programming.html#the-non-pure-print-function">1.2.6 The Non-Pure Print Function</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="13-defining-new-functions.html">1.3 Defining New Functions</a></h3>
				<li><a href="13-defining-new-functions.html#environments">1.3.1 Environments</a>
				<li><a href="13-defining-new-functions.html#calling-user-defined-functions">1.3.2 Calling User-Defined Functions</a>
				<li><a href="13-defining-new-functions.html#example-calling-a-user-defined-function">1.3.3 Example: Calling a User-Defined Function</a>
				<li><a href="13-defining-new-functions.html#local-names">1.3.4 Local Names</a>
				<li><a href="13-defining-new-functions.html#choosing-names">1.3.5 Choosing Names</a>
				<li><a href="13-defining-new-functions.html#functions-as-abstractions">1.3.6 Functions as Abstractions</a>
				<li><a href="13-defining-new-functions.html#operators">1.3.7 Operators</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="14-designing-functions.html">1.4 Designing Functions</a></h3>
				<li><a href="14-designing-functions.html#documentation">1.4.1 Documentation</a>
				<li><a href="14-designing-functions.html#default-argument-values">1.4.2 Default Argument Values</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="15-control.html">1.5 Control</a></h3>
				<li><a href="15-control.html#statements">1.5.1 Statements</a>
				<li><a href="15-control.html#compound-statements">1.5.2 Compound Statements</a>
				<li><a href="15-control.html#defining-functions-ii-local-assignment">1.5.3 Defining Functions II: Local Assignment</a>
				<li><a href="15-control.html#conditional-statements">1.5.4 Conditional Statements</a>
				<li><a href="15-control.html#iteration">1.5.5 Iteration</a>
				<li><a href="15-control.html#testing">1.5.6 Testing</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="16-higher-order-functions.html">1.6 Higher-Order Functions</a></h3>
				<li><a href="16-higher-order-functions.html#functions-as-arguments">1.6.1 Functions as Arguments</a>
				<li><a href="16-higher-order-functions.html#functions-as-general-methods">1.6.2 Functions as General Methods</a>
				<li><a href="16-higher-order-functions.html#defining-functions-iii-nested-definitions">1.6.3 Defining Functions III: Nested Definitions</a>
				<li><a href="16-higher-order-functions.html#functions-as-returned-values">1.6.4 Functions as Returned Values</a>
				<li><a href="16-higher-order-functions.html#example-newton-s-method">1.6.5 Example: Newton's Method</a>
				<li><a href="16-higher-order-functions.html#currying">1.6.6 Currying</a>
				<li><a href="16-higher-order-functions.html#lambda-expressions">1.6.7 Lambda Expressions</a>
				<li><a href="16-higher-order-functions.html#abstractions-and-first-class-functions">1.6.8 Abstractions and First-Class Functions</a>
				<li><a href="16-higher-order-functions.html#function-decorators">1.6.9 Function Decorators</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="17-recursive-functions.html">1.7 Recursive Functions</a></h3>
				<li><a href="17-recursive-functions.html#the-anatomy-of-recursive-functions">1.7.1 The Anatomy of Recursive Functions</a>
				<li><a href="17-recursive-functions.html#mutual-recursion">1.7.2 Mutual Recursion</a>
				<li><a href="17-recursive-functions.html#printing-in-recursive-functions">1.7.3 Printing in Recursive Functions</a>
				<li><a href="17-recursive-functions.html#tree-recursion">1.7.4 Tree Recursion</a>
				<li><a href="17-recursive-functions.html#example-partitions">1.7.5 Example: Partitions</a>
		</div>
      </div>

      <div class="inner-content">
	<h1>Chapter 1: Building Abstractions with Functions</h1>
  <div class="section" id="getting-started">
<h2>1.1   Getting Started</h2>
<p>Computer science is a tremendously broad academic discipline. The areas of
globally distributed systems, artificial intelligence, robotics, graphics,
security, scientific computing, computer architecture, and dozens of emerging
sub-fields all expand with new techniques and discoveries every year. The
rapid progress of computer science has left few aspects of human life
unaffected. Commerce, communication, science, art, leisure, and politics have
all been reinvented as computational domains.</p>
<p>The high productivity of computer science is only possible because the
discipline is built upon an elegant and powerful set of fundamental ideas. All
computing begins with representing information, specifying logic to process it,
and designing abstractions that manage the complexity of that logic. Mastering
these fundamentals will require us to understand precisely how computers
interpret computer programs and carry out computational processes.</p>
<p>These fundamental ideas have long been taught using the classic textbook
<em>Structure and Interpretation of Computer Programs</em> (<a class="reference external" href="http://mitpress.mit.edu/sicp">SICP</a>) by Harold
Abelson and Gerald Jay Sussman with Julie Sussman. This text borrows
heavily from that textbook, which the original authors have kindly licensed for
adaptation and reuse. These notes are also in the public domain, released under
the <a class="reference external" href="http://creativecommons.org/licenses/by-nc-sa/3.0/deed.en_US">Creative Commons attribution non-commericial share-alike license version 3</a>.</p>
<div class="section" id="programming-in-python">
<h3>1.1.1   Programming in Python</h3>
<blockquote class="epigraph">
<p>A language isn't something you learn so much as something you join.</p>
<p class="attribution">—<a class="reference external" href="http://arikaokrent.com/">Arika Okrent</a></p>
</blockquote>
<p>In order to define computational processes, we need a programming language;
preferably one that many humans and a great variety of computers can all
understand.  In this text, we will work primarily with the <a class="reference external" href="http://docs.python.org/py3k/">Python</a> language.</p>
<p>Python is a widely used programming language that has recruited enthusiasts from
many professions: web programmers, game engineers, scientists, academics, and
even designers of new programming languages. When you learn Python, you join a
million-person-strong community of developers. Developer communities are
tremendously important institutions: members help each other solve problems,
share their projects and experiences, and collectively develop software and
tools.  Dedicated members often achieve celebrity and widespread esteem for
their contributions.</p>
<p>The Python language itself is the product of a <a class="reference external" href="http://www.python.org/psf/members/">large volunteer community</a> that prides itself on the <a class="reference external" href="http://python.org/community/diversity/">diversity</a> of its contributors. The language
was conceived and first implemented by <a class="reference external" href="http://en.wikipedia.org/wiki/Guido_van_Rossum">Guido van Rossum</a> in the late 1980's.
The first chapter of his <a class="reference external" href="http://docs.python.org/py3k/tutorial/appetite.html">Python 3 Tutorial</a> explains why Python is so
popular, among the many languages available today.</p>
<p>Python excels as an instructional language because, throughout its history,
Python's developers have emphasized the human interpretability of Python code,
reinforced by the <a class="reference external" href="http://www.python.org/dev/peps/pep-0020/">Zen of Python</a> guiding principles of beauty, simplicity, and
readability. Python is particularly appropriate for this text because its broad
set of features support a variety of different programming styles, which we will
explore. While there is no single way to program in Python, there are a set of
conventions shared across the developer community that facilitate reading,
understanding, and extending existing programs. Python's combination of great
flexibility and accessibility allows students to explore many programming
paradigms, and then apply their newly acquired knowledge to thousands of
<a class="reference external" href="http://pypi.python.org/pypi">ongoing projects</a>.</p>
<p>These notes maintain the spirit of <a class="reference external" href="http://mitpress.mit.edu/sicp">SICP</a> by introducing the features of Python
in step with techniques for abstraction and a rigorous model of computation. In
addition, these notes provide a practical introduction to Python programming,
including some advanced language features and illustrative examples. Increasing
your facility Python should come naturally as you progress through the text.</p>
<p>The best way to get started programming in Python is to interact with the
interpreter directly. This section describes how to install Python 3, initiate
an interactive session with the interpreter, and start programming.</p>
</div>
<div class="section" id="installing-python-3">
<h3>1.1.2   Installing Python 3</h3>
<p>As with all great software, Python has many versions. This text will use the
most recent stable version of Python 3. Many computers have older versions of
Python installed already, such as Python 2.6, but those will not match the
descriptions in this text. You should be able to use any computer, but expect to
install Python 3. (Don't worry, Python is free.)</p>
<p>The free online book <a class="reference external" href="http://getpython3.com/diveintopython3">Dive Into Python 3</a> has detailed <a class="reference external" href="http://getpython3.com/diveintopython3/installing-python.html">installation
instructions</a> for all major platforms. These instructions mention Python 3.1
several times, but you're better off with the latest version of Python 3
(although the differences are insignificant for this text).</p>
<p>For further guidance, try these video tutorials on <a class="reference external" href="http://www.youtube.com/watch?v=54-wuFsPi0w">Windows installation</a> and
<a class="reference external" href="http://www.youtube.com/watch?v=smHuBHxJdK8">Mac installation</a> of Python 3, created by Julia Oh.</p>
</div>
<div class="section" id="interactive-sessions">
<h3>1.1.3   Interactive Sessions</h3>
<p>In an interactive Python session, you type some Python <em>code</em> after the
<em>prompt</em>, <tt class="docutils literal">&gt;&gt;&gt;</tt>. The Python <em>interpreter</em> reads and executes what you type,
carrying out your various commands.</p>
<p>To start an interactive session, run the Python 3 application. Type <tt class="docutils literal">python3</tt>
at a terminal prompt (Mac/Unix/Linux) or open the Python 3 application in
Windows.</p>
<p>If you see the Python prompt, <tt class="docutils literal">&gt;&gt;&gt;</tt>, then you have successfully started an
interactive session. These notes depict example interactions using the prompt,
followed by some input.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="mi">2</span> <span class="o">+</span> <span class="mi">2</span>
<span class="go">4</span>
</pre></div>

<p><strong>Interactive controls.</strong> Each session keeps a history of what you have typed.
To access that history, press <tt class="docutils literal"><span class="pre">&lt;Control&gt;-P</span></tt> (previous) and <tt class="docutils literal"><span class="pre">&lt;Control&gt;-N</span></tt>
(next).  <tt class="docutils literal"><span class="pre">&lt;Control&gt;-D</span></tt> exits a session, which discards this history. Up and
down arrows also cycle through history on some systems.</p>
</div>
<div class="section" id="first-example">
<h3>1.1.4   First Example</h3>
<blockquote class="epigraph">
<div class="line-block">
<div class="line">And, as imagination bodies forth</div>
<div class="line">The forms of things to unknown, and the poet's pen</div>
<div class="line">Turns them to shapes, and gives to airy nothing</div>
<div class="line">A local habitation and a name.</div>
</div>
<p class="attribution">—William Shakespeare, A Midsummer-Night's Dream</p>
</blockquote>
<p>To give Python a proper introduction, we will begin with an example that uses
several language features.  In the next section, we will start from scratch and
build up the language piece by piece. Think of this section as a sneak preview
of features to come.</p>
<p>Python has built-in support for a wide range of common programming activities,
such as manipulating text, displaying graphics, and communicating over the
Internet.  The line of Python code</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="kn">from</span> <span class="nn">urllib.request</span> <span class="k">import</span> <span class="n">urlopen</span>
</pre></div>

<p>is an <tt class="docutils literal">import</tt> statement that loads functionality for accessing data on the
Internet. In particular, it makes available a function called <tt class="docutils literal">urlopen</tt>, which
can access the content at a uniform resource locator (URL), a location of
something on the Internet.</p>
<p><strong>Statements &amp; Expressions</strong>. Python code consists of expressions and
statements. Broadly, computer programs consist of instructions to either</p>
<ol class="arabic simple">
<li>Compute some value</li>
<li>Carry out some action</li>
</ol>
<p>Statements typically describe actions. When the Python interpreter executes a
statement, it carries out the corresponding action. On the other hand,
expressions typically describe computations. When Python evaluates an
expression, it computes the value of that expression. This chapter introduces
several types of statements and expressions.</p>
<p>The assignment statement</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">shakespeare</span> <span class="o">=</span> <span class="n">urlopen</span><span class="p">(</span><span class="s">'http://composingprograms.com/shakespeare.txt'</span><span class="p">)</span>
</pre></div>

<p>associates the name <tt class="docutils literal">shakespeare</tt> with the value of the expression that
follows <tt class="docutils literal">=</tt>.  That expression applies the <tt class="docutils literal">urlopen</tt> function to a URL that
contains the complete text of William Shakespeare's 37 plays, all in a single
text document.</p>
<p><strong>Functions</strong>. Functions encapsulate logic that manipulates data. <tt class="docutils literal">urlopen</tt>
is a function.  A web address is a piece of data, and the text of Shakespeare's
plays is another. The process by which the former leads to the latter may be
complex, but we can apply that process using only a simple expression because
that complexity is tucked away within a function. Functions are the primary
topic of this chapter.</p>
<p>Another assignment statement</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">words</span> <span class="o">=</span> <span class="nb">set</span><span class="p">(</span><span class="n">shakespeare</span><span class="o">.</span><span class="n">read</span><span class="p">()</span><span class="o">.</span><span class="n">decode</span><span class="p">()</span><span class="o">.</span><span class="n">split</span><span class="p">())</span>
</pre></div>

<p>associates the name <tt class="docutils literal">words</tt> to the set of all unique words that appear in
Shakespeare's plays, all 33,721 of them.  The chain of commands to <tt class="docutils literal">read</tt>,
<tt class="docutils literal">decode</tt>, and <tt class="docutils literal">split</tt>, each operate on an intermediate computational entity:
we <tt class="docutils literal">read</tt> the data from the opened URL, then <tt class="docutils literal">decode</tt> the data into text,
and finally <tt class="docutils literal">split</tt> the text into words.  All of those words are placed in a
<tt class="docutils literal">set</tt>.</p>
<p><strong>Objects</strong>. A <tt class="docutils literal">set</tt> is a type of object, one that supports set operations
like computing intersections and membership. An object seamlessly bundles
together data and the logic that manipulates that data, in a way that manages
the complexity of both. Objects are the primary topic of Chapter 2.  Finally,
the expression</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="p">{</span><span class="n">w</span> <span class="k">for</span> <span class="n">w</span> <span class="ow">in</span> <span class="n">words</span> <span class="k">if</span> <span class="nb">len</span><span class="p">(</span><span class="n">w</span><span class="p">)</span> <span class="o">==</span> <span class="mi">6</span> <span class="ow">and</span> <span class="n">w</span><span class="p">[::</span><span class="o">-</span><span class="mi">1</span><span class="p">]</span> <span class="ow">in</span> <span class="n">words</span><span class="p">}</span>
<span class="go">{'redder', 'drawer', 'reward', 'diaper', 'repaid'}</span>
</pre></div>

<p>is a compound expression that evaluates to the set of all Shakespearian words
that are simultaneously a word spelled in reverse. The cryptic notation
<tt class="docutils literal"><span class="pre">w[::-1]</span></tt> enumerates each letter in a word, but the <tt class="docutils literal"><span class="pre">-1</span></tt> dictates to step
backwards.  When you enter an expression in an interactive session, Python
prints its value on the following line.</p>
<p><strong>Interpreters</strong>. Evaluating compound expressions requires a precise procedure
that interprets code in a predictable way. A program that implements such a
procedure, evaluating compound expressions, is called an interpreter. The
design and implementation of interpreters is the primary topic of Chapter 3.</p>
<p>When compared with other computer programs, interpreters for programming
languages are unique in their generality. Python was not designed with
Shakespeare in mind. However, its great flexibility allowed us to process a
large amount of text with only a few statements and expressions.</p>
<p>In the end, we will find that all of these core concepts are closely related:
functions are objects, objects are functions, and interpreters are instances of
both. However, developing a clear understanding of each of these concepts and
their role in organizing code is critical to mastering the art of programming.</p>
</div>
<div class="section" id="errors">
<h3>1.1.5   Errors</h3>
<p>Python is waiting for your command.  You are encouraged to experiment with the
language, even though you may not yet know its full vocabulary and structure.
However, be prepared for errors.  While computers are tremendously fast and
flexible, they are also extremely rigid. The nature of computers is described in
<a class="reference external" href="http://www.stanford.edu/class/cs101/code-introduction.html">Stanford's introductory course</a> as</p>
<blockquote class="epigraph">
<p>The fundamental equation of computers is:</p>
<p><tt class="docutils literal">computer = powerful + stupid</tt></p>
<p>Computers are very powerful, looking at volumes of data very quickly.
Computers can perform billions of operations per second, where each operation
is pretty simple.</p>
<p>Computers are also shockingly stupid and fragile. The operations that they can
do are extremely rigid, simple, and mechanical. The computer lacks anything
like real insight ... it's nothing like the HAL 9000 from the movies. If
nothing else, you should not be intimidated by the computer as if it's some
sort of brain. It's very mechanical underneath it all.</p>
<p>Programming is about a person using their real insight to build something
useful, constructed out of these teeny, simple little operations that the
computer can do.</p>
<p class="attribution">—Francisco Cai and Nick Parlante, Stanford CS101</p>
</blockquote>
<p>The rigidity of computers will immediately become apparent as you experiment
with the Python interpreter: even the smallest spelling and formatting changes
will cause unexpected output and errors.</p>
<p>Learning to interpret errors and diagnose the cause of unexpected errors is
called <em>debugging</em>. Some guiding principles of debugging are:</p>
<ol class="arabic simple">
<li><strong>Test incrementally</strong>: Every well-written program is composed of small,
modular components that can be tested individually. Try out everything you
write as soon as possible to identify problems early and gain confidence in
your components.</li>
<li><strong>Isolate errors</strong>: An error in the output of a statement can typically be
attributed to a particular modular component.  When trying to diagnose a
problem, trace the error to the smallest fragment of code you can before
trying to correct it.</li>
<li><strong>Check your assumptions</strong>: Interpreters do carry out your instructions to
the letter — no more and no less. Their output is unexpected when the
behavior of some code does not match what the programmer believes (or
assumes) that behavior to be. Know your assumptions, then focus your
debugging effort on verifying that your assumptions actually hold.</li>
<li><strong>Consult others</strong>: You are not alone!  If you don't understand an error
message, ask a friend, instructor, or search engine. If you have isolated an
error, but can't figure out how to correct it, ask someone else to take a
look. A lot of valuable programming knowledge is shared in the process of
group problem solving.</li>
</ol>
<p>Incremental testing, modular design, precise assumptions, and teamwork are
themes that persist throughout this text. Hopefully, they will also persist
throughout your computer science career.</p>
</div>
</div>
  <p><i>Continue</i>:
  	<a href="12-elements-of-programming.html">
  		1.2 Elements of Programming
  	</a>
      </div>
    </section>

    <div class="wrap">
      <footer id="contentinfo" class="body">
          Composing Programs by <a href="http://www.denero.org/">John
          DeNero</a>, based on the textbook <a
          href="http://mitpress.mit.edu/sicp/">Structure and
          Interpretation of Computer Programs</a> by Harold Abelson and
          Gerald Jay Sussman, is licensed under a <a rel="license"
          href="http://creativecommons.org/licenses/by-sa/3.0/">Creative
          Commons Attribution-ShareAlike 3.0 Unported License</a>.
      </footer><!-- /#contentinfo -->
    </div>
  </div>
</body>

<!-- Mirrored from www.composingprograms.com/versions/v1/pages/11-getting-started.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:45:46 GMT -->
</html>
