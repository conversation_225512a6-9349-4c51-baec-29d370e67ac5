<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from www.composingprograms.com/pages/21-introduction.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:44:59 GMT -->
<head>
  <title>2.1 Introduction</title>
  <meta charset="utf-8" />

  <link rel="stylesheet" type="text/css" href="../theme/css/cp.css" />

  <!-- Stylesheets -->
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/pytutor.css"/>
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/ui-lightness/jquery-ui-1.8.21.custom.css" />
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/codemirror.css"  />
  <link rel="stylesheet" type="text/css" href="../theme/coding-js/coding.css"  />

  <!-- jQuery -->
  <script type="text/javascript" src="../theme/tutor/js/jquery-1.8.2.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery-ui-1.8.24.custom.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery.ba-bbq.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery.jsPlumb-1.3.10-all-min.js"></script>

  <!-- codemirror.net online code editor -->
  <script type="text/javascript" src="../theme/tutor/js/codemirror/codemirror.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/codemirror/python.js"></script>

  <!-- d3 -->
  <script type="text/javascript" src="../theme/tutor/js/d3.v2.min.js"></script>

  <!-- Online Python Tutor -->
  <script type="text/javascript" src="../theme/tutor/js/pytutor.js"></script>

  <!-- Coding.js -->
  <script type="text/javascript" src="../theme/coding-js/coding.js"></script>
  <script> var $c = new CodingJS("../theme/coding-js/index.html"); </script>

  <!-- Composing Programs -->
  <script type="text/javascript" src="../theme/js/cp.js"></script>

  
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.1/MathJax.js?config=TeX-AMS-MML_HTMLorMML" type= "text/javascript">
       MathJax.Hub.Config({
           config: ["MMLorHTML.js"],
           jax: ["input/TeX","input/MathML","output/HTML-CSS","output/NativeMML"],
           TeX: { extensions: ["AMSmath.js","AMSsymbols.html","noErrors.html","noUndefined.js"], equationNumbers: { autoNumber: "AMS" } },
           extensions: ["tex2jax.js","mml2jax.html","MathMenu.html","MathZoom.html","jsMath2jax.js"],
           tex2jax: {
               inlineMath: [ ['$','$'] ],
               displayMath: [ ['$$','$$'] ],
               processEscapes: true },
           "HTML-CSS": {
               styles: { ".MathJax .mo, .MathJax .mi": {color: "black ! important"}}
           }
       });
    </script>

</head>

<body id="index" class="home">
  <div class="container">

    <div class="nav-main">
      <div class="wrap">
        <a class="nav-home" href="../index.html">
          <span class="nav-logo">c<span class="nav-logo-compose">⚬</span>mp<span class="nav-logo-compose">⚬</span>sing pr<span class="nav-logo-compose">⚬</span>grams</span>
        </a>
        <ul class="nav-site">
          <li><a href="../index.html">Text</a></li>
          <li><a href="../projects.html">Projects</a></li>
          <li><a href="../tutor.html">Tutor</a></li>
          <li><a href="../about.html">About</a></li>
        </ul>
      </div>
    </div>

    <section class="content wrap documentationContent">
      <div class="nav-docs">
	<h3>Chapter 2<a id="hide_contents">Hide contents</a> </h3>
		<div class="nav-docs-section">
			<h3><a href="21-introduction.html">2.1 Introduction</a></h3>
				<li><a href="21-introduction.html#native-data-types">2.1.1 Native Data Types</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="22-data-abstraction.html">2.2 Data Abstraction</a></h3>
				<li><a href="22-data-abstraction.html#example-rational-numbers">2.2.1 Example: Rational Numbers</a>
				<li><a href="22-data-abstraction.html#pairs">2.2.2 Pairs</a>
				<li><a href="22-data-abstraction.html#abstraction-barriers">2.2.3 Abstraction Barriers</a>
				<li><a href="22-data-abstraction.html#the-properties-of-data">2.2.4 The Properties of Data</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="23-sequences.html">2.3 Sequences</a></h3>
				<li><a href="23-sequences.html#lists">2.3.1 Lists</a>
				<li><a href="23-sequences.html#sequence-iteration">2.3.2 Sequence Iteration</a>
				<li><a href="23-sequences.html#sequence-processing">2.3.3 Sequence Processing</a>
				<li><a href="23-sequences.html#sequence-abstraction">2.3.4 Sequence Abstraction</a>
				<li><a href="23-sequences.html#strings">2.3.5 Strings</a>
				<li><a href="23-sequences.html#trees">2.3.6 Trees</a>
				<li><a href="23-sequences.html#linked-lists">2.3.7 Linked Lists</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="24-mutable-data.html">2.4 Mutable Data</a></h3>
				<li><a href="24-mutable-data.html#the-object-metaphor">2.4.1 The Object Metaphor</a>
				<li><a href="24-mutable-data.html#sequence-objects">2.4.2 Sequence Objects</a>
				<li><a href="24-mutable-data.html#dictionaries">2.4.3 Dictionaries</a>
				<li><a href="24-mutable-data.html#local-state">2.4.4 Local State</a>
				<li><a href="24-mutable-data.html#the-benefits-of-non-local-assignment">2.4.5 The Benefits of Non-Local Assignment</a>
				<li><a href="24-mutable-data.html#the-cost-of-non-local-assignment">2.4.6 The Cost of Non-Local Assignment</a>
				<li><a href="24-mutable-data.html#implementing-lists-and-dictionaries">2.4.7 Implementing Lists and Dictionaries</a>
				<li><a href="24-mutable-data.html#dispatch-dictionaries">2.4.8 Dispatch Dictionaries</a>
				<li><a href="24-mutable-data.html#propagating-constraints">2.4.9 Propagating Constraints</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="25-object-oriented-programming.html">2.5 Object-Oriented Programming</a></h3>
				<li><a href="25-object-oriented-programming.html#objects-and-classes">2.5.1 Objects and Classes</a>
				<li><a href="25-object-oriented-programming.html#defining-classes">2.5.2 Defining Classes</a>
				<li><a href="25-object-oriented-programming.html#message-passing-and-dot-expressions">2.5.3 Message Passing and Dot Expressions</a>
				<li><a href="25-object-oriented-programming.html#class-attributes">2.5.4 Class Attributes</a>
				<li><a href="25-object-oriented-programming.html#inheritance">2.5.5 Inheritance</a>
				<li><a href="25-object-oriented-programming.html#using-inheritance">2.5.6 Using Inheritance</a>
				<li><a href="25-object-oriented-programming.html#multiple-inheritance">2.5.7 Multiple Inheritance</a>
				<li><a href="25-object-oriented-programming.html#the-role-of-objects">2.5.8 The Role of Objects</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="26-implementing-classes-and-objects.html">2.6 Implementing Classes and Objects</a></h3>
				<li><a href="26-implementing-classes-and-objects.html#instances">2.6.1 Instances</a>
				<li><a href="26-implementing-classes-and-objects.html#classes">2.6.2 Classes</a>
				<li><a href="26-implementing-classes-and-objects.html#using-implemented-objects">2.6.3 Using Implemented Objects</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="27-object-abstraction.html">2.7 Object Abstraction</a></h3>
				<li><a href="27-object-abstraction.html#string-conversion">2.7.1 String Conversion</a>
				<li><a href="27-object-abstraction.html#special-methods">2.7.2 Special Methods</a>
				<li><a href="27-object-abstraction.html#multiple-representations">2.7.3 Multiple Representations</a>
				<li><a href="27-object-abstraction.html#generic-functions">2.7.4 Generic Functions</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="28-efficiency.html">2.8 Efficiency</a></h3>
				<li><a href="28-efficiency.html#measuring-efficiency">2.8.1 Measuring Efficiency</a>
				<li><a href="28-efficiency.html#memoization">2.8.2 Memoization</a>
				<li><a href="28-efficiency.html#orders-of-growth">2.8.3 Orders of Growth</a>
				<li><a href="28-efficiency.html#example-exponentiation">2.8.4 Example: Exponentiation</a>
				<li><a href="28-efficiency.html#growth-categories">2.8.5 Growth Categories</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="29-recursive-objects.html">2.9 Recursive Objects</a></h3>
				<li><a href="29-recursive-objects.html#linked-list-class">2.9.1 Linked List Class</a>
				<li><a href="29-recursive-objects.html#tree-class">2.9.2 Tree Class</a>
				<li><a href="29-recursive-objects.html#sets">2.9.3 Sets</a>
		</div>
      </div>

      <div class="inner-content">
	<h1>Chapter 2: Building Abstractions with Data</h1>
  <div class="section" id="introduction">
<h2>2.1   Introduction</h2>
<p>We concentrated in Chapter 1 on computational processes and on the role of
functions in program design. We saw how to use primitive data (numbers) and
primitive operations (arithmetic), how to form compound functions
through composition and control, and how to create functional abstractions by
giving names to processes. We also saw that higher-order functions enhance the
power of our language by enabling us to manipulate, and thereby to reason, in
terms of general methods of computation. This is much of the essence of
programming.</p>
<p>This chapter focuses on data.  The techniques we investigate here will allow us
to represent and manipulate information about many different domains.  Due to
the explosive growth of the Internet, a vast amount of structured information
is freely available to all of us online, and computation can be applied to a
vast range of different problems. Effective use of built-in and user-defined
data types are fundamental to data processing applications.</p>
<div class="section" id="native-data-types">
<h3>2.1.1   Native Data Types</h3>
<p>Every value in Python has a <em>class</em> that determines what type of value it is.
Values that share a class also share behavior. For example, the integers
<tt class="docutils literal">1</tt> and <tt class="docutils literal">2</tt> are both instances of the <tt class="docutils literal">int</tt> class. These two values can
be treated similarly. For example, they can both be negated or added to another
integer. The built-in <tt class="docutils literal">type</tt> function allows us to inspect the class of any
value.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="mi">2</span><span class="p">)</span>
<span class="go">&lt;class 'int'&gt;</span>
</pre></div>

<p>The values we have used so far are instances of a small number of <em>native</em> data
types that are built into the Python language. Native data types have the
following properties:</p>
<ol class="arabic simple">
<li>There are expressions that evaluate to values of native types, called
<em>literals</em>.</li>
<li>There are built-in functions and operators to manipulate values of native
types.</li>
</ol>
<p>The <tt class="docutils literal">int</tt> class is the native data type used to represent integers. Integer
literals (sequences of adjacent numerals) evaluate to <tt class="docutils literal">int</tt> values, and
mathematical operators manipulate these values.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="mi">12</span> <span class="o">+</span> <span class="mi">3000000000000000000000000</span>
<span class="go">3000000000000000000000012</span>
</pre></div>

<p>Python includes three native numeric types: integers (<tt class="docutils literal">int</tt>), real numbers
(<tt class="docutils literal">float</tt>), and complex numbers (<tt class="docutils literal">complex</tt>).</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="mf">1.5</span><span class="p">)</span>
<span class="go">&lt;class 'float'&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="mi">1</span><span class="o">+</span><span class="mi">1</span><span class="n">j</span><span class="p">)</span>
<span class="go">&lt;class 'complex'&gt;</span>
</pre></div>

<p><strong>Floats.</strong> The name <tt class="docutils literal">float</tt> comes from the way in which real numbers are
represented in Python and many other programming languages: a "floating point"
representation.  While the details of how numbers are represented is not a
topic for this text, some high-level differences between <tt class="docutils literal">int</tt> and <tt class="docutils literal">float</tt>
objects are important to know.  In particular, <tt class="docutils literal">int</tt> objects represent
integers exactly, without any approximation or limits on their size.  On the
other hand, <tt class="docutils literal">float</tt> objects can represent a wide range of fractional numbers,
but not all numbers can be represented exactly, and there are minimum and
maximum values. Therefore, <tt class="docutils literal">float</tt> values should be treated as approximations
to real values. These approximations have only a finite amount of precision.
Combining <tt class="docutils literal">float</tt> values can lead to approximation errors; both of the
following expressions would evaluate to <tt class="docutils literal">7</tt> if not for approximation.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="mi">7</span> <span class="o">/</span> <span class="mi">3</span> <span class="o">*</span> <span class="mi">3</span>
<span class="go">7.0</span>
</pre></div>

<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="mi">1</span> <span class="o">/</span> <span class="mi">3</span> <span class="o">*</span> <span class="mi">7</span> <span class="o">*</span> <span class="mi">3</span>
<span class="go">6.999999999999999</span>
</pre></div>

<p>Although <tt class="docutils literal">int</tt> values are combined above, dividing one <tt class="docutils literal">int</tt> by another
yields a <tt class="docutils literal">float</tt> value: a truncated finite approximation to the actual ratio
of the two integers divided.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="nb">type</span><span class="p">(</span><span class="mi">1</span><span class="o">/</span><span class="mi">3</span><span class="p">)</span>
<span class="go">&lt;class 'float'&gt;</span>
<span class="gp">&gt;&gt;&gt; </span><span class="mi">1</span><span class="o">/</span><span class="mi">3</span>
<span class="go">0.3333333333333333</span>
</pre></div>

<p>Problems with this approximation appear when we conduct equality tests.</p>
<div class="highlight"><pre><span></span><span class="gp">&gt;&gt;&gt; </span><span class="mi">1</span><span class="o">/</span><span class="mi">3</span> <span class="o">==</span> <span class="mf">0.333333333333333312345</span>  <span class="c1"># Beware of float approximation</span>
<span class="go">True</span>
</pre></div>

<p>These subtle differences between the <tt class="docutils literal">int</tt> and <tt class="docutils literal">float</tt> class have
wide-ranging consequences for writing programs, and so they are details that
must be memorized by programmers. Fortunately, there are only a handful of
native data types, limiting the amount of memorization required to become
proficient in a programming language. Moreover, these same details are
consistent across many programming languages, enforced by community guidelines
such as the
<a class="reference external" href="http://en.wikipedia.org/wiki/IEEE_floating_point">IEEE 754 floating point standard</a>.</p>
<p><strong>Non-numeric types.</strong> Values can represent many other types of data, such as
sounds, images, locations, web addresses, network connections, and more. A few
are represented by native data types, such as the <tt class="docutils literal">bool</tt> class for values
<tt class="docutils literal">True</tt> and <tt class="docutils literal">False</tt>. The type for most values must be defined by
programmers using the means of combination and abstraction that we will
develop in this chapter.</p>
<p>The following sections introduce more of Python's native data types, focusing
on the role they play in creating useful data abstractions. For those
interested in further details, a chapter on <a class="reference external" href="http://getpython3.com/diveintopython3/native-datatypes.html">native data types</a> in the online
book Dive Into Python 3 gives a pragmatic overview of all Python's native data
types and how to manipulate them, including numerous usage examples and
practical tips.</p>
</div>
</div>
  <p><i>Continue</i>:
  	<a href="22-data-abstraction.html">
  		2.2 Data Abstraction
  	</a>
      </div>
    </section>

    <div class="wrap">
      <footer id="contentinfo" class="body">
          Composing Programs by <a href="http://www.denero.org/">John
          DeNero</a>, based on the textbook <a
          href="http://mitpress.mit.edu/sicp/">Structure and
          Interpretation of Computer Programs</a> by Harold Abelson and
          Gerald Jay Sussman, is licensed under a <a rel="license"
          href="http://creativecommons.org/licenses/by-sa/3.0/">Creative
          Commons Attribution-ShareAlike 3.0 Unported License</a>.
      </footer><!-- /#contentinfo -->
    </div>
  </div>
</body>

<!-- Mirrored from www.composingprograms.com/pages/21-introduction.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:44:59 GMT -->
</html>