#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高阶函数演示程序
展示CS61A第1.6章中高阶函数的核心概念
"""

# 1. 函数作为参数
def summation(n, term):
    """通用求和函数，接受一个项函数作为参数"""
    total, k = 0, 1
    while k <= n:
        total, k = total + term(k), k + 1
    return total

def cube(x):
    """计算立方"""
    return x * x * x

def identity(x):
    """恒等函数"""
    return x

def square(x):
    """计算平方"""
    return x * x

def sum_cubes(n):
    """计算立方和"""
    return summation(n, cube)

def sum_naturals(n):
    """计算自然数之和"""
    return summation(n, identity)

def sum_squares(n):
    """计算平方和"""
    return summation(n, square)

# 2. 函数作为通用方法 - 迭代改进算法
def improve(update, close, guess=1):
    """迭代改进框架"""
    while not close(guess):
        guess = update(guess)
    return guess

def approx_eq(x, y, tolerance=1e-3):
    """近似相等判断"""
    return abs(x - y) < tolerance

# 3. 嵌套定义和闭包
def sqrt(a):
    """计算平方根"""
    def sqrt_update(x):
        return (x + a/x) / 2
    
    def sqrt_close(x):
        return approx_eq(x * x, a)
    
    return improve(sqrt_update, sqrt_close)

# 4. 函数作为返回值 - 函数组合
def compose1(f, g):
    """函数组合：返回一个新函数h，使得h(x) = f(g(x))"""
    def h(x):
        return f(g(x))
    return h

# 5. 柯里化
def curry2(f):
    """将二元函数柯里化"""
    def g(x):
        def h(y):
            return f(x, y)
        return h
    return g

def uncurry2(g):
    """将柯里化的函数还原为二元函数"""
    def f(x, y):
        return g(x)(y)
    return f

# 6. Lambda表达式
# 使用lambda简化函数定义
square_lambda = lambda x: x * x
successor = lambda x: x + 1

# 使用lambda的函数组合
square_successor = lambda x: square_lambda(successor(x))

# 7. 函数装饰器
def trace(fn):
    """跟踪函数调用的装饰器"""
    def wrapped(x):
        print(f'-> 调用 {fn.__name__}({x})')
        result = fn(x)
        print(f'<- 返回 {result}')
        return result
    return wrapped

@trace
def triple(x):
    """三倍函数"""
    return 3 * x

@trace
def factorial(n):
    """阶乘函数"""
    if n == 0 or n == 1:
        return 1
    else:
        return n * factorial(n - 1)

# 8. 牛顿法示例
def newton_update(f, df):
    """牛顿法更新函数"""
    def update(x):
        return x - f(x) / df(x)
    return update

def find_zero(f, df):
    """使用牛顿法寻找函数零点"""
    def near_zero(x):
        return approx_eq(f(x), 0)
    return improve(newton_update(f, df), near_zero)

def square_root_newton(a):
    """使用牛顿法计算平方根"""
    def f(x):
        return x * x - a
    
    def df(x):
        return 2 * x
    
    return find_zero(f, df)

# 演示程序
if __name__ == "__main__":
    print("=== 高阶函数演示程序 ===\n")
    
    # 1. 函数作为参数
    print("1. 函数作为参数:")
    print(f"前3个自然数的立方和: {sum_cubes(3)}")  # 1³ + 2³ + 3³ = 1 + 8 + 27 = 36
    print(f"前10个自然数之和: {sum_naturals(10)}")  # 1 + 2 + ... + 10 = 55
    print(f"前4个自然数的平方和: {sum_squares(4)}")  # 1² + 2² + 3² + 4² = 1 + 4 + 9 + 16 = 30
    print()
    
    # 2. 函数作为通用方法
    print("2. 函数作为通用方法 - 计算平方根:")
    print(f"256的平方根: {sqrt(256)}")
    print()
    
    # 3. 函数作为返回值
    print("3. 函数作为返回值 - 函数组合:")
    # 创建函数组合
    square_triple = compose1(square, triple)
    print(f"先三倍再平方10的结果: {square_triple(10)}")  # (3*10)² = 30² = 900
    print()
    
    # 4. 柯里化
    print("4. 柯里化:")
    # 柯里化pow函数
    curried_pow = curry2(pow)
    print(f"2的3次方 (柯里化): {curried_pow(2)(3)}")
    
    # 还原柯里化
    uncurried_pow = uncurry2(curried_pow)
    print(f"2的3次方 (还原): {uncurried_pow(2, 3)}")
    print()
    
    # 5. Lambda表达式
    print("5. Lambda表达式:")
    print(f"使用lambda计算平方: {square_lambda(12)}")
    print(f"使用lambda的函数组合: {square_successor(12)}")  # (12+1)² = 169
    print()
    
    # 6. 函数装饰器
    print("6. 函数装饰器:")
    result = triple(12)
    print(f"装饰器跟踪结果: {result}")
    print()
    
    # 7. 牛顿法
    print("7. 牛顿法:")
    print(f"64的平方根: {square_root_newton(64)}")
    print()
    
    print("=== 演示完成 ===")