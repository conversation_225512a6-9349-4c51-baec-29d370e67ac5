<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from www.composingprograms.com/versions/v1/pages/17-recursive-functions.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:45:59 GMT -->
<head>
  <title>1.7 Recursive Functions</title>
  <meta charset="utf-8" />

  <link rel="stylesheet" type="text/css" href="../theme/css/cp.css" />

  <!-- Stylesheets -->
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/pytutor.css"/>
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/ui-lightness/jquery-ui-1.8.21.custom.css" />
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/codemirror.css"  />

  <!-- jQuery -->
  <script type="text/javascript" src="../theme/tutor/js/jquery-1.8.2.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery-ui-1.8.24.custom.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery.ba-bbq.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery.jsPlumb-1.3.10-all-min.js"></script>

  <!-- codemirror.net online code editor -->
  <script type="text/javascript" src="../theme/tutor/js/codemirror/codemirror.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/codemirror/python.js"></script>

  <!-- d3 -->
  <script type="text/javascript" src="../theme/tutor/js/d3.v2.min.js"></script>

  <!-- Online Python Tutor -->
  <script type="text/javascript" src="../theme/tutor/js/pytutor.js"></script>
  <script type="text/javascript" src="../theme/js/tutorize.js"></script>


    <script src="http://cdn.mathjax.org/mathjax/latest/MathJax.js?config=TeX-AMS-MML_HTMLorMML" type= "text/javascript">
       MathJax.Hub.Config({
           config: ["MMLorHTML.js"],
           jax: ["input/TeX","input/MathML","output/HTML-CSS","output/NativeMML"],
           TeX: { extensions: ["AMSmath.js","AMSsymbols.html","noErrors.html","noUndefined.js"], equationNumbers: { autoNumber: "AMS" } },
           extensions: ["tex2jax.js","mml2jax.html","MathMenu.html","MathZoom.html","jsMath2jax.js"],
           tex2jax: {
               inlineMath: [ ['$','$'] ],
               displayMath: [ ['$$','$$'] ],
               processEscapes: true },
           "HTML-CSS": {
               styles: { ".MathJax .mo, .MathJax .mi": {color: "black ! important"}}
           }
       });
    </script>

</head>

<body id="index" class="home">
  <div class="container">

    <div class="nav-main">
      <div class="wrap">
        <a class="nav-home" href="../index.html">
          <span class="nav-logo">c<span class="nav-logo-compose">⚬</span>mp<span class="nav-logo-compose">⚬</span>sing pr<span class="nav-logo-compose">⚬</span>grams</span>
        </a>
VERSION 1, Replaced July 2014
      </div>
    </div>

    <section class="content wrap documentationContent">
      <div class="nav-docs">
	<h3>Chapter 1<a id="hide_contents">Hide contents</a> </h3>
		<div class="nav-docs-section">
			<h3><a href="11-getting-started.html">1.1 Getting Started</a></h3>
				<li><a href="11-getting-started.html#programming-in-python">1.1.1 Programming in Python</a>
				<li><a href="11-getting-started.html#installing-python-3">1.1.2 Installing Python 3</a>
				<li><a href="11-getting-started.html#interactive-sessions">1.1.3 Interactive Sessions</a>
				<li><a href="11-getting-started.html#first-example">1.1.4 First Example</a>
				<li><a href="11-getting-started.html#errors">1.1.5 Errors</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="12-elements-of-programming.html">1.2 Elements of Programming</a></h3>
				<li><a href="12-elements-of-programming.html#expressions">1.2.1 Expressions</a>
				<li><a href="12-elements-of-programming.html#call-expressions">1.2.2 Call Expressions</a>
				<li><a href="12-elements-of-programming.html#importing-library-functions">1.2.3 Importing Library Functions</a>
				<li><a href="12-elements-of-programming.html#names-and-the-environment">1.2.4 Names and the Environment</a>
				<li><a href="12-elements-of-programming.html#evaluating-nested-expressions">1.2.5 Evaluating Nested Expressions</a>
				<li><a href="12-elements-of-programming.html#the-non-pure-print-function">1.2.6 The Non-Pure Print Function</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="13-defining-new-functions.html">1.3 Defining New Functions</a></h3>
				<li><a href="13-defining-new-functions.html#environments">1.3.1 Environments</a>
				<li><a href="13-defining-new-functions.html#calling-user-defined-functions">1.3.2 Calling User-Defined Functions</a>
				<li><a href="13-defining-new-functions.html#example-calling-a-user-defined-function">1.3.3 Example: Calling a User-Defined Function</a>
				<li><a href="13-defining-new-functions.html#local-names">1.3.4 Local Names</a>
				<li><a href="13-defining-new-functions.html#choosing-names">1.3.5 Choosing Names</a>
				<li><a href="13-defining-new-functions.html#functions-as-abstractions">1.3.6 Functions as Abstractions</a>
				<li><a href="13-defining-new-functions.html#operators">1.3.7 Operators</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="14-designing-functions.html">1.4 Designing Functions</a></h3>
				<li><a href="14-designing-functions.html#documentation">1.4.1 Documentation</a>
				<li><a href="14-designing-functions.html#default-argument-values">1.4.2 Default Argument Values</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="15-control.html">1.5 Control</a></h3>
				<li><a href="15-control.html#statements">1.5.1 Statements</a>
				<li><a href="15-control.html#compound-statements">1.5.2 Compound Statements</a>
				<li><a href="15-control.html#defining-functions-ii-local-assignment">1.5.3 Defining Functions II: Local Assignment</a>
				<li><a href="15-control.html#conditional-statements">1.5.4 Conditional Statements</a>
				<li><a href="15-control.html#iteration">1.5.5 Iteration</a>
				<li><a href="15-control.html#testing">1.5.6 Testing</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="16-higher-order-functions.html">1.6 Higher-Order Functions</a></h3>
				<li><a href="16-higher-order-functions.html#functions-as-arguments">1.6.1 Functions as Arguments</a>
				<li><a href="16-higher-order-functions.html#functions-as-general-methods">1.6.2 Functions as General Methods</a>
				<li><a href="16-higher-order-functions.html#defining-functions-iii-nested-definitions">1.6.3 Defining Functions III: Nested Definitions</a>
				<li><a href="16-higher-order-functions.html#functions-as-returned-values">1.6.4 Functions as Returned Values</a>
				<li><a href="16-higher-order-functions.html#example-newton-s-method">1.6.5 Example: Newton's Method</a>
				<li><a href="16-higher-order-functions.html#currying">1.6.6 Currying</a>
				<li><a href="16-higher-order-functions.html#lambda-expressions">1.6.7 Lambda Expressions</a>
				<li><a href="16-higher-order-functions.html#abstractions-and-first-class-functions">1.6.8 Abstractions and First-Class Functions</a>
				<li><a href="16-higher-order-functions.html#function-decorators">1.6.9 Function Decorators</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="17-recursive-functions.html">1.7 Recursive Functions</a></h3>
				<li><a href="17-recursive-functions.html#the-anatomy-of-recursive-functions">1.7.1 The Anatomy of Recursive Functions</a>
				<li><a href="17-recursive-functions.html#mutual-recursion">1.7.2 Mutual Recursion</a>
				<li><a href="17-recursive-functions.html#printing-in-recursive-functions">1.7.3 Printing in Recursive Functions</a>
				<li><a href="17-recursive-functions.html#tree-recursion">1.7.4 Tree Recursion</a>
				<li><a href="17-recursive-functions.html#example-partitions">1.7.5 Example: Partitions</a>
		</div>
      </div>

      <div class="inner-content">
  <div class="section" id="recursive-functions">
<h2>1.7   Recursive Functions</h2>
<p>A function is called <em>recursive</em> if the body of the function calls the function
itself, either directly or indirectly.  That is, the process of executing the
body of a recursive function may in turn require applying that function again.
Recursive functions do not use any special syntax in Python, but they do require
some effort to understand and create.</p>
<p>We'll begin with an example problem: write a function that sums the digits of a
natural number. When designing recursive functions, we look for ways in which a
problem can be broken down into simpler problems.  In this case, the operators
<tt class="docutils literal">%</tt> and <tt class="docutils literal">//</tt> can be used to separate a number into two parts: its last digit
and all but its last digit.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="mi">18117</span> <span class="o">%</span> <span class="mi">10</span>
<span class="go">7</span>
<span class="gp">&gt;&gt;&gt; </span><span class="mi">18117</span> <span class="o">//</span> <span class="mi">10</span>
<span class="go">1811</span>
</pre></div>

<p>The sum of the digits of 18117 is <tt class="docutils literal">1+8+1+1+7 = 18</tt>.  Just as we can separate
the number, we can separate this sum into the last digit, 7, and the sum of all
but the last digit, <tt class="docutils literal">1+8+1+1 = 11</tt>.  This separation gives us an algorithm: to
sum the digits of a number <tt class="docutils literal">n</tt>, add its last digit <tt class="docutils literal">n % 10</tt> to the sum of
the digits of <tt class="docutils literal">n // 10</tt>.  There's one special case: if a number has only one
digit, then the sum of its digits is itself.  This algorithm can be implemented
as a recursive function.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">sum_digits</span><span class="p">(</span><span class="n">n</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""Return the sum of the digits of positive integer n."""</span>
<span class="gp">    </span>    <span class="k">if</span> <span class="n">n</span> <span class="o">&lt;</span> <span class="mi">10</span><span class="p">:</span>
<span class="gp">    </span>        <span class="k">return</span> <span class="n">n</span>
<span class="gp">    </span>    <span class="k">else</span><span class="p">:</span>
<span class="gp">    </span>        <span class="n">all_but_last</span><span class="p">,</span> <span class="n">last</span> <span class="o">=</span> <span class="n">n</span> <span class="o">//</span> <span class="mi">10</span><span class="p">,</span> <span class="n">n</span> <span class="o">%</span> <span class="mi">10</span>
<span class="gp">    </span>        <span class="k">return</span> <span class="n">sum_digits</span><span class="p">(</span><span class="n">all_but_last</span><span class="p">)</span> <span class="o">+</span> <span class="n">last</span>
</pre></div>

<p>This definition of <tt class="docutils literal">sum_digits</tt> is both complete and correct, even though the
<tt class="docutils literal">sum_digits</tt> function is called within its own body. The problem of summing
the digits of a number is broken down into two steps: summing all but the last
digit, then adding the last digit.  Both of these steps are simpler than the
original problem.  The function is recursive because the first step is the same
kind of problem as the original problem.  That is, <tt class="docutils literal">sum_digits</tt> is exactly the
function we need in order to implement <tt class="docutils literal">sum_digits</tt>.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">sum_digits</span><span class="p">(</span><span class="mi">9</span><span class="p">)</span>
<span class="go">9</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sum_digits</span><span class="p">(</span><span class="mi">18117</span><span class="p">)</span>
<span class="go">18</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sum_digits</span><span class="p">(</span><span class="mi">9437184</span><span class="p">)</span>
<span class="go">36</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">sum_digits</span><span class="p">(</span><span class="mi">11408855402054064613470328848384</span><span class="p">)</span>
<span class="go">126</span>
</pre></div>

<p>We can understand precisely how this recursive function applies successfully
using our environment model of computation. No new rules are required.</p>
<div class="example" data-output="False" data-step="-1" id="example_21" style="">
def sum_digits(n):
    if n &lt; 10:
        return n
    else:
        all_but_last, last = n // 10, n % 10
        return sum_digits(all_but_last) + last

sum_digits(738)
</div>
<script type="text/javascript">
var example_21_trace = {"trace": [{"line": 1, "event": "step_line", "globals": {}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": [], "heap": {}, "stdout": ""}, {"line": 8, "event": "step_line", "globals": {"sum_digits": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["sum_digits"], "heap": {"1": ["FUNCTION", "sum_digits(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"sum_digits": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f1", "is_zombie": false, "encoded_locals": {"n": 738}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n"]}], "func_name": "sum_digits", "ordered_globals": ["sum_digits"], "heap": {"1": ["FUNCTION", "sum_digits(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"sum_digits": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f1", "is_zombie": false, "encoded_locals": {"n": 738}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n"]}], "func_name": "sum_digits", "ordered_globals": ["sum_digits"], "heap": {"1": ["FUNCTION", "sum_digits(n)", null]}, "stdout": ""}, {"line": 5, "event": "step_line", "globals": {"sum_digits": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f1", "is_zombie": false, "encoded_locals": {"n": 738}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n"]}], "func_name": "sum_digits", "ordered_globals": ["sum_digits"], "heap": {"1": ["FUNCTION", "sum_digits(n)", null]}, "stdout": ""}, {"line": 6, "event": "step_line", "globals": {"sum_digits": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f1", "is_zombie": false, "encoded_locals": {"last": 8, "n": 738, "all_but_last": 73}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n", "all_but_last", "last"]}], "func_name": "sum_digits", "ordered_globals": ["sum_digits"], "heap": {"1": ["FUNCTION", "sum_digits(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"sum_digits": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f1", "is_zombie": false, "encoded_locals": {"last": 8, "n": 738, "all_but_last": 73}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n", "all_but_last", "last"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f2", "is_zombie": false, "encoded_locals": {"n": 73}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n"]}], "func_name": "sum_digits", "ordered_globals": ["sum_digits"], "heap": {"1": ["FUNCTION", "sum_digits(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"sum_digits": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f1", "is_zombie": false, "encoded_locals": {"last": 8, "n": 738, "all_but_last": 73}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n", "all_but_last", "last"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f2", "is_zombie": false, "encoded_locals": {"n": 73}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n"]}], "func_name": "sum_digits", "ordered_globals": ["sum_digits"], "heap": {"1": ["FUNCTION", "sum_digits(n)", null]}, "stdout": ""}, {"line": 5, "event": "step_line", "globals": {"sum_digits": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f1", "is_zombie": false, "encoded_locals": {"last": 8, "n": 738, "all_but_last": 73}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n", "all_but_last", "last"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f2", "is_zombie": false, "encoded_locals": {"n": 73}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n"]}], "func_name": "sum_digits", "ordered_globals": ["sum_digits"], "heap": {"1": ["FUNCTION", "sum_digits(n)", null]}, "stdout": ""}, {"line": 6, "event": "step_line", "globals": {"sum_digits": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f1", "is_zombie": false, "encoded_locals": {"last": 8, "n": 738, "all_but_last": 73}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n", "all_but_last", "last"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f2", "is_zombie": false, "encoded_locals": {"last": 3, "n": 73, "all_but_last": 7}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n", "all_but_last", "last"]}], "func_name": "sum_digits", "ordered_globals": ["sum_digits"], "heap": {"1": ["FUNCTION", "sum_digits(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"sum_digits": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f1", "is_zombie": false, "encoded_locals": {"last": 8, "n": 738, "all_but_last": 73}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n", "all_but_last", "last"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f2", "is_zombie": false, "encoded_locals": {"last": 3, "n": 73, "all_but_last": 7}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n", "all_but_last", "last"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f3", "is_zombie": false, "encoded_locals": {"n": 7}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n"]}], "func_name": "sum_digits", "ordered_globals": ["sum_digits"], "heap": {"1": ["FUNCTION", "sum_digits(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"sum_digits": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f1", "is_zombie": false, "encoded_locals": {"last": 8, "n": 738, "all_but_last": 73}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n", "all_but_last", "last"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f2", "is_zombie": false, "encoded_locals": {"last": 3, "n": 73, "all_but_last": 7}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n", "all_but_last", "last"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f3", "is_zombie": false, "encoded_locals": {"n": 7}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n"]}], "func_name": "sum_digits", "ordered_globals": ["sum_digits"], "heap": {"1": ["FUNCTION", "sum_digits(n)", null]}, "stdout": ""}, {"line": 3, "event": "step_line", "globals": {"sum_digits": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f1", "is_zombie": false, "encoded_locals": {"last": 8, "n": 738, "all_but_last": 73}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n", "all_but_last", "last"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f2", "is_zombie": false, "encoded_locals": {"last": 3, "n": 73, "all_but_last": 7}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n", "all_but_last", "last"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f3", "is_zombie": false, "encoded_locals": {"n": 7}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n"]}], "func_name": "sum_digits", "ordered_globals": ["sum_digits"], "heap": {"1": ["FUNCTION", "sum_digits(n)", null]}, "stdout": ""}, {"line": 3, "event": "return", "globals": {"sum_digits": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f1", "is_zombie": false, "encoded_locals": {"last": 8, "n": 738, "all_but_last": 73}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n", "all_but_last", "last"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f2", "is_zombie": false, "encoded_locals": {"last": 3, "n": 73, "all_but_last": 7}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n", "all_but_last", "last"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f3", "is_zombie": false, "encoded_locals": {"n": 7, "__return__": 7}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n", "__return__"]}], "func_name": "sum_digits", "ordered_globals": ["sum_digits"], "heap": {"1": ["FUNCTION", "sum_digits(n)", null]}, "stdout": ""}, {"line": 6, "event": "return", "globals": {"sum_digits": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f1", "is_zombie": false, "encoded_locals": {"last": 8, "n": 738, "all_but_last": 73}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n", "all_but_last", "last"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f2", "is_zombie": false, "encoded_locals": {"last": 3, "n": 73, "__return__": 10, "all_but_last": 7}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n", "all_but_last", "last", "__return__"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f3_z", "is_zombie": true, "encoded_locals": {"n": 7, "__return__": 7}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n", "__return__"]}], "func_name": "sum_digits", "ordered_globals": ["sum_digits"], "heap": {"1": ["FUNCTION", "sum_digits(n)", null]}, "stdout": ""}, {"line": 6, "event": "return", "globals": {"sum_digits": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f1", "is_zombie": false, "encoded_locals": {"last": 8, "n": 738, "__return__": 18, "all_but_last": 73}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n", "all_but_last", "last", "__return__"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f2_z", "is_zombie": true, "encoded_locals": {"last": 3, "n": 73, "__return__": 10, "all_but_last": 7}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n", "all_but_last", "last", "__return__"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f3_z", "is_zombie": true, "encoded_locals": {"n": 7, "__return__": 7}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n", "__return__"]}], "func_name": "sum_digits", "ordered_globals": ["sum_digits"], "heap": {"1": ["FUNCTION", "sum_digits(n)", null]}, "stdout": ""}, {"line": 8, "event": "return", "globals": {"sum_digits": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f1_z", "is_zombie": true, "encoded_locals": {"last": 8, "n": 738, "__return__": 18, "all_but_last": 73}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n", "all_but_last", "last", "__return__"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f2_z", "is_zombie": true, "encoded_locals": {"last": 3, "n": 73, "__return__": 10, "all_but_last": 7}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n", "all_but_last", "last", "__return__"]}, {"parent_frame_id_list": [], "func_name": "sum_digits", "is_parent": false, "unique_hash": "sum_digits_f3_z", "is_zombie": true, "encoded_locals": {"n": 7, "__return__": 7}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n", "__return__"]}], "func_name": "<module>", "ordered_globals": ["sum_digits"], "heap": {"1": ["FUNCTION", "sum_digits(n)", null]}, "stdout": ""}], "code": "def sum_digits(n):\n    if n < 10:\n        return n\n    else:\n        all_but_last, last = n // 10, n % 10\n        return sum_digits(all_but_last) + last\n\nsum_digits(738)"}</script><p>When the <tt class="docutils literal">def</tt> statement is executed, the name <tt class="docutils literal">sum_digits</tt> is bound to a
new function, but the body of that function is not yet executed.  Therefore, the
circular nature of <tt class="docutils literal">sum_digits</tt> is not a problem yet.  Then, <tt class="docutils literal">sum_digits</tt> is
called on 738:</p>
<ol class="arabic simple">
<li>A local frame for <tt class="docutils literal">sum_digits</tt> with <tt class="docutils literal">n</tt> bound to 738 is created, and the
body of <tt class="docutils literal">sum_digits</tt> is executed in the environment that starts with that
frame.</li>
<li>Since 738 is not less than 10, the assignment statement on line 4 is
executed, splitting 738 into 73 and 8.</li>
<li>In the following return statement, <tt class="docutils literal">sum_digits</tt> is called on 73, the value
of <tt class="docutils literal">all_but_last</tt> in the current environment.</li>
</ol>
<ol class="arabic simple" start="3">
<li>Another local frame for <tt class="docutils literal">sum_digits</tt> is created, this time with <tt class="docutils literal">n</tt> bound
to 73. The body of <tt class="docutils literal">sum_digits</tt> is again executed in the new environment
that starts with this frame.</li>
<li>Since 73 is also not less than 10, 73 is split into 7 and 3 and
<tt class="docutils literal">sum_digits</tt> is called on 7, the value of <tt class="docutils literal">all_but_last</tt> evaluated in
this frame.</li>
<li>A third local frame for <tt class="docutils literal">sum_digits</tt> is created, with <tt class="docutils literal">n</tt> bound to 7.</li>
<li>In the environment starting with this frame, it is true that <tt class="docutils literal">n &lt; 10</tt>, and
therefore 7 is returned.</li>
<li>In the second local frame, this return value 7 is summed with 3, the value of
<tt class="docutils literal">last</tt>, to return 10.</li>
<li>In the first local frame, this return value 10 is summed with 8, the value of
<tt class="docutils literal">last</tt>, to return 18.</li>
</ol>
<p>This recursive function applies correctly, despite its circular character,
because it is applied twice, but with a different argument each time.  Moreover,
the second application was a simpler instance of the digit summing problem than
the first.  Generate the environment diagram for the call <tt class="docutils literal">sum_digits(18117)</tt>
to see that each successive call to <tt class="docutils literal">sum_digits</tt> takes a smaller argument than
the last, until eventually a single-digit input is reached.</p>
<p>This example also illustrates how functions with simple bodies can evolve
complex computational processes by using recursion.</p>
<div class="section" id="the-anatomy-of-recursive-functions">
<h3>1.7.1   The Anatomy of Recursive Functions</h3>
<p>A common pattern can be found in the body of many recursive functions.  The body
begins with a <em>base case</em>, a conditional statement that defines the behavior of
the function for the inputs that are simplest to process.  In the case of
<tt class="docutils literal">sum_digits</tt>, the base case is any single-digit argument, and we simply return
that argument. Some recursive functions will have multiple base cases.</p>
<p>The base cases are then followed by one or more <em>recursive calls</em>.  Recursive
calls always have a certain character: they simplify the original problem.
Recursive functions express computation by simplifying problems incrementally.
For example, summing the digits of 7 is simpler than summing the digits of 73,
which in turn is simpler than summing the digits of 738.  For each subsequent
call, there is less work left to be done.</p>
<p>Recursive functions often solve problems in a different way than the iterative
approaches that we have used previously. Consider a function <tt class="docutils literal">fact</tt> to compute
<tt class="docutils literal">n</tt> factorial, where for example <tt class="docutils literal">fact(4)</tt> computes <span class="rawlatex">$4! = 4 \cdot
3 \cdot 2 \cdot 1 = 24$</span>.</p>
<p>A natural implementation using a <tt class="docutils literal">while</tt> statement accumulates the total by
multiplying together each positive integer up to <tt class="docutils literal">n</tt>.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">fact_iter</span><span class="p">(</span><span class="n">n</span><span class="p">):</span>
<span class="gp">    </span>    <span class="n">total</span><span class="p">,</span> <span class="n">k</span> <span class="o">=</span> <span class="mi">1</span><span class="p">,</span> <span class="mi">1</span>
<span class="gp">    </span>    <span class="k">while</span> <span class="n">k</span> <span class="o">&lt;=</span> <span class="n">n</span><span class="p">:</span>
<span class="gp">    </span>        <span class="n">total</span><span class="p">,</span> <span class="n">k</span> <span class="o">=</span> <span class="n">total</span> <span class="o">*</span> <span class="n">k</span><span class="p">,</span> <span class="n">k</span> <span class="o">+</span> <span class="mi">1</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">total</span>
</pre></div>

<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">fact_iter</span><span class="p">(</span><span class="mi">4</span><span class="p">)</span>
<span class="go">24</span>
</pre></div>

<p>On the other hand, a recursive implementation of factorial can express
<tt class="docutils literal">fact(n)</tt> in terms of <tt class="docutils literal"><span class="pre">fact(n-1)</span></tt>, a simpler problem.  The base case of the
recursion is the simplest form of the problem: <tt class="docutils literal">fact(1)</tt> is 1.</p>
<div class="example" data-output="False" data-step="-1" id="example_22" style="">
def fact(n):
    if n == 1:
        return 1
    else:
        return n * fact(n-1)

fact(4)
</div>
<script type="text/javascript">
var example_22_trace = {"trace": [{"line": 1, "event": "step_line", "globals": {}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": [], "heap": {}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"fact": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n"]}], "func_name": "fact", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n"]}], "func_name": "fact", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 5, "event": "step_line", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n"]}], "func_name": "fact", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n"]}], "func_name": "fact", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n"]}], "func_name": "fact", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 5, "event": "step_line", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n"]}], "func_name": "fact", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n"]}], "func_name": "fact", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n"]}], "func_name": "fact", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 5, "event": "step_line", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n"]}], "func_name": "fact", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f4", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["n"]}], "func_name": "fact", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f4", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["n"]}], "func_name": "fact", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 3, "event": "step_line", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f4", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["n"]}], "func_name": "fact", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 3, "event": "return", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f4", "is_zombie": false, "encoded_locals": {"n": 1, "__return__": 1}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["n", "__return__"]}], "func_name": "fact", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 5, "event": "return", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f3", "is_zombie": false, "encoded_locals": {"n": 2, "__return__": 2}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n", "__return__"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f4_z", "is_zombie": true, "encoded_locals": {"n": 1, "__return__": 1}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n", "__return__"]}], "func_name": "fact", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 5, "event": "return", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f2", "is_zombie": false, "encoded_locals": {"n": 3, "__return__": 6}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n", "__return__"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f3_z", "is_zombie": true, "encoded_locals": {"n": 2, "__return__": 2}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n", "__return__"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f4_z", "is_zombie": true, "encoded_locals": {"n": 1, "__return__": 1}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n", "__return__"]}], "func_name": "fact", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 5, "event": "return", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1", "is_zombie": false, "encoded_locals": {"n": 4, "__return__": 24}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n", "__return__"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f2_z", "is_zombie": true, "encoded_locals": {"n": 3, "__return__": 6}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n", "__return__"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f3_z", "is_zombie": true, "encoded_locals": {"n": 2, "__return__": 2}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n", "__return__"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f4_z", "is_zombie": true, "encoded_locals": {"n": 1, "__return__": 1}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n", "__return__"]}], "func_name": "fact", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"fact": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f1_z", "is_zombie": true, "encoded_locals": {"n": 4, "__return__": 24}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n", "__return__"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f2_z", "is_zombie": true, "encoded_locals": {"n": 3, "__return__": 6}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n", "__return__"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f3_z", "is_zombie": true, "encoded_locals": {"n": 2, "__return__": 2}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n", "__return__"]}, {"parent_frame_id_list": [], "func_name": "fact", "is_parent": false, "unique_hash": "fact_f4_z", "is_zombie": true, "encoded_locals": {"n": 1, "__return__": 1}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n", "__return__"]}], "func_name": "<module>", "ordered_globals": ["fact"], "heap": {"1": ["FUNCTION", "fact(n)", null]}, "stdout": ""}], "code": "def fact(n):\n    if n == 1:\n        return 1\n    else:\n        return n * fact(n-1)\n\nfact(4)"}</script><p>These two factorial functions differ conceptually.  The iterative function
constructs the result from the base case of 1 to the final total by
successively multiplying in each term.  The recursive function, on the other
hand, constructs the result directly from the final term, <tt class="docutils literal">n</tt>,  and the
result of the simpler problem, <tt class="docutils literal"><span class="pre">fact(n-1)</span></tt>.</p>
<p>As the recursion "unwinds" through successive applications of the <em>fact</em>
function to simpler and simpler problem instances, the result is eventually
built starting from the base case.  The recursion ends by passing the argument
1 to <tt class="docutils literal">fact</tt>; the result of each call depends on the next until the base
case is reached.</p>
<p>The correctness of this recursive function is easy to verify from the standard
definition of the mathematical function for factorial:</p>
\begin{align*}
(n-1)! &amp;=     (n-1) \cdot (n-2) \cdot \dots \cdot 1 \\
n!     &amp;= n \cdot (n-1) \cdot (n-2) \cdot \dots \cdot 1 \\
n!     &amp;= n \cdot (n-1)!
\end{align*}<p>While we can unwind the recursion using our model of computation, it is often
clearer to think about recursive calls as functional abstractions.  That is, we
should not care about how <tt class="docutils literal"><span class="pre">fact(n-1)</span></tt> is implemented in the body of <tt class="docutils literal">fact</tt>;
we should simply trust that it computes the factorial of <tt class="docutils literal"><span class="pre">n-1</span></tt>.  Treating a
recursive call as a functional abstraction has been called a <em>recursive leap
of faith</em>. We define a function in terms of itself, but simply trust that the
simpler cases will work correctly when verifying the correctness of the
function. In this example, we trust that <tt class="docutils literal"><span class="pre">fact(n-1)</span></tt> will correctly compute
<tt class="docutils literal"><span class="pre">(n-1)!</span></tt>; we must only check that <tt class="docutils literal">n!</tt> is computed correctly if this
assumption holds. In this way, verifying the correctness of a recursive function
is a form of proof by induction.</p>
<p>The functions <em>fact_iter</em> and <em>fact</em> also differ because the former must
introduce two additional names, <tt class="docutils literal">total</tt> and <tt class="docutils literal">k</tt>, that are not required in
the recursive implementation.  In general, iterative functions must maintain
some local state that changes throughout the course of computation.  At any
point in the iteration, that state characterizes the result of completed work
and the amount of work remaining.  For example, when <tt class="docutils literal">k</tt> is 3 and total
is 2, there are still two terms remaining to be processed, 3 and 4.
On the other hand, <em>fact</em> is characterized by its single argument <tt class="docutils literal">n</tt>. The
state of the computation is entirely contained within the structure of the
environment, which has return values that take the role of <tt class="docutils literal">total</tt>, and binds
<tt class="docutils literal">n</tt> to different values in different frames rather than explicitly tracking
<tt class="docutils literal">k</tt>.</p>
<p>Recursive functions leverage the rules of evaluating call expressions to bind
names to values, often avoiding the nuisance of correctly assigning local names
during iteration. For this reason, recursive functions can be easier to define
correctly. However, learning to recognize the computational processes evolved by
recursive functions certainly requires practice.</p>
</div>
<div class="section" id="mutual-recursion">
<h3>1.7.2   Mutual Recursion</h3>
<p>When a recursive procedure is divided among two functions that call each other,
the functions are said to be <em>mutually recursive</em>. As an example, consider the
following definition of even and odd for non-negative integers:</p>
<ul class="simple">
<li>a number is even if it is one more than an odd number</li>
<li>a number is odd if it is one more than an even number</li>
<li>0 is even</li>
</ul>
<p>Using this definition, we can implement mutually recursive functions to
determine whether a number is even or odd:</p>
<div class="example" data-output="False" data-step="0" id="example_23" style="">
def is_even(n):
    if n == 0:
        return True
    else:
        return is_odd(n-1)

def is_odd(n):
    if n == 0:
        return False
    else:
        return is_even(n-1)

result = is_even(4)
</div>
<script type="text/javascript">
var example_23_trace = {"trace": [{"line": 1, "event": "step_line", "globals": {}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": [], "heap": {}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"is_even": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["is_even"], "heap": {"1": ["FUNCTION", "is_even(n)", null]}, "stdout": ""}, {"line": 13, "event": "step_line", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n"]}], "func_name": "is_even", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n"]}], "func_name": "is_even", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 5, "event": "step_line", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n"]}], "func_name": "is_even", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 7, "event": "call", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n"]}], "func_name": "is_odd", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 8, "event": "step_line", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n"]}], "func_name": "is_odd", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 11, "event": "step_line", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n"]}], "func_name": "is_odd", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n"]}], "func_name": "is_even", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n"]}], "func_name": "is_even", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 5, "event": "step_line", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n"]}], "func_name": "is_even", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 7, "event": "call", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f4", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["n"]}], "func_name": "is_odd", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 8, "event": "step_line", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f4", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["n"]}], "func_name": "is_odd", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 11, "event": "step_line", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f4", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["n"]}], "func_name": "is_odd", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f4", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f5", "is_zombie": false, "encoded_locals": {"n": 0}, "is_highlighted": true, "frame_id": 5, "ordered_varnames": ["n"]}], "func_name": "is_even", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f4", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f5", "is_zombie": false, "encoded_locals": {"n": 0}, "is_highlighted": true, "frame_id": 5, "ordered_varnames": ["n"]}], "func_name": "is_even", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 3, "event": "step_line", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f4", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f5", "is_zombie": false, "encoded_locals": {"n": 0}, "is_highlighted": true, "frame_id": 5, "ordered_varnames": ["n"]}], "func_name": "is_even", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 3, "event": "return", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f4", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f5", "is_zombie": false, "encoded_locals": {"n": 0, "__return__": true}, "is_highlighted": true, "frame_id": 5, "ordered_varnames": ["n", "__return__"]}], "func_name": "is_even", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 11, "event": "return", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": false, "frame_id": 3, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f4", "is_zombie": false, "encoded_locals": {"n": 1, "__return__": true}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["n", "__return__"]}], "func_name": "is_odd", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 5, "event": "return", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f2", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f3", "is_zombie": false, "encoded_locals": {"n": 2, "__return__": true}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n", "__return__"]}], "func_name": "is_even", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 11, "event": "return", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "is_odd", "is_parent": false, "unique_hash": "is_odd_f2", "is_zombie": false, "encoded_locals": {"n": 3, "__return__": true}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n", "__return__"]}], "func_name": "is_odd", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 5, "event": "return", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "is_even", "is_parent": false, "unique_hash": "is_even_f1", "is_zombie": false, "encoded_locals": {"n": 4, "__return__": true}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n", "__return__"]}], "func_name": "is_even", "ordered_globals": ["is_even", "is_odd"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}, {"line": 13, "event": "return", "globals": {"is_even": ["REF", 1], "is_odd": ["REF", 2], "result": true}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["is_even", "is_odd", "result"], "heap": {"1": ["FUNCTION", "is_even(n)", null], "2": ["FUNCTION", "is_odd(n)", null]}, "stdout": ""}], "code": "def is_even(n):\n    if n == 0:\n        return True\n    else:\n        return is_odd(n-1)\n\ndef is_odd(n):\n    if n == 0:\n        return False\n    else:\n        return is_even(n-1)\n\nresult = is_even(4)"}</script><p>Mutually recursive functions can be turned into a single recursive function
by breaking the abstraction boundary between the two functions. In this
example, the body of <tt class="docutils literal">is_odd</tt> can be incorporated into that of <tt class="docutils literal">is_even</tt>,
making sure to replace <tt class="docutils literal">n</tt> with <tt class="docutils literal"><span class="pre">n-1</span></tt> in the body of <tt class="docutils literal">is_odd</tt> to reflect
the argument passed into it:</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">is_even</span><span class="p">(</span><span class="n">n</span><span class="p">):</span>
<span class="gp">    </span>    <span class="k">if</span> <span class="n">n</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
<span class="gp">    </span>        <span class="k">return</span> <span class="k">True</span>
<span class="gp">    </span>    <span class="k">else</span><span class="p">:</span>
<span class="gp">    </span>        <span class="k">if</span> <span class="p">(</span><span class="n">n</span><span class="o">-</span><span class="mi">1</span><span class="p">)</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="k">False</span>
<span class="gp">    </span>        <span class="k">else</span><span class="p">:</span>
<span class="gp">    </span>            <span class="k">return</span> <span class="n">is_even</span><span class="p">((</span><span class="n">n</span><span class="o">-</span><span class="mi">1</span><span class="p">)</span><span class="o">-</span><span class="mi">1</span><span class="p">)</span>
</pre></div>

<p>As such, mutual recursion is no more mysterious or powerful than simple
recursion, and it provides a mechanism for maintaining abstraction within a
complicated recursive program.</p>
</div>
<div class="section" id="printing-in-recursive-functions">
<h3>1.7.3   Printing in Recursive Functions</h3>
<p>The computational process evolved by a recursive function can often be visualized using calls to <tt class="docutils literal">print</tt>.  As an example, we will implement a function <tt class="docutils literal">cascade</tt> that prints all prefixes of a number from largest to smallest to largest.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">cascade</span><span class="p">(</span><span class="n">n</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""Print a cascade of prefixes of n."""</span>
<span class="gp">    </span>    <span class="k">if</span> <span class="n">n</span> <span class="o">&lt;</span> <span class="mi">10</span><span class="p">:</span>
<span class="gp">    </span>        <span class="nb">print</span><span class="p">(</span><span class="n">n</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">else</span><span class="p">:</span>
<span class="gp">    </span>        <span class="nb">print</span><span class="p">(</span><span class="n">n</span><span class="p">)</span>
<span class="gp">    </span>        <span class="n">cascade</span><span class="p">(</span><span class="n">n</span><span class="o">//</span><span class="mi">10</span><span class="p">)</span>
<span class="gp">    </span>        <span class="nb">print</span><span class="p">(</span><span class="n">n</span><span class="p">)</span>
</pre></div>

<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">cascade</span><span class="p">(</span><span class="mi">2013</span><span class="p">)</span>
<span class="go">2013</span>
<span class="go">201</span>
<span class="go">20</span>
<span class="go">2</span>
<span class="go">20</span>
<span class="go">201</span>
<span class="go">2013</span>
</pre></div>

<p>In this recursive function, the base case is a single-digit number, which is printed.  Otherwise, a recursive call is placed between two calls to <tt class="docutils literal">print</tt>.</p>
<p>It is not a rigid requirement that base cases be expressed before recursive
calls.  In fact, this function can be expressed more compactly by observing that
<tt class="docutils literal">print(n)</tt> is repeated in both clauses of the conditional statement, and
therefore can precede it.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">cascade</span><span class="p">(</span><span class="n">n</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""Print a cascade of prefixes of n."""</span>
<span class="gp">    </span>    <span class="nb">print</span><span class="p">(</span><span class="n">n</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">if</span> <span class="n">n</span> <span class="o">&gt;=</span> <span class="mi">10</span><span class="p">:</span>
<span class="gp">    </span>        <span class="n">cascade</span><span class="p">(</span><span class="n">n</span><span class="o">//</span><span class="mi">10</span><span class="p">)</span>
<span class="gp">    </span>        <span class="nb">print</span><span class="p">(</span><span class="n">n</span><span class="p">)</span>
</pre></div>

<p>As another example of mutual recursion, consider a two-player game in which
there are <tt class="docutils literal">n</tt> initial pebbles on a table. The players take turns, removing
either one or two pebbles from the table, and the player who removes the
final pebble wins. Suppose that Alice and Bob play this game, each using a
simple strategy:</p>
<ul class="simple">
<li>Alice always removes a single pebble</li>
<li>Bob removes two pebbles if an even number of pebbles is on the table, and
one otherwise</li>
</ul>
<p>Given <tt class="docutils literal">n</tt> initial pebbles and Alice starting, who wins the game?</p>
<p>A natural decomposition of this problem is to encapsulate each strategy in its
own function. This allows us to modify one strategy without affecting the other,
maintaining the abstraction barrier between the two. In order to incorporate the
turn-by-turn nature of the game, these two functions call each other at the end
of each turn.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">play_alice</span><span class="p">(</span><span class="n">n</span><span class="p">):</span>
<span class="gp">    </span>    <span class="k">if</span> <span class="n">n</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
<span class="gp">    </span>        <span class="nb">print</span><span class="p">(</span><span class="s">"Bob wins!"</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">else</span><span class="p">:</span>
<span class="gp">    </span>        <span class="n">play_bob</span><span class="p">(</span><span class="n">n</span><span class="o">-</span><span class="mi">1</span><span class="p">)</span>
</pre></div>

<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">play_bob</span><span class="p">(</span><span class="n">n</span><span class="p">):</span>
<span class="gp">    </span>    <span class="k">if</span> <span class="n">n</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
<span class="gp">    </span>        <span class="nb">print</span><span class="p">(</span><span class="s">"Alice wins!"</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">elif</span> <span class="n">is_even</span><span class="p">(</span><span class="n">n</span><span class="p">):</span>
<span class="gp">    </span>        <span class="n">play_alice</span><span class="p">(</span><span class="n">n</span><span class="o">-</span><span class="mi">2</span><span class="p">)</span>
<span class="gp">    </span>    <span class="k">else</span><span class="p">:</span>
<span class="gp">    </span>        <span class="n">play_alice</span><span class="p">(</span><span class="n">n</span><span class="o">-</span><span class="mi">1</span><span class="p">)</span>
</pre></div>

<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">play_alice</span><span class="p">(</span><span class="mi">20</span><span class="p">)</span>
<span class="go">Bob wins!</span>
</pre></div>

<p>In <tt class="docutils literal">play_bob</tt>, we see that multiple recursive calls may appear in the body of
a function.  However, in this example, each call to <tt class="docutils literal">play_bob</tt> calls
<tt class="docutils literal">play_alice</tt> at most once. In the next section, we consider what happens when
a single function call makes multiple direct recursive calls.</p>
</div>
<div class="section" id="tree-recursion">
<h3>1.7.4   Tree Recursion</h3>
<p>Another common pattern of computation is called tree recursion. As an example,
consider computing the sequence of Fibonacci numbers, in which each number is
the sum of the preceding two.</p>
<div class="example" data-output="False" data-step="0" id="example_24" style="">
def fib(n):
    if n == 1:
        return 0
    if n == 2:
        return 1
    else:
        return fib(n-2) + fib(n-1)

result = fib(6)
</div>
<script type="text/javascript">
var example_24_trace = {"trace": [{"line": 1, "event": "step_line", "globals": {}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": [], "heap": {}, "stdout": ""}, {"line": 9, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 5, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f3", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 5, "event": "return", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f3", "is_zombie": false, "encoded_locals": {"n": 2, "__return__": 1}, "is_highlighted": true, "frame_id": 3, "ordered_varnames": ["n", "__return__"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f4", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f4", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f4", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f4", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f4", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f5", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": true, "frame_id": 5, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f4", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f5", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": true, "frame_id": 5, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 3, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f4", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f5", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": true, "frame_id": 5, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 3, "event": "return", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f4", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f5", "is_zombie": false, "encoded_locals": {"n": 1, "__return__": 0}, "is_highlighted": true, "frame_id": 5, "ordered_varnames": ["n", "__return__"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f4", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f6", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 6, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f4", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f6", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 6, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f4", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f6", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 6, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 5, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f4", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f6", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 6, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 5, "event": "return", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f4", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 4, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f6", "is_zombie": false, "encoded_locals": {"n": 2, "__return__": 1}, "is_highlighted": true, "frame_id": 6, "ordered_varnames": ["n", "__return__"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 2, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f4", "is_zombie": false, "encoded_locals": {"n": 3, "__return__": 1}, "is_highlighted": true, "frame_id": 4, "ordered_varnames": ["n", "__return__"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f2", "is_zombie": false, "encoded_locals": {"n": 4, "__return__": 2}, "is_highlighted": true, "frame_id": 2, "ordered_varnames": ["n", "__return__"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": true, "frame_id": 7, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": true, "frame_id": 7, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": true, "frame_id": 7, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": true, "frame_id": 7, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f8", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 8, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f8", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 8, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f8", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 8, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f8", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 8, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f8", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 8, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f9", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": true, "frame_id": 9, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f8", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 8, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f9", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": true, "frame_id": 9, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 3, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f8", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 8, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f9", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": true, "frame_id": 9, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 3, "event": "return", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f8", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 8, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f9", "is_zombie": false, "encoded_locals": {"n": 1, "__return__": 0}, "is_highlighted": true, "frame_id": 9, "ordered_varnames": ["n", "__return__"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f8", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 8, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f10", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 10, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f8", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 8, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f10", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 10, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f8", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 8, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f10", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 10, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 5, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f8", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 8, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f10", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 10, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 5, "event": "return", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f8", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 8, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f10", "is_zombie": false, "encoded_locals": {"n": 2, "__return__": 1}, "is_highlighted": true, "frame_id": 10, "ordered_varnames": ["n", "__return__"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f8", "is_zombie": false, "encoded_locals": {"n": 3, "__return__": 1}, "is_highlighted": true, "frame_id": 8, "ordered_varnames": ["n", "__return__"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": true, "frame_id": 11, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": true, "frame_id": 11, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": true, "frame_id": 11, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": true, "frame_id": 11, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f12", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 12, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f12", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 12, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f12", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 12, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 5, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f12", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 12, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 5, "event": "return", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f12", "is_zombie": false, "encoded_locals": {"n": 2, "__return__": 1}, "is_highlighted": true, "frame_id": 12, "ordered_varnames": ["n", "__return__"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f13", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 13, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f13", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 13, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f13", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 13, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 7, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f13", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": true, "frame_id": 13, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f13", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 13, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f14", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": true, "frame_id": 14, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f13", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 13, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f14", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": true, "frame_id": 14, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 3, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f13", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 13, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f14", "is_zombie": false, "encoded_locals": {"n": 1}, "is_highlighted": true, "frame_id": 14, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 3, "event": "return", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f13", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 13, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f14", "is_zombie": false, "encoded_locals": {"n": 1, "__return__": 0}, "is_highlighted": true, "frame_id": 14, "ordered_varnames": ["n", "__return__"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 1, "event": "call", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f13", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 13, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f15", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 15, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 2, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f13", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 13, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f15", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 15, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 4, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f13", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 13, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f15", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 15, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 5, "event": "step_line", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f13", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 13, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f15", "is_zombie": false, "encoded_locals": {"n": 2}, "is_highlighted": true, "frame_id": 15, "ordered_varnames": ["n"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 5, "event": "return", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f13", "is_zombie": false, "encoded_locals": {"n": 3}, "is_highlighted": false, "frame_id": 13, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f15", "is_zombie": false, "encoded_locals": {"n": 2, "__return__": 1}, "is_highlighted": true, "frame_id": 15, "ordered_varnames": ["n", "__return__"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4}, "is_highlighted": false, "frame_id": 11, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f13", "is_zombie": false, "encoded_locals": {"n": 3, "__return__": 1}, "is_highlighted": true, "frame_id": 13, "ordered_varnames": ["n", "__return__"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5}, "is_highlighted": false, "frame_id": 7, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f11", "is_zombie": false, "encoded_locals": {"n": 4, "__return__": 2}, "is_highlighted": true, "frame_id": 11, "ordered_varnames": ["n", "__return__"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6}, "is_highlighted": false, "frame_id": 1, "ordered_varnames": ["n"]}, {"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f7", "is_zombie": false, "encoded_locals": {"n": 5, "__return__": 3}, "is_highlighted": true, "frame_id": 7, "ordered_varnames": ["n", "__return__"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 7, "event": "return", "globals": {"fib": ["REF", 1]}, "stack_to_render": [{"parent_frame_id_list": [], "func_name": "fib", "is_parent": false, "unique_hash": "fib_f1", "is_zombie": false, "encoded_locals": {"n": 6, "__return__": 5}, "is_highlighted": true, "frame_id": 1, "ordered_varnames": ["n", "__return__"]}], "func_name": "fib", "ordered_globals": ["fib"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}, {"line": 9, "event": "return", "globals": {"fib": ["REF", 1], "result": 5}, "stack_to_render": [], "func_name": "<module>", "ordered_globals": ["fib", "result"], "heap": {"1": ["FUNCTION", "fib(n)", null]}, "stdout": ""}], "code": "def fib(n):\n    if n == 1:\n        return 0\n    if n == 2:\n        return 1\n    else:\n        return fib(n-2) + fib(n-1)\n\nresult = fib(6)"}</script><p>This recursive definition is tremendously appealing relative to our previous
attempts: it exactly mirrors the familiar definition of Fibonacci numbers.
Consider the pattern of computation that results from evaluating <tt class="docutils literal">fib(6)</tt>,
shown below.  To compute <tt class="docutils literal">fib(6)</tt>, we compute <tt class="docutils literal">fib(5)</tt> and <tt class="docutils literal">fib(4)</tt>.  To
compute <tt class="docutils literal">fib(5)</tt>, we compute <tt class="docutils literal">fib(4)</tt> and <tt class="docutils literal">fib(3)</tt>.  In general, the
evolved process looks like a tree (the diagram below is not a full environment
diagram, but instead a simplified depiction of the process). Each blue dot
indicates a completed computation of a Fibonacci number in the traversal of
this tree.</p>
<div class="figure">
<img alt="" src="../img/fib.png"/>
</div>
<p>Functions that call themselves multiple times in this way are said to be
<em>tree recursive</em>.  This function is instructive as a prototypical tree
recursion, but it is a terribly inefficient way to compute Fibonacci numbers
because it does so much redundant computation. Notice that the entire
computation of <tt class="docutils literal">fib(4)</tt> -- almost half the work -- is duplicated. In fact, it
is not hard to show that the number of times the function will compute
<tt class="docutils literal">fib(1)</tt> or <tt class="docutils literal">fib(2)</tt> (the number of leaves in the tree, in general) is
precisely <tt class="docutils literal">fib(n+1)</tt>. To get an idea of how bad this is, one can show that
the value of <tt class="docutils literal">fib(n)</tt> grows exponentially with <tt class="docutils literal">n</tt>.  <tt class="docutils literal">fib(40)</tt> is
63,245,986! The function above uses a number of steps that grows
exponentially with the input.</p>
<p>We have already seen an iterative implementation of Fibonacci numbers, repeated
here for convenience.</p>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">fib_iter</span><span class="p">(</span><span class="n">n</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""Return the nth Fibonacci number, for n &gt;= 2."""</span>
<span class="gp">    </span>    <span class="n">predecessor</span><span class="p">,</span> <span class="n">current</span> <span class="o">=</span> <span class="mi">0</span><span class="p">,</span> <span class="mi">1</span>   <span class="c"># Fibonacci numbers 1 and 2</span>
<span class="gp">    </span>    <span class="n">k</span> <span class="o">=</span> <span class="mi">2</span>  <span class="c"># Which Fib number is bound to current?</span>
<span class="gp">    </span>    <span class="k">while</span> <span class="n">k</span> <span class="o">&lt;</span> <span class="n">n</span><span class="p">:</span>
<span class="gp">    </span>        <span class="n">predecessor</span><span class="p">,</span> <span class="n">current</span> <span class="o">=</span> <span class="n">current</span><span class="p">,</span> <span class="n">predecessor</span> <span class="o">+</span> <span class="n">current</span>
<span class="gp">    </span>        <span class="n">k</span> <span class="o">=</span> <span class="n">k</span> <span class="o">+</span> <span class="mi">1</span>
<span class="gp">    </span>    <span class="k">return</span> <span class="n">current</span>
</pre></div>

<p>The state that we must maintain in this case consists of the current and
previous Fibonacci numbers, along with the index of the current number.  This
definition does not reflect the standard mathematical definition of Fibonacci
numbers as clearly as the recursive approach.  However, the amount of
computation required in the iterative implementation is only linear in <tt class="docutils literal">n</tt>,
rather than exponential. Even for small values of <tt class="docutils literal">n</tt>, this difference can be
enormous.</p>
<p>One should not conclude from this difference that tree-recursive processes are
useless. When we consider processes that operate on hierarchically structured
data rather than numbers, we will find that tree recursion is a natural and
powerful tool. Furthermore, tree-recursive processes can often be made more
efficient, as we will see in Chapter 3.</p>
</div>
<div class="section" id="example-partitions">
<h3>1.7.5   Example: Partitions</h3>
<p>The number of partitions of a positive integer <tt class="docutils literal">n</tt>, using parts up to size
<tt class="docutils literal">m</tt>, is the number of ways in which <tt class="docutils literal">n</tt> can be expressed as the sum of
positive integer parts up to <tt class="docutils literal">m</tt> in increasing order.  For example, the number
of partitions of 6 using parts up to 4 is 9.</p>
<ol class="arabic simple">
<li><tt class="docutils literal">6 = 2 + 4</tt></li>
<li><tt class="docutils literal">6 = 1 + 1 + 4</tt></li>
<li><tt class="docutils literal">6 = 3 + 3</tt></li>
<li><tt class="docutils literal">6 = 1 + 2 + 3</tt></li>
<li><tt class="docutils literal">6 = 1 + 1 + 1 + 3</tt></li>
<li><tt class="docutils literal">6 = 2 + 2 + 2</tt></li>
<li><tt class="docutils literal">6 = 1 + 1 + 2 + 2</tt></li>
<li><tt class="docutils literal">6 = 1 + 1 + 1 + 1 + 2</tt></li>
<li><tt class="docutils literal">6 = 1 + 1 + 1 + 1 + 1 + 1</tt></li>
</ol>
<p>We will define a function <tt class="docutils literal">count_partitions(n, m)</tt> that returns the number of
different partitions of <tt class="docutils literal">n</tt> using parts up to <tt class="docutils literal">m</tt>.  This function has a
simple solution as a tree-recursive function, based on the following
observation:</p>
<p>The number of ways to partition <tt class="docutils literal">n</tt> using integers up to <tt class="docutils literal">m</tt> equals</p>
<ol class="arabic simple">
<li>the number of ways to partition <tt class="docutils literal"><span class="pre">n-m</span></tt> using integers up to <tt class="docutils literal">m</tt>, and</li>
<li>the number of ways to partition <tt class="docutils literal">n</tt> using integers up to <tt class="docutils literal"><span class="pre">m-1</span></tt>.</li>
</ol>
<p>To see why this is true, observe that all the ways of partitioning <tt class="docutils literal">n</tt> can be
divided into two groups: those that include at least one <tt class="docutils literal">m</tt> and those that do
not.  Moreover, each partition in the first group is a partition of <tt class="docutils literal"><span class="pre">n-m</span></tt>,
followed by <tt class="docutils literal">m</tt> added at the end. In the example above, the first two
partitions contain 4, and the rest do not.</p>
<p>Therefore, we can recursively reduce the problem of partitioning <tt class="docutils literal">n</tt> using
integers up to <tt class="docutils literal">m</tt> into two simpler problems: (1) partition a smaller number
<tt class="docutils literal"><span class="pre">n-m</span></tt>, and (2) partition with smaller components up to <tt class="docutils literal"><span class="pre">m-1</span></tt>.</p>
<p>To complete the implementation, we need to specify the following base cases:</p>
<ol class="arabic simple">
<li>There is one way to partition 0: include no parts.</li>
<li>There are 0 ways to partition a negative <tt class="docutils literal">n</tt>.</li>
<li>There are 0 ways to partition any <tt class="docutils literal">n</tt> greater than 0 using parts of
size 0 or less.</li>
</ol>
<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="k">def</span> <span class="nf">count_partitions</span><span class="p">(</span><span class="n">n</span><span class="p">,</span> <span class="n">m</span><span class="p">):</span>
<span class="gp">    </span>    <span class="sd">"""Count the ways to partition n using parts up to m."""</span>
<span class="gp">    </span>    <span class="k">if</span> <span class="n">n</span> <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
<span class="gp">    </span>        <span class="k">return</span> <span class="mi">1</span>
<span class="gp">    </span>    <span class="k">elif</span> <span class="n">n</span> <span class="o">&lt;</span> <span class="mi">0</span><span class="p">:</span>
<span class="gp">    </span>        <span class="k">return</span> <span class="mi">0</span>
<span class="gp">    </span>    <span class="k">elif</span> <span class="n">m</span>  <span class="o">==</span> <span class="mi">0</span><span class="p">:</span>
<span class="gp">    </span>        <span class="k">return</span> <span class="mi">0</span>
<span class="gp">    </span>    <span class="k">else</span><span class="p">:</span>
<span class="gp">    </span>        <span class="k">return</span> <span class="n">count_partitions</span><span class="p">(</span><span class="n">n</span><span class="o">-</span><span class="n">m</span><span class="p">,</span> <span class="n">m</span><span class="p">)</span> <span class="o">+</span> <span class="n">count_partitions</span><span class="p">(</span><span class="n">n</span><span class="p">,</span> <span class="n">m</span><span class="o">-</span><span class="mi">1</span><span class="p">)</span>
</pre></div>

<div class="highlight"><pre><span class="gp">&gt;&gt;&gt; </span><span class="n">count_partitions</span><span class="p">(</span><span class="mi">6</span><span class="p">,</span> <span class="mi">4</span><span class="p">)</span>
<span class="go">9</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">count_partitions</span><span class="p">(</span><span class="mi">5</span><span class="p">,</span> <span class="mi">5</span><span class="p">)</span>
<span class="go">7</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">count_partitions</span><span class="p">(</span><span class="mi">10</span><span class="p">,</span> <span class="mi">10</span><span class="p">)</span>
<span class="go">42</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">count_partitions</span><span class="p">(</span><span class="mi">15</span><span class="p">,</span> <span class="mi">15</span><span class="p">)</span>
<span class="go">176</span>
<span class="gp">&gt;&gt;&gt; </span><span class="n">count_partitions</span><span class="p">(</span><span class="mi">20</span><span class="p">,</span> <span class="mi">20</span><span class="p">)</span>
<span class="go">627</span>
</pre></div>

<p>We can think of a tree-recursive function as exploring different possibilities.
In this case, we explore the possibility that we use a part of size <tt class="docutils literal">m</tt> and
the possibility that we do not.  The first and second recursive calls correspond
to these possibilities.</p>
<p>Implementing this function without recursion would be substantially more
involved.</p>
</div>
</div>
      </div>
    </section>

    <div class="wrap">
      <footer id="contentinfo" class="body">
          Composing Programs by <a href="http://www.denero.org/">John
          DeNero</a>, based on the textbook <a
          href="http://mitpress.mit.edu/sicp/">Structure and
          Interpretation of Computer Programs</a> by Harold Abelson and
          Gerald Jay Sussman, is licensed under a <a rel="license"
          href="http://creativecommons.org/licenses/by-sa/3.0/">Creative
          Commons Attribution-ShareAlike 3.0 Unported License</a>.
      </footer><!-- /#contentinfo -->
    </div>
  </div>
</body>

<!-- Mirrored from www.composingprograms.com/versions/v1/pages/17-recursive-functions.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:46:00 GMT -->
</html>
