<!DOCTYPE html>
<html lang="en">

<!-- Mirrored from www.composingprograms.com/pages/44-logic-programming.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:45:16 GMT -->
<head>
  <title>4.4 Logic Programming</title>
  <meta charset="utf-8" />

  <link rel="stylesheet" type="text/css" href="../theme/css/cp.css" />

  <!-- Stylesheets -->
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/pytutor.css"/>
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/ui-lightness/jquery-ui-1.8.21.custom.css" />
  <link rel="stylesheet" type="text/css" href="../theme/tutor/css/codemirror.css"  />
  <link rel="stylesheet" type="text/css" href="../theme/coding-js/coding.css"  />

  <!-- jQuery -->
  <script type="text/javascript" src="../theme/tutor/js/jquery-1.8.2.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery-ui-1.8.24.custom.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery.ba-bbq.min.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/jquery.jsPlumb-1.3.10-all-min.js"></script>

  <!-- codemirror.net online code editor -->
  <script type="text/javascript" src="../theme/tutor/js/codemirror/codemirror.js"></script>
  <script type="text/javascript" src="../theme/tutor/js/codemirror/python.js"></script>

  <!-- d3 -->
  <script type="text/javascript" src="../theme/tutor/js/d3.v2.min.js"></script>

  <!-- Online Python Tutor -->
  <script type="text/javascript" src="../theme/tutor/js/pytutor.js"></script>

  <!-- Coding.js -->
  <script type="text/javascript" src="../theme/coding-js/coding.js"></script>
  <script> var $c = new CodingJS("../theme/coding-js/index.html"); </script>

  <!-- Composing Programs -->
  <script type="text/javascript" src="../theme/js/cp.js"></script>

  
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.1/MathJax.js?config=TeX-AMS-MML_HTMLorMML" type= "text/javascript">
       MathJax.Hub.Config({
           config: ["MMLorHTML.js"],
           jax: ["input/TeX","input/MathML","output/HTML-CSS","output/NativeMML"],
           TeX: { extensions: ["AMSmath.js","AMSsymbols.html","noErrors.html","noUndefined.js"], equationNumbers: { autoNumber: "AMS" } },
           extensions: ["tex2jax.js","mml2jax.html","MathMenu.html","MathZoom.html","jsMath2jax.js"],
           tex2jax: {
               inlineMath: [ ['$','$'] ],
               displayMath: [ ['$$','$$'] ],
               processEscapes: true },
           "HTML-CSS": {
               styles: { ".MathJax .mo, .MathJax .mi": {color: "black ! important"}}
           }
       });
    </script>

</head>

<body id="index" class="home">
  <div class="container">

    <div class="nav-main">
      <div class="wrap">
        <a class="nav-home" href="../index.html">
          <span class="nav-logo">c<span class="nav-logo-compose">⚬</span>mp<span class="nav-logo-compose">⚬</span>sing pr<span class="nav-logo-compose">⚬</span>grams</span>
        </a>
        <ul class="nav-site">
          <li><a href="../index.html">Text</a></li>
          <li><a href="../projects.html">Projects</a></li>
          <li><a href="../tutor.html">Tutor</a></li>
          <li><a href="../about.html">About</a></li>
        </ul>
      </div>
    </div>

    <section class="content wrap documentationContent">
      <div class="nav-docs">
	<h3>Chapter 4<a id="hide_contents">Hide contents</a> </h3>
		<div class="nav-docs-section">
			<h3><a href="41-introduction.html">4.1 Introduction</a></h3>
		</div>
		<div class="nav-docs-section">
			<h3><a href="42-implicit-sequences.html">4.2 Implicit Sequences</a></h3>
				<li><a href="42-implicit-sequences.html#iterators">4.2.1 Iterators</a>
				<li><a href="42-implicit-sequences.html#iterables">4.2.2 Iterables</a>
				<li><a href="42-implicit-sequences.html#built-in-iterators">4.2.3 Built-in Iterators</a>
				<li><a href="42-implicit-sequences.html#for-statements">4.2.4 For Statements</a>
				<li><a href="42-implicit-sequences.html#generators-and-yield-statements">4.2.5 Generators and Yield Statements</a>
				<li><a href="42-implicit-sequences.html#iterable-interface">4.2.6 Iterable Interface</a>
				<li><a href="42-implicit-sequences.html#creating-iterables-with-yield">4.2.7 Creating Iterables with Yield</a>
				<li><a href="42-implicit-sequences.html#iterator-interface">4.2.8 Iterator Interface</a>
				<li><a href="42-implicit-sequences.html#streams">4.2.9 Streams</a>
				<li><a href="42-implicit-sequences.html#python-streams">4.2.10 Python Streams</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="43-declarative-programming.html">4.3 Declarative Programming</a></h3>
				<li><a href="43-declarative-programming.html#tables">4.3.1 Tables</a>
				<li><a href="43-declarative-programming.html#select-statements">4.3.2 Select Statements</a>
				<li><a href="43-declarative-programming.html#joins">4.3.3 Joins</a>
				<li><a href="43-declarative-programming.html#interpreting-sql">4.3.4 Interpreting SQL</a>
				<li><a href="43-declarative-programming.html#recursive-select-statements">4.3.5 Recursive Select Statements</a>
				<li><a href="43-declarative-programming.html#aggregation-and-grouping">4.3.6 Aggregation and Grouping</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="44-logic-programming.html">4.4 Logic Programming</a></h3>
				<li><a href="44-logic-programming.html#facts-and-queries">4.4.1 Facts and Queries</a>
				<li><a href="44-logic-programming.html#recursive-facts">4.4.2 Recursive Facts</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="45-unification.html">4.5 Unification</a></h3>
				<li><a href="45-unification.html#pattern-matching">4.5.1 Pattern Matching</a>
				<li><a href="45-unification.html#representing-facts-and-queries">4.5.2 Representing Facts and Queries</a>
				<li><a href="45-unification.html#the-unification-algorithm">4.5.3 The Unification Algorithm</a>
				<li><a href="45-unification.html#proofs">4.5.4 Proofs</a>
				<li><a href="45-unification.html#search">4.5.5 Search</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="46-distributed-computing.html">4.6 Distributed Computing</a></h3>
				<li><a href="46-distributed-computing.html#messages">4.6.1 Messages</a>
				<li><a href="46-distributed-computing.html#client-server-architecture">4.6.2 Client/Server Architecture</a>
				<li><a href="46-distributed-computing.html#peer-to-peer-systems">4.6.3 Peer-to-Peer Systems</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="47-distributed-data-processing.html">4.7 Distributed Data Processing</a></h3>
				<li><a href="47-distributed-data-processing.html#id1">4.7.1 MapReduce</a>
				<li><a href="47-distributed-data-processing.html#local-implementation">4.7.2 Local Implementation</a>
				<li><a href="47-distributed-data-processing.html#distributed-implementation">4.7.3 Distributed Implementation</a>
		</div>
		<div class="nav-docs-section">
			<h3><a href="48-parallel-computing.html">4.8 Parallel Computing</a></h3>
				<li><a href="48-parallel-computing.html#parallelism-in-python">4.8.1 Parallelism in Python</a>
				<li><a href="48-parallel-computing.html#the-problem-with-shared-state">4.8.2 The Problem with Shared State</a>
				<li><a href="48-parallel-computing.html#when-no-synchronization-is-necessary">4.8.3 When No Synchronization is Necessary</a>
				<li><a href="48-parallel-computing.html#synchronized-data-structures">4.8.4 Synchronized Data Structures</a>
				<li><a href="48-parallel-computing.html#locks">4.8.5 Locks</a>
				<li><a href="48-parallel-computing.html#barriers">4.8.6 Barriers</a>
				<li><a href="48-parallel-computing.html#message-passing">4.8.7 Message Passing</a>
				<li><a href="48-parallel-computing.html#synchronization-pitfalls">4.8.8 Synchronization Pitfalls</a>
				<li><a href="48-parallel-computing.html#conclusion">4.8.9 Conclusion</a>
		</div>
      </div>

      <div class="inner-content">
  <div class="section" id="logic-programming">
<h2>4.4   Logic Programming</h2>
<p>In this section, we introduce a declarative query language called <tt class="docutils literal">logic</tt>,
designed specifically for this text.  It is based upon <a class="reference external" href="http://en.wikipedia.org/wiki/Prolog">Prolog</a> and the declarative language in
<a class="reference external" href="http://mitpress.mit.edu/sicp/full-text/book/book-Z-H-29.html#%_sec_4.4.1">Structure and Interpretation of Computer Programs</a>.
Data records are expressed as Scheme lists, and queries are expressed as Scheme
values. The <a class="reference external" href="http://composingprograms.com/examples/logic/logic.py.html">logic</a> interpreter is a complete implementation that depends
upon the Scheme project of the previous chapter.</p>
<div class="section" id="facts-and-queries">
<h3>4.4.1   Facts and Queries</h3>
<p>Databases store records that represent facts in the system.  The
purpose of the query interpreter is to retrieve collections of facts drawn
directly from database records, as well as to deduce new facts from the
database using logical inference. A <tt class="docutils literal">fact</tt> statement in the <tt class="docutils literal">logic</tt>
language consists of one or more lists following the keyword <tt class="docutils literal">fact</tt>.  A
simple fact is a single list. A dog breeder with an interest in U.S. Presidents
might record the genealogy of her collection of dogs using the <tt class="docutils literal">logic</tt>
language as follows:</p>
<pre id="Coding-JS-parent-def">
(fact (parent abraham barack))
(fact (parent abraham clinton))
(fact (parent delano herbert))
(fact (parent fillmore abraham))
(fact (parent fillmore delano))
(fact (parent fillmore grover))
(fact (parent eisenhower fillmore))
</pre>
<script>$c.prompt("Coding-JS-parent-def", [], "logic");</script>
<p>Each fact is not a procedure application, as in a Scheme expression, but
instead a <em>relation</em> that is declared.  "The dog Abraham is the parent of
Barack," declares the first fact. Relation types do not need to be defined in
advance. Relations are not applied, but instead matched to queries.</p>
<p>A query also consists of one or more lists, but begins with the keyword
<tt class="docutils literal">query</tt>. A query may contain variables, which are symbols that begin with a
question mark.  Variables are matched to facts by the query interpreter:</p>
<pre id="Coding-JS-parent-eg">
(query (parent abraham ?child))
</pre>
<script>$c.prompt("Coding-JS-parent-eg", ["Coding-JS-parent-def"], "logic");</script>
<p>The query interpreter responds with <tt class="docutils literal">Success!</tt> to indicate that the query
matches some fact.  The following lines show substitutions of the variable
<tt class="docutils literal"><span class="pre">?child</span></tt> that match the query to the facts in the database.</p>
<p><strong>Compound facts.</strong> Facts may also contain variables as well as multiple
sub-expressions. A multi-expression fact begins with a conclusion, followed by
hypotheses.  For the conclusion to be true, all of the hypotheses must be
satisfied:</p>
<pre id="Coding-JS-fact-syntax">
(fact &lt;conclusion&gt; &lt;hypothesis0&gt; &lt;hypothesis1&gt; ... &lt;hypothesisN&gt;)
</pre>
<script>$c.no_output_frozen_prompt("Coding-JS-fact-syntax", [], "logic");</script>
<p>For example, facts about children can be declared based on the facts about
parents already in the database:</p>
<pre id="Coding-JS-child-def">
(fact (child ?c ?p) (parent ?p ?c))
</pre>
<script>$c.prompt("Coding-JS-child-def", [], "logic");</script>
<p>The fact above can be read as: "<tt class="docutils literal"><span class="pre">?c</span></tt> is the child of <tt class="docutils literal"><span class="pre">?p</span></tt>, provided that
<tt class="docutils literal"><span class="pre">?p</span></tt> is the parent of <tt class="docutils literal"><span class="pre">?c</span></tt>." A query can now refer to this fact:</p>
<pre id="Coding-JS-child-eg-1">
(query (child ?child fillmore))
</pre>
<script>$c.prompt("Coding-JS-child-eg-1", ["Coding-JS-child-def", "Coding-JS-parent-def"], "logic");</script>
<p>The query above requires the query interpreter to combine the fact that defines
<tt class="docutils literal">child</tt> with the various parent facts about <tt class="docutils literal">fillmore</tt>. The user of the
language does not need to know how this information is combined, but only that
the result has a particular form.  It is up to the query interpreter to prove
that <tt class="docutils literal">(child abraham fillmore)</tt> is true, given the available facts.</p>
<p>A query is not required to include variables; it may simply verify a fact:</p>
<pre id="Coding-JS-child-eg-2">
(query (child herbert delano))
</pre>
<script>$c.prompt("Coding-JS-child-eg-2", ["Coding-JS-child-def", "Coding-JS-parent-def"], "logic");</script>
<p>A query that does not match any facts will return failure:</p>
<pre id="Coding-JS-child-eg-3">
(query (child eisenhower ?parent))
</pre>
<script>$c.prompt("Coding-JS-child-eg-3", ["Coding-JS-child-def", "Coding-JS-parent-def"], "logic");</script>
<p><strong>Negation.</strong> We can check if some query does not match any fact by using the
special keyword <tt class="docutils literal">not</tt>:</p>
<pre id="Coding-JS-negation-syntax">
(query (not &lt;relation&gt;))
</pre>
<script>$c.no_output_frozen_prompt("Coding-JS-negation-syntax", [], "logic");</script>
<p>This query succeeds if <tt class="docutils literal">&lt;relation&gt;</tt> fails, and fails if <tt class="docutils literal">&lt;relation&gt;</tt>
succeeds. This idea is known as <em>negation as failure</em>.</p>
<pre id="Coding-JS-negation-eg">
(query (not (parent abraham clinton)))
</pre>
<script>$c.prompt("Coding-JS-negation-eg", ["Coding-JS-parent-def"], "logic");</script>
<pre id="Coding-JS-negation-eg2">
(query (not (parent abraham barack)))
</pre>
<script>$c.prompt("Coding-JS-negation-eg2", ["Coding-JS-parent-def"], "logic");</script>
<p>Sometimes, negation as failure may be counterintuitive to how one might expect
negation to work. Think about the result of the following query:</p>
<pre id="Coding-JS-negation-syntax">
(query (not (parent abraham ?who)))
</pre>
<script>$c.prompt("Coding-JS-negation-syntax", ["Coding-JS-parent-def"], "logic");</script>
<p>Why does this query fail? Surely there are many symbols that could be bound to
<tt class="docutils literal"><span class="pre">?who</span></tt> for which this should hold. However, the steps for negation indicate
that we first inspect the relation <tt class="docutils literal">(parent abraham <span class="pre">?who)</span></tt>. This relation
succeeds, since <tt class="docutils literal"><span class="pre">?who</span></tt> can be bound to either <tt class="docutils literal">barack</tt> or <tt class="docutils literal">clinton</tt>.
Because this relation succeeds, the negation of this relation must fail.</p>
</div>
<div class="section" id="recursive-facts">
<h3>4.4.2   Recursive Facts</h3>
<p>The <tt class="docutils literal">logic</tt> language also allows recursive facts.  That is, the conclusion of
a fact may depend upon a hypothesis that contains the same symbols.  For
instance, the ancestor relation is defined with two facts.  Some <tt class="docutils literal"><span class="pre">?a</span></tt> is an
ancestor of <tt class="docutils literal"><span class="pre">?y</span></tt> if it is a parent of <tt class="docutils literal"><span class="pre">?y</span></tt> or if it is the parent of an
ancestor of <tt class="docutils literal"><span class="pre">?y</span></tt>:</p>
<pre id="Coding-JS-ancestor-def">
(fact (ancestor ?a ?y) (parent ?a ?y))
(fact (ancestor ?a ?y) (parent ?a ?z) (ancestor ?z ?y))
</pre>
<script>$c.prompt("Coding-JS-ancestor-def", ["Coding-JS-parent-def"], "logic");</script>
<p>A single query can then list all ancestors of <tt class="docutils literal">herbert</tt>:</p>
<pre id="Coding-JS-ancestor-eg-1">
(query (ancestor ?a herbert))
</pre>
<script>$c.prompt("Coding-JS-ancestor-eg-1", ["Coding-JS-ancestor-def"], "logic");</script>
<p><strong>Compound queries.</strong> A query may have multiple subexpressions, in which case
all must be satisfied simultaneously by an assignment of symbols to variables.
If a variable appears more than once in a query, then it must take the same
value in each context.  The following query finds ancestors of both <tt class="docutils literal">herbert</tt>
and <tt class="docutils literal">barack</tt>:</p>
<pre id="Coding-JS-ancestor-eg-2">
(query (ancestor ?a barack) (ancestor ?a herbert))
</pre>
<script>$c.prompt("Coding-JS-ancestor-eg-2", ["Coding-JS-ancestor-def"], "logic");</script>
<p>Recursive facts may require long chains of inference to match queries to
existing facts in a database. For instance, to prove the fact <tt class="docutils literal">(ancestor
fillmore herbert)</tt>, we must prove each of the following facts in
succession:</p>
<pre class="literal-block">
(parent delano herbert)       ; (1), a simple fact
(ancestor delano herbert)     ; (2), from (1) and the 1st ancestor fact
(parent fillmore delano)      ; (3), a simple fact
(ancestor fillmore herbert)   ; (4), from (2), (3), &amp; the 2nd ancestor fact
</pre>
<p>In this way, a single fact can imply a large number of additional facts, or
even infinitely many, as long as the query interpreter is able to discover them.</p>
<p><strong>Hierarchical facts.</strong> Thus far, each fact and query expression has been a
list of symbols.  In addition, fact and query lists can contain lists,
providing a way to represent hierarchical data.  The color of each dog may be
stored along with the name an additional record:</p>
<pre id="Coding-JS-dog-def">
(fact (dog (name abraham) (color white)))
(fact (dog (name barack) (color tan)))
(fact (dog (name clinton) (color white)))
(fact (dog (name delano) (color white)))
(fact (dog (name eisenhower) (color tan)))
(fact (dog (name fillmore) (color brown)))
(fact (dog (name grover) (color tan)))
(fact (dog (name herbert) (color brown)))
</pre>
<script>$c.prompt("Coding-JS-dog-def", [], "logic");</script>
<p>Queries can articulate the full structure of hierarchical facts, or they can
match variables to whole lists:</p>
<pre id="Coding-JS-dog-eg-1">
(query (dog (name clinton) (color ?color)))
</pre>
<script>$c.prompt("Coding-JS-dog-eg-1", ["Coding-JS-dog-def"], "logic");</script>
<pre id="Coding-JS-dog-eg-2">
(query (dog (name clinton) ?info))
</pre>
<script>$c.prompt("Coding-JS-dog-eg-2", ["Coding-JS-dog-def"], "logic");</script>
<p>Much of the power of a database lies in the ability of the query interpreter to
join together multiple kinds of facts in a single query.  The following query
finds all pairs of dogs for which one is the ancestor of the other and they
share a color:</p>
<pre id="Coding-JS-dog-ancestor-eg">
(query (dog (name ?name) (color ?color))
       (ancestor ?ancestor ?name)
       (dog (name ?ancestor) (color ?color)))
</pre>
<script>$c.prompt("Coding-JS-dog-ancestor-eg", ["Coding-JS-dog-def", "Coding-JS-ancestor-def"], "logic");</script>
<p>Variables can refer to lists in hierarchical records, but also using dot
notation.  A variable following a dot matches the rest of the list of a fact.
Dotted lists can appear in either facts or queries. The following example
constructs pedigrees of dogs by listing their chain of ancestry.  Young
<tt class="docutils literal">barack</tt> follows a venerable line of presidential pups:</p>
<pre id="Coding-JS-dog-pedigree-def">
(fact (pedigree ?name) (dog (name ?name) . ?details))
(fact (pedigree ?child ?parent . ?rest)
      (parent ?parent ?child)
      (pedigree ?parent . ?rest))
</pre>
<script>$c.prompt("Coding-JS-dog-pedigree-def", ["Coding-JS-dog-def", "Coding-JS-parent-def"], "logic");</script>
<pre id="Coding-JS-dog-pedigree-eg">
(query (pedigree barack . ?lineage))
</pre>
<script>$c.prompt("Coding-JS-dog-pedigree-eg", ["Coding-JS-dog-pedigree-def"], "logic");</script>
<p>Declarative or logical programming can express relationships among facts with
remarkable efficiency. For example, if we wish to express that two lists can
append to form a longer list with the elements of the first, followed by the
elements of the second, we state two rules.  First, a base case declares that
appending an empty list to any list gives that list:</p>
<pre id="Coding-JS-atf-base">
(fact (append-to-form () ?x ?x))
</pre>
<script>$c.prompt("Coding-JS-atf-base", [], "logic");</script>
<p>Second, a recursive fact declares that a list with first element <tt class="docutils literal"><span class="pre">?a</span></tt> and
rest <tt class="docutils literal"><span class="pre">?r</span></tt> appends to a list <tt class="docutils literal"><span class="pre">?y</span></tt> to form a list with first element <tt class="docutils literal"><span class="pre">?a</span></tt>
and some appended rest <tt class="docutils literal"><span class="pre">?z</span></tt>.  For this relation to hold, it must be the case
that <tt class="docutils literal"><span class="pre">?r</span></tt> and <tt class="docutils literal"><span class="pre">?y</span></tt> append to form <tt class="docutils literal"><span class="pre">?z</span></tt>:</p>
<pre id="Coding-JS-atf-recur">
(fact (append-to-form (?a . ?r) ?y (?a . ?z)) (append-to-form ?r ?y ?z))
</pre>
<script>$c.prompt("Coding-JS-atf-recur", [], "logic");</script>
<p>Using these two facts, the query interpreter can compute the result of appending any
two lists together:</p>
<pre id="Coding-JS-atf-eg-1">
(query (append-to-form (a b c) (d e) ?result))
</pre>
<script>$c.prompt("Coding-JS-atf-eg-1", ["Coding-JS-atf-base", "Coding-JS-atf-recur"], "logic");</script>
<p>In addition, it can compute all possible pairs of lists <tt class="docutils literal"><span class="pre">?left</span></tt> and <tt class="docutils literal"><span class="pre">?right</span></tt>
that can append to form the list <tt class="docutils literal">(a b c d e)</tt>:</p>
<pre id="Coding-JS-atf-eg-2">
(query (append-to-form ?left ?right (a b c d e)))
</pre>
<script>$c.prompt("Coding-JS-atf-eg-2", ["Coding-JS-atf-base", "Coding-JS-atf-recur"], "logic");</script>
<p>Although it may appear that our query interpreter is quite intelligent, we will
see that it finds these combinations through one simple operation repeated many
times: that of matching two lists that contain variables in an environment.</p>
</div>
</div>
  <p><i>Continue</i>:
  	<a href="45-unification.html">
  		4.5 Unification
  	</a>
      </div>
    </section>

    <div class="wrap">
      <footer id="contentinfo" class="body">
          Composing Programs by <a href="http://www.denero.org/">John
          DeNero</a>, based on the textbook <a
          href="http://mitpress.mit.edu/sicp/">Structure and
          Interpretation of Computer Programs</a> by Harold Abelson and
          Gerald Jay Sussman, is licensed under a <a rel="license"
          href="http://creativecommons.org/licenses/by-sa/3.0/">Creative
          Commons Attribution-ShareAlike 3.0 Unported License</a>.
      </footer><!-- /#contentinfo -->
    </div>
  </div>
</body>

<!-- Mirrored from www.composingprograms.com/pages/44-logic-programming.html by HTTrack Website Copier/3.x [XR&CO'2014], Sun, 13 Jul 2025 10:45:16 GMT -->
</html>