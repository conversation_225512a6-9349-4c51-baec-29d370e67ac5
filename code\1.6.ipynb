{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 第1.6章 高阶函数学习笔记\\n\n", "\\n\n", "## 核心概念\\n\n", "\\n\n", "1. **函数作为参数**：函数可以接受其他函数作为参数\\n\n", "2. **函数作为通用方法**：通过将函数作为参数传递，可以创建通用的计算方法\\n\n", "3. **嵌套定义**：在函数内部定义函数，利用词法作用域\\n\n", "4. **函数作为返回值**：函数可以返回其他函数\\n\n", "5. **柯里化**：将多参数函数转换为单参数函数序列的技术\\n\n", "6. **Lambda表达式**：创建匿名函数的简洁方式\\n\n", "7. **一等函数**：函数可以像其他数据一样被传递、返回和存储\\n\n", "8. **函数装饰器**：一种特殊的语法，用于修改函数的行为"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.6.1 函数作为参数\\n\n", "\\n\n", "通过将函数作为参数传递，我们可以创建通用的计算模式。\\n\n", "\\n\n", "示例：使用高阶函数计算各种数列的和"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def summation(n, term):\\n\n", "    total, k = 0, 1\\n\n", "    while k <= n:\\n\n", "        total, k = total + term(k), k + 1\\n\n", "    return total\\n\n", "\\n\n", "def cube(x):\\n\n", "    return x*x*x\\n\n", "\\n\n", "def sum_cubes(n):\\n\n", "    return summation(n, cube)\\n\n", "\\n\n", "result = sum_cubes(3)\\n\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["使用identity函数计算自然数之和："]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "SyntaxError", "evalue": "unexpected character after line continuation character (*********.py, line 1)", "output_type": "error", "traceback": ["  \u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31mdef identity(x):\\n\u001b[39m\n                     ^\n\u001b[31mSyntaxError\u001b[39m\u001b[31m:\u001b[39m unexpected character after line continuation character\n"]}], "source": ["def identity(x):\\n\n", "    return x\\n\n", "\\n\n", "def sum_naturals(n):\\n\n", "    return summation(n, identity)\\n\n", "\\n\n", "result = sum_naturals(10)\\n\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.6.2 函数作为通用方法\\n\n", "\\n\n", "通过将函数作为参数传递，我们可以创建通用的计算方法。\\n\n", "\\n\n", "示例：使用迭代改进算法计算黄金比例"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def improve(update, close, guess=1):\\n\n", "    while not close(guess):\\n\n", "        guess = update(guess)\\n\n", "    return guess\\n\n", "\\n\n", "def golden_update(guess):\\n\n", "    return 1/guess + 1\\n\n", "\\n\n", "def square_close_to_successor(guess):\\n\n", "    return approx_eq(guess * guess, guess + 1)\\n\n", "\\n\n", "def approx_eq(x, y, tolerance=1e-3):\\n\n", "    return abs(x - y) < tolerance\\n\n", "\\n\n", "phi = improve(golden_update, square_close_to_successor)\\n\n", "print(phi)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.6.3 嵌套定义\\n\n", "\\n\n", "在函数内部定义函数，利用词法作用域。\\n\n", "\\n\n", "示例：计算平方根的函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def average(x, y):\\n\n", "    return (x + y)/2\\n\n", "\\n\n", "def improve(update, close, guess=1):\\n\n", "    while not close(guess):\\n\n", "        guess = update(guess)\\n\n", "    return guess\\n\n", "\\n\n", "def approx_eq(x, y, tolerance=1e-3):\\n\n", "    return abs(x - y) < tolerance\\n\n", "\\n\n", "def sqrt(a):\\n\n", "    def sqrt_update(x):\\n\n", "        return average(x, a/x)\\n\n", "    def sqrt_close(x):\\n\n", "        return approx_eq(x * x, a)\\n\n", "    return improve(sqrt_update, sqrt_close)\\n\n", "\\n\n", "result = sqrt(256)\\n\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.6.4 函数作为返回值\\n\n", "\\n\n", "函数可以返回其他函数，这使得我们可以创建更灵活的计算模式。\\n\n", "\\n\n", "示例：函数组合"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def square(x):\\n\n", "    return x * x\\n\n", "\\n\n", "def successor(x):\\n\n", "    return x + 1\\n\n", "\\n\n", "def compose1(f, g):\\n\n", "    def h(x):\\n\n", "        return f(g(x))\\n\n", "    return h\\n\n", "\\n\n", "def f(x):\\n\n", "    \\\"\\\"\\\"Never called.\\\"\\\"\\\"\\n\n", "    return -x\\n\n", "\\n\n", "square_successor = compose1(square, successor)\\n\n", "result = square_successor(12)\\n\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.6.5 示例：牛顿法\\n\n", "\\n\n", "使用牛顿法计算函数的零点。\\n\n", "\\n\n", "示例：计算平方根和n次方根"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def newton_update(f, df):\\n\n", "    def update(x):\\n\n", "        return x - f(x) / df(x)\\n\n", "    return update\\n\n", "\\n\n", "def improve(update, close, guess=1):\\n\n", "    while not close(guess):\\n\n", "        guess = update(guess)\\n\n", "    return guess\\n\n", "\\n\n", "def approx_eq(x, y, tolerance=1e-3):\\n\n", "    return abs(x - y) < tolerance\\n\n", "\\n\n", "def find_zero(f, df):\\n\n", "    def near_zero(x):\\n\n", "        return approx_eq(f(x), 0)\\n\n", "    return improve(newton_update(f, df), near_zero)\\n\n", "\\n\n", "def square_root_newton(a):\\n\n", "    def f(x):\\n\n", "        return x * x - a\\n\n", "    def df(x):\\n\n", "        return 2 * x\\n\n", "    return find_zero(f, df)\\n\n", "\\n\n", "print(square_root_newton(64))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def power(x, n):\\n\n", "    \\\"\\\"\\\"Return x * x * x * ... * x for x repeated n times.\\\"\\\"\\\"\\n\n", "    product, k = 1, 0\\n\n", "    while k < n:\\n\n", "        product, k = product * x, k + 1\\n\n", "    return product\\n\n", "\\n\n", "def nth_root_of_a(n, a):\\n\n", "    def f(x):\\n\n", "        return power(x, n) - a\\n\n", "    def df(x):\\n\n", "        return n * power(x, n-1)\\n\n", "    return find_zero(f, df)\\n\n", "\\n\n", "print(nth_root_of_a(2, 64))\\n\n", "print(nth_root_of_a(3, 64))\\n\n", "print(nth_root_of_a(6, 64))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.6.6 柯里化\\n\n", "\\n\n", "将多参数函数转换为单参数函数序列的技术。\\n\n", "\\n\n", "示例：柯里化pow函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def curried_pow(x):\\n\n", "    def h(y):\\n\n", "        return pow(x, y)\\n\n", "    return h\\n\n", "\\n\n", "print(curried_pow(2)(3))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def map_to_range(start, end, f):\\n\n", "    while start < end:\\n\n", "        print(f(start))\\n\n", "        start = start + 1\\n\n", "\\n\n", "map_to_range(0, 10, curried_pow(2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def curry2(f):\\n\n", "    \\\"\\\"\\\"Return a curried version of the given two-argument function.\\\"\\\"\\\"\\n\n", "    def g(x):\\n\n", "        def h(y):\\n\n", "            return f(x, y)\\n\n", "        return h\\n\n", "    return g\\n\n", "\\n\n", "def uncurry2(g):\\n\n", "    \\\"\\\"\\\"Return a two-argument version of the given curried function.\\\"\\\"\\\"\\n\n", "    def f(x, y):\\n\n", "        return g(x)(y)\\n\n", "    return f\\n\n", "\\n\n", "pow_curried = curry2(pow)\\n\n", "print(pow_curried(2)(5))\\n\n", "map_to_range(0, 10, pow_curried(2))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.6.7 Lambda表达式\\n\n", "\\n\n", "创建匿名函数的简洁方式。\\n\n", "\\n\n", "示例：使用lambda表达式简化函数定义"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compose1(f, g):\\n\n", "    return lambda x: f(g(x))\\n\n", "\\n\n", "s = lambda x: x * x\\n\n", "print(s)\\n\n", "print(s(12))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def compose1(f, g):\\n\n", "    return lambda x: f(g(x))\\n\n", "\\n\n", "f = compose1(lambda x: x * x,\\n\n", "             lambda y: y + 1)\\n\n", "result = f(12)\\n\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.6.9 函数装饰器\\n\n", "\\n\n", "一种特殊的语法，用于修改函数的行为。\\n\n", "\\n\n", "示例：使用装饰器跟踪函数调用"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def trace(fn):\\n\n", "    def wrapped(x):\\n\n", "        print('-> ', fn, '(', x, ')')\\n\n", "        return fn(x)\\n\n", "    return wrapped\\n\n", "\\n\n", "@trace\\n\n", "def triple(x):\\n\n", "    return 3 * x\\n\n", "\\n\n", "print(triple(12))"]}], "metadata": {"kernelspec": {"display_name": "ml", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}